<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xcloud.ec</groupId>
        <artifactId>xcloud-ec</artifactId>
        <version>2.7.1.AG.RELEASE</version>
    </parent>
    <artifactId>xcloud-ec-foundation-iaas</artifactId>
    <dependencies>
        <!-- 基础框架/组件依赖 -->
        <dependency>
            <groupId>com.aiya.platform</groupId>
            <artifactId>platform-asynctask</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>platform-foundation</artifactId>
                    <groupId>com.aiya.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>platform-orm</artifactId>
                    <groupId>com.aiya.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>platform-orm-mongodb</artifactId>
                    <groupId>com.aiya.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aiya.platform</groupId>
            <artifactId>platform-orm-influxdb</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>platform-foundation</artifactId>
                    <groupId>com.aiya.platform</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aiya.platform</groupId>
            <artifactId>platform-orm</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aiya.platform</groupId>
            <artifactId>platform-foundation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>netty</artifactId>
                    <groupId>io.netty</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aiya.platform</groupId>
            <artifactId>platform-trace</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aiya.kpy.support</groupId>
            <artifactId>kpy-support-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xcloud.ec.sdk</groupId>
            <artifactId>xcloud-ec-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>platform-foundation</artifactId>
                    <groupId>com.aiya.platform</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>kpy-support-common</artifactId>
                    <groupId>com.aiya.kpy.support</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--移动云引入相关依赖-->
        <dependency>
            <groupId>com.ecloud.sdk</groupId>
            <artifactId>ecloud-sdk-core</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.10.1</version>
        </dependency>
        <dependency>
            <groupId>org.threeten</groupId>
            <artifactId>threetenbp</artifactId>
            <version>1.6.8</version>
        </dependency>
        <dependency>
            <groupId>io.gsonfire</groupId>
            <artifactId>gson-fire</artifactId>
            <version>1.8.5</version>
        </dependency>
        <!--移动云引入相关依赖-->

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
        </dependency>

        <!-- 其他第三方依赖 -->
        <dependency>
            <groupId>com.squareup.okio</groupId>
            <artifactId>okio</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>xcloud-ec-foundation-iaas</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
