package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Getter
@AllArgsConstructor
public enum BackupIntervalEnum {
    SUN("SUN"),
    MON("MON"),
    TUE("TUE"),
    WED("WED"),
    THU("THU"),
    FRI("FRI"),
    SAT("SAT");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
