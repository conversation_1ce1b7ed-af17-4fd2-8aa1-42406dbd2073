package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Getter
@AllArgsConstructor
public enum DiskTypeEnum {
    /**
     * 容量型
     */
    CAPEBS("capebs"),
    /**
     * 性能优化型
     */
    EBS("ebs"),
    /**
     * 高性能型
     */
    SSD("ssd"),
    EDGESSD("edgessd"),
    /**
     * 极速型-L2云硬盘
     */
    ESSDL2("essdl2"),
    /**
     * 极速型-L3云硬盘
     */
    ESSDL3("essdl3"),
    SSDEBS("ssdebs"),
    EDGESSDEBS("edgessdebs"),
    EDGECAPACITY("edgecapacity"),
    SSDIO("ssdio"),
    HDD("hdd"),
    CAPEBSYC("capebsyc"),
    SSDYC("ssdyc"),
    SSDEBSYC("ssdebsyc");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
