package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmRenewBody extends Body {
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("billingType")
    private String billingType;
    @SerializedName("serverId")
    private String serverId;
}
