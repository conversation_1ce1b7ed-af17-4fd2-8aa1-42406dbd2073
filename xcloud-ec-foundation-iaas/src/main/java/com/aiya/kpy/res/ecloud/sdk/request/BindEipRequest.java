package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.BindTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipBindBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipBindQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipBindRequest;
import com.aiya.kpy.res.ecloud.sdk.response.BindEipResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BindEipRequest extends ECloudAcsRequest<BindEipResponse> {
    @ApiModelProperty("asyncType")
    private String asyncType;

    @ApiModelProperty("弹性ipid")
    @NotNull(message = "弹性ipid不能为空")
    private String ipId;

    @ApiModelProperty("端口id")
    private String portId;

    @ApiModelProperty("资源id")
    @NotNull(message = "资源id不能为空")
    private String resourceId;

    @ApiModelProperty("IP绑定类型")
    @NotNull(message = "IP绑定类型不能为空")
    private BindTypeEnum type;

    @ApiModelProperty("虚拟可用区资源池选择")
    private String vpoolId;
    @ApiModelProperty("虚拟可用区资源池选择")
    private String vPoolId;

    @ApiModelProperty("虚拟可用区选择")
    private String vaz;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("floatingIpBind")
                .uri("/acl/v3/floatingip/bind")
                .gatewayUri("/api/openapi-eip/acl/v3/floatingip/bind")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipBindRequest toRequest() {
        EipBindQuery query = EipBindQuery.builder().asyncType(this.asyncType).build();
        EipBindBody body = EipBindBody.builder()
                .resourceId(this.resourceId)
                .ipId(this.ipId)
                .vpoolId(this.vpoolId)
                .portId(this.portId)
                .type(this.type)
                .vPoolId(this.vPoolId)
                .vaz(this.vaz)
                .build();
        return EipBindRequest.builder()
                .floatingIpBindQuery(query)
                .floatingIpBindBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return BindEipResponse.class;
    }
}
