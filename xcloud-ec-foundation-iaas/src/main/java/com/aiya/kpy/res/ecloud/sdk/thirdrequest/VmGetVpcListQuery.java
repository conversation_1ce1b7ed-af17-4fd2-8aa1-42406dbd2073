package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ScaleEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmGetVpcListQuery extends Query {
    @SerializedName("visible")
    private Boolean visible;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("scale")
    private ScaleEnum scale;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("page")
    private Integer page;
    @SerializedName("region")
    private String region;
    @SerializedName("natGatewayBind")
    private Boolean natGatewayBind;
}
