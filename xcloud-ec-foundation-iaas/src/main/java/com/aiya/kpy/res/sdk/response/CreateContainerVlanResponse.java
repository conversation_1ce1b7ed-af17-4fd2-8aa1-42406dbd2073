package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.ContainerVlan;

/**
 * 创建容器VLAN响应
 *
 * @Author: liuying<PERSON>e
 * @Date: 2025/1/24
 * @Description: 创建容器VLAN操作的响应类，包含请求ID、主机ID和VLAN信息，基于container.swagger.json生成
 */
public class CreateContainerVlanResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private ContainerVlan info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public ContainerVlan getInfo() {
        return info;
    }

    public void setInfo(ContainerVlan info) {
        this.info = info;
    }
}
