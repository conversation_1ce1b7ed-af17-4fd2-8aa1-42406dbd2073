package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.RegionComponentEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetRegionQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetRegionRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetRegionResponseContent;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRegionRequest extends ECloudAcsRequest<GetRegionResponseContent> {
    @ApiModelProperty("组件")
    private RegionComponentEnum component;

    public Params getParams() {
        return Params.builder()
                .action("vmGetRegion")
                .uri("/api/v2/region")
                .gatewayUri("/api/v2/region")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    public VmGetRegionRequest toRequest() {
        return VmGetRegionRequest.builder()
                .vmGetRegionQuery(VmGetRegionQuery.builder()
                        .component(this.component == null ? null : this.component.getValue())
                        .build())
                .build();
    }

    public Class getResponseClass() {
        return GetRegionResponseContent.class;
    }
}
