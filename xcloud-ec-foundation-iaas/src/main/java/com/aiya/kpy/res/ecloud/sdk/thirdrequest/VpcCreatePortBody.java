package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class VpcCreatePortBody extends Body {
    @SerializedName("portSumNum")
    private Integer portSumNum;
    @SerializedName("sgIds")
    private List<String> sgIds;
    @SerializedName("edge")
    private Boolean edge;
    @SerializedName("macAddress")
    private String macAddress;
    @SerializedName("bindingHostId")
    private String bindingHostId;
    @SerializedName("name")
    private String name;
    @SerializedName("usagetype")
    private ResourceTypeEnum usagetype;
    @SerializedName("networkId")
    private String networkId;
    @SerializedName("region")
    private String region;
    @SerializedName("type")
    private PortTypeEnum type;
    @SerializedName("ips")
    private List<VpcCreatePortRequestIps> ips;
}
