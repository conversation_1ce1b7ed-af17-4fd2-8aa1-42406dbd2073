package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.DiskBackupTypeEnum;
import com.aiya.kpy.res.ecloud.enums.DiskTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ServerTypeEnum;
import com.aiya.kpy.res.ecloud.enums.SnapshotTypeEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetDiskConfigListResponseContent implements ECloudResponseBody {
    @ApiModelProperty("是否ISCSI属性")
    private Boolean iscsi;

    @ApiModelProperty("硬盘cinder类型")
    private String cinderType;

    @ApiModelProperty("底层快照接口类型")
    private SnapshotTypeEnum snapshotType;

    @ApiModelProperty("是否支持加密功能")
    private Boolean encryption;

    @ApiModelProperty("OP侧硬盘类型")
    private DiskTypeEnum opType;

    @ApiModelProperty("是否支持自定义备份")
    private Boolean customBack;

    @ApiModelProperty("可挂载主机类型")
    private List<ServerTypeEnum> attachServerTypes;

    @ApiModelProperty("优先级")
    private Integer priority;

    @ApiModelProperty("底层可用区")
    private String region;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("底层备份类型")
    private DiskBackupTypeEnum backupType;
}
