package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.DescribeContainerInstancesResponse;

/**
 * 查询容器实例列表请求
 *
 * 用于容器云平台查询容器实例列表的API请求类。支持分页查询、
 * 条件过滤，可根据容器状态、规格、IP地址等条件筛选容器实例。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class DescribeContainerInstancesRequest extends KpyRpcAcsRequest<DescribeContainerInstancesResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private Integer pageNumber;
    private Integer pageSize;
    private String flavorUuid;
    private String uuid;
    private Integer state;
    private String ip;
    private Integer userId;

    public DescribeContainerInstancesRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "DescribeContainerInstances");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "PageNumber", description = "页码")
    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    @KpyParam(value = "PageSize", description = "每页大小")
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @KpyParam(value = "FlavorUuid", description = "规格UUID")
    public String getFlavorUuid() {
        return flavorUuid;
    }

    public void setFlavorUuid(String flavorUuid) {
        this.flavorUuid = flavorUuid;
    }

    @KpyParam(value = "Uuid", description = "容器UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @KpyParam(value = "State", description = "容器状态(0处理中 1正常 2关机)")
    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    @KpyParam(value = "Ip", description = "IP")
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    @KpyParam(value = "UserId", description = "用户ID")
    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public Class<DescribeContainerInstancesResponse> getResponseClass() {
        return DescribeContainerInstancesResponse.class;
    }
}
