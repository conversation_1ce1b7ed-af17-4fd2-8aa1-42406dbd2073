package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetPortListResponseIp {
    @SerializedName("subnetId")
    private String subnetId;
    @SerializedName("ipv6ShareBandwidthId")
    private String ipv6ShareBandwidthId;
    @SerializedName("resourceId")
    private String resourceId;
    @SerializedName("vpcName")
    private String vpcName;
    @SerializedName("proposer")
    private String proposer;
    @SerializedName("ipAddress")
    private String ipAddress;
    @SerializedName("ipv6BandwidthSize")
    private Integer ipv6BandwidthSize;
    @SerializedName("resourceName")
    private String resourceName;
    @SerializedName("portName")
    private String portName;
    @SerializedName("portId")
    private String portId;
    @SerializedName("chargeModeEnum")
    private ChargeModeEnum chargeModeEnum;
    @SerializedName("subnetName")
    private String subnetName;
    @SerializedName("vaz")
    private String vaz;
    @SerializedName("subnetCidr")
    private String subnetCidr;
    @SerializedName("ipVersion")
    private VersionsEnum ipVersion;
    @SerializedName("createdBy")
    private String createdBy;
    @SerializedName("sharedIpOperate")
    private Boolean sharedIpOperate;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("vpcId")
    private String vpcId;
    @SerializedName("customerId")
    private String customerId;
    @SerializedName("createdTime")
    private String createdTime;
    @SerializedName("adminStateUp")
    private Boolean adminStateUp;
    @SerializedName("nsQosPolicyId")
    private String nsQosPolicyId;
    @SerializedName("resourceType")
    private PortTypeEnum resourceType;
}
