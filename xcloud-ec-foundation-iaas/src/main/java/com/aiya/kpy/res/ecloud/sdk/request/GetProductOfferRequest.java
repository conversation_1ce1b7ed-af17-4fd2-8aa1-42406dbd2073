package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetProductOfferResponseContent;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
public class GetProductOfferRequest extends ECloudAcsRequest<GetProductOfferResponseContent> {
    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmgetProductOfferIds")
                .uri("/acl/v3/server/products")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/products")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .build();
    }

    @Override
    public Object toRequest() {
        return null;
    }

    @Override
    public Class getResponseClass() {
        return GetProductOfferResponseContent.class;
    }
}
