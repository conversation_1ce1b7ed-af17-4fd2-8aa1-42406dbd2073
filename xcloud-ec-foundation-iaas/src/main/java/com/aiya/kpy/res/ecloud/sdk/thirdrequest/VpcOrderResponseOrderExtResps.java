package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class VpcOrderResponseOrderExtResps {
    @ApiModelProperty("订单项ID")
    private String orderExtId;

    @ApiModelProperty("订单项状态，取值包括： 3：等待开通。 4：正在开通。 5：开通待同步。 6：开通成功。 7：开通失败。")
    private Integer orderExtStatus;

    @ApiModelProperty("产品类型，用于表示该订单项所属的产品类型，VPC订购取值为router")
    private EipProductTypeEnum productType;
}
