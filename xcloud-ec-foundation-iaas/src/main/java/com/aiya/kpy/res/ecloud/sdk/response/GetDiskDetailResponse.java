package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.ec.api.enums.DiskStatus;
import com.aiya.kpy.ec.foundation.iaas.model.DiskModel;
import com.aiya.kpy.ec.foundation.iaas.utils.DiskStatusUtils;
import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsGetDetailAttachSevers;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.RequestTags;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/24
 */
@Data
public class GetDiskDetailResponse implements ECloudResponseBody {
    @ApiModelProperty("硬盘id")
    private String id;

    @ApiModelProperty("硬盘名称")
    private String name;

    @ApiModelProperty("硬盘描述")
    private String description;

    @ApiModelProperty("硬盘状态")
    private String status;

    @ApiModelProperty("克隆时的源卷ID")
    private String sourceVolumeId;

    @ApiModelProperty("备份ID")
    private String backupId;

    @ApiModelProperty("硬盘大小")
    private Integer size;

    @ApiModelProperty("硬盘cinder类型")
    private String volumeType;

    @ApiModelProperty("硬盘所在集群的ID")
    private String metadata;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("是否被删除")
    private Boolean isDelete;

    @ApiModelProperty("当前操作状态")
    private OperationFlagEnum operationFlag;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("快照ID")
    private String snapshotId;

    @ApiModelProperty("硬盘op侧类型")
    private DiskTypeEnum type;

    @ApiModelProperty("是否共享")
    private Boolean isShare;

    @ApiModelProperty("硬盘挂载主机Id列表")
    private List<String> serverIds;

    @ApiModelProperty("挂载的主机")
    private List<EbsGetDetailAttachSevers> attachSevers;

    @ApiModelProperty("硬盘可挂载主机类型")
    private List<ServerTypeEnum> attachServerTypes;

    @ApiModelProperty("产品类型")
    private ProductTypeEnum productType;

    @ApiModelProperty("是否支持ISCSI")
    private Boolean iscsi;

    @ApiModelProperty("底层可用区")
    private String region;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("订购用户名称")
    private String userName;

    @ApiModelProperty("是否放入回收站")
    private Boolean recycle;

    @ApiModelProperty("回收时间")
    private String recycleTime;

    @ApiModelProperty("创建来源")
    private CreateSourceEnum createSource;

    @ApiModelProperty("计费模式，值为year,month,hour")
    private String measure;

    @ApiModelProperty("密钥id")
    private String keyId;

    @ApiModelProperty("是否支持加密功能")
    private Boolean encryption;

    @ApiModelProperty("内循环账号id")
    private String iauserId;

    @ApiModelProperty("备份恢复至硬盘进度（0~100)")
    private Integer backupRestoreProcess;

    @ApiModelProperty("标签")
    private List<RequestTags> tags;

    @ApiModelProperty("复制对卷类型")
    private DiskReplicationTypeEnum volumeReplicationType;

    public DiskModel toDiskModel() {
        DiskModel model = new DiskModel();
        model.setIaasDiskUUID(id);
        //挂载云主机后,status返回null
        if (CollectionUtils.isNotEmpty(serverIds)) {
            model.setIaasEcsUUID(serverIds.get(0));
            model.setDiskStatus(DiskStatus.AVAILABLE);
        }
        if (status != null) {
            model.setDiskStatus(DiskStatusUtils.convertStatus(DiskStatusEnum.fromCodeOrValue(status)));
        }
        if (Objects.equals(operationFlag, OperationFlagEnum.FROZEN)) {
            model.setDiskStatus(DiskStatus.EXPIRED);
        }
        //model.setDiskType();
        model.setDiskCategory(DiskStatusUtils.converCategory(this.type));
        return model;
    }
}
