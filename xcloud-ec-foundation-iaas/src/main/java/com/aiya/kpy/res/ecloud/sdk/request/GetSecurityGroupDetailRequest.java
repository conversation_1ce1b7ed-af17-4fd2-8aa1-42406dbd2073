package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSecurityGroupDetailResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupDetailRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSecurityGroupDetailRequest extends ECloudAcsRequest<GetSecurityGroupDetailResponse> {
    @ApiModelProperty("安全组ID")
    @NotNull(message = "安全组ID不能为空")
    private String securityGroupId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("getSecurityGroupDetailResp")
                .uri("/customer/v3/SecurityGroup/{securityGroupId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroup/{securityGroupId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetSecurityGroupDetailRequest toRequest() {
        VpcGetSecurityGroupDetailPath path = VpcGetSecurityGroupDetailPath.builder()
                .securityGroupId(securityGroupId)
                .build();
        return VpcGetSecurityGroupDetailRequest.builder()
                .getSecurityGroupDetailRespPath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetSecurityGroupDetailResponse.class;
    }
}
