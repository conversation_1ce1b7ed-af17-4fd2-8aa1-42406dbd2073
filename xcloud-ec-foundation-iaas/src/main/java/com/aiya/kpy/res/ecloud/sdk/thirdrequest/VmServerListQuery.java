package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.*;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmServerListQuery extends Query {
    @SerializedName("unBindBackupPolicy")
    private Boolean unBindBackupPolicy;
    @SerializedName("ecStatus")
    private List<EcStatusEnum> ecStatus;
    @SerializedName("visible")
    private Boolean visible;
    @SerializedName("backupPolicyId")
    private String backupPolicyId;
    @SerializedName("serverTypes")
    private List<ServerTypeEnum> serverTypes;
    @SerializedName("privateIp")
    private String privateIp;
    @SerializedName("imageOsTypes")
    private List<OsTypeEnum> imageOsTypes;
    @SerializedName("keyName")
    private String keyName;
    @SerializedName("serverName")
    private String serverName;
    @SerializedName("cpu")
    private Integer cpu;
    @SerializedName("productTypes")
    private List<ProductTypeEnum> productTypes;
    @SerializedName("publicIp")
    private String publicIp;
    @SerializedName("queryWordName")
    private String queryWordName;
    @SerializedName("serverId")
    private String serverId;
    @SerializedName("hasBackup")
    private Boolean hasBackup;
    @SerializedName("queryWordId")
    private String queryWordId;
    @SerializedName("securityGroupId")
    private String securityGroupId;
    @SerializedName("disk")
    private Integer disk;
    @SerializedName("size")
    private Integer size;
    @SerializedName("page")
    private Integer page;
    @SerializedName("opStatus")
    private OpStatusEnum opStatus;
    @SerializedName("ram")
    private Integer ram;
}
