package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsGetListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsGetListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetDiskListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDiskListRequest extends ECloudAcsRequest<GetDiskListResponse> {
    @ApiModelProperty("硬盘id")
    private String volumeId;

    @ApiModelProperty("模糊查询")
    private String likeSearch;

    @ApiModelProperty("标签id集合")
    private List<String> tagIds;

    @ApiModelProperty("页码")
    private Integer page;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("list")
                .uri("/acl/v3/volume")
                .gatewayUri("/api/ebs/acl/v3/volume")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsGetListRequest toRequest() {
        EbsGetListQuery query = EbsGetListQuery.builder()
                .likeSearch(this.likeSearch)
                .size(this.pageSize)
                .page(this.page)
                .tagIds(this.tagIds)
                .volumeId(this.volumeId)
                .build();
        return EbsGetListRequest.builder().listQuery(query).build();
    }

    @Override
    public Class getResponseClass() {
        return GetDiskListResponse.class;
    }
}
