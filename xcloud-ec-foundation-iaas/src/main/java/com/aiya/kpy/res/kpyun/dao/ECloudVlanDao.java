package com.aiya.kpy.res.kpyun.dao;

import com.aiya.kpy.ec.api.enums.VlanStatus;
import com.aiya.kpy.res.kpyun.entity.ECloudVlanEntity;
import com.aiya.platform.orm.PlatformCommonDAO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/12 14:21
 */
@Repository
public interface ECloudVlanDao extends PlatformCommonDAO<ECloudVlanEntity> {
    @Select("select * from ECLOUD_VLAN where PLATFORM_REGION_CODE = #{platformRegionCode} and PLATFORM_ZONE_CODE = #{platformZoneCode} and STATUS = #{vlanStatus} and DELETE_TIME = 0")
    List<ECloudVlanEntity> listByRegionAndZone(@Param("platformRegionCode") String platformRegionCode, @Param("platformZoneCode") String platformZoneCode, @Param("vlanStatus") VlanStatus vlanStatus);

    List<ECloudVlanEntity> list(@Param("platformRegionCode") String platformRegionCode, @Param("platformZoneCode") String platformZoneCode, @Param("vlanStatus") VlanStatus vlanStatus, @Param("vlanUUID") String... vlanUUID);

    /**
     * 获取指定地域下的最后一个IP(同一VPC下取最大IP进行升序排序，在排序结果中取最小IP)
     * @param platformRegionCode
     * @param platformZoneCode
     * @return
     */
    ECloudVlanEntity getLastIp(@Param("platformRegionCode") String platformRegionCode, @Param("platformZoneCode") String platformZoneCode);

    @Select("select * from ECLOUD_VLAN where THIRD_PARTY_RESOURCE_UUID = #{vlanUUID} AND DELETE_TIME = 0")
    ECloudVlanEntity getByVlanUUID(@Param("vlanUUID") String vlanUUID);

    @Update("update ECLOUD_VLAN set DELETE_TIME = CURRENT_TIMESTAMP where SUBNET_ID = #{subnetId}")
    void deleteBySubnetId(@Param("subnetId") String subnetId);
}
