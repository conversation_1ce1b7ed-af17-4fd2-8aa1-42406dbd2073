package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.aiya.kpy.res.ecloud.enums.ChargePeriodEnum;
import com.aiya.kpy.res.ecloud.enums.IpTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EipOrderCreateBody extends Body {
    @SerializedName("chargePeriodEnum")
    private ChargePeriodEnum chargePeriodEnum;
    @SerializedName("quantity")
    private Integer quantity;
    @SerializedName("tagId")
    private String tagId;
    @SerializedName("bandwidthSize")
    private Integer bandwidthSize;
    @SerializedName("description")
    private String description;
    @SerializedName("chargeModeEnum")
    private ChargeModeEnum chargeModeEnum;
    @SerializedName("floatingIpAddress")
    private String floatingIpAddress;
    @SerializedName("isQueryOrderExts")
    private Boolean isQueryOrderExts;
    @SerializedName("vaz")
    private String vaz;
    @SerializedName("tags")
    private List<RequestTags> tags;
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("bandwidthKbSize")
    private Integer bandwidthKbSize;
    @SerializedName("autoRenew")
    private Boolean autoRenew;
    @SerializedName("ipType")
    private IpTypeEnum ipType;
    @SerializedName("vPoolId")
    private String vPoolId;
}
