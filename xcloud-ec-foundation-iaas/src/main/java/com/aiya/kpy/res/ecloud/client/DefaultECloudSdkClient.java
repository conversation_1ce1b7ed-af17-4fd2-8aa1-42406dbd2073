package com.aiya.kpy.res.ecloud.client;

import com.aiya.kpy.res.ecloud.enums.ECloudProductEnum;
import com.aiya.kpy.res.ecloud.enums.StateEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.response.ECloudSdkResponse;
import com.aiya.kpy.res.ecloud.utils.HibernateValidator;
import com.aiya.kpy.res.kpyun.client.CallOutApiEvent;
import com.alibaba.fastjson.JSON;
import com.ecloud.sdk.ApiClient;
import com.ecloud.sdk.ApiResponse;
import com.ecloud.sdk.Configuration;
import com.ecloud.sdk.HttpRequest;
import com.ecloud.sdk.config.Config;
import com.ecloud.sdk.config.RuntimeConfig;
import com.ecloud.sdk.exception.SdkException;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Component
@Scope("prototype")
public class DefaultECloudSdkClient implements ECloudSdkClient {
    private ApiClient apiClient;
    private Config config;
    private RuntimeConfig runtimeConfig;
    private String poolId;
    private String regionId;
    private String endpoint;
    @Resource
    private ApplicationEventPublisher mongoDbLogEventPublisher;

//    private static final Map<String, String> ALL_REGIONS = initRegions();

    public DefaultECloudSdkClient(ClientKey key) {
        this.config = this.initConfig(key);
        HttpRequest httpRequest = this.initRequest(key.getProduct());
//        setEndpoint(this.config, httpRequest);
        httpRequest.setEndpoint(endpoint);
        this.apiClient = Configuration.getDefaultApiClient(this.config, httpRequest);
        this.runtimeConfig = RuntimeConfig.builder().autoRetry(true).maxRetryTimes(3).build();
        this.regionId = key.getRegionId();
        this.poolId = key.getPoolId();
        this.endpoint = key.getEndpoint();
    }

    private Config initConfig(ClientKey key) {
        Config config = new Config();
        config.setAccessKey(key.getAccessKey());
        config.setSecretKey(key.getSecretKey());
        config.setPoolId(key.getPoolId());
        return config;
    }

    private HttpRequest initRequest(ECloudProductEnum product) {
        String version = "v1";
        HttpRequest httpRequest = new HttpRequest();
        httpRequest.setProduct(product.getValue());
        httpRequest.setVersion(version);
        httpRequest.setSdkVersion(product.getVersion());
        return httpRequest;
    }

//    private static void setEndpoint(Config config, HttpRequest httpRequest) {
//        if (ObjectUtil.isUnset(config.getPoolId())) {
//            httpRequest.setEndpoint("https://ecloud.10086.cn");
//        } else {
//            String endpoint = (String) ALL_REGIONS.get(config.getPoolId());
//            if (ObjectUtil.isUnset(endpoint)) {
//                httpRequest.setEndpoint("https://ecloud.10086.cn");
//            } else {
//                httpRequest.setEndpoint(endpoint);
//            }
//        }
//    }

    private static Map<String, String> initRegions() {
        Map<String, String> map = new HashMap();
        map.put("CIDC-RP-04", "https://console-yunnan-1.cmecloud.cn:8443");
        map.put("CIDC-RP-16", "https://console-qinghai-1.cmecloud.cn:8443");
        map.put("CIDC-RP-25", "https://console-wuxi-1.cmecloud.cn:8443");
        map.put("CIDC-RP-26", "https://console-dongguan-1.cmecloud.cn:8443");
        map.put("CIDC-RP-27", "https://console-yaan-1.cmecloud.cn:8443");
        map.put("CIDC-RP-28", "https://console-zhengzhou-1.cmecloud.cn:8443");
        map.put("CIDC-RP-29", "https://console-beijing-2.cmecloud.cn:8443");
        map.put("CIDC-RP-30", "https://console-zhuzhou-1.cmecloud.cn:8443");
        map.put("CIDC-RP-31", "https://console-jinan-1.cmecloud.cn:8443");
        map.put("CIDC-RP-32", "https://console-xian-1.cmecloud.cn:8443");
        map.put("CIDC-RP-33", "https://console-shanghai-1.cmecloud.cn:8443");
        map.put("CIDC-RP-34", "https://console-chongqing-1.cmecloud.cn:8443");
        map.put("CIDC-RP-35", "https://console-ningbo-1.cmecloud.cn:8443");
        map.put("CIDC-RP-36", "https://console-tianjin-1.cmecloud.cn:8443");
        map.put("CIDC-RP-37", "https://console-jilin-1.cmecloud.cn:8443");
        map.put("CIDC-RP-38", "https://console-hubei-1.cmecloud.cn:8443");
        map.put("CIDC-RP-39", "https://console-jiangxi-1.cmecloud.cn:8443");
        map.put("CIDC-RP-40", "https://console-gansu-1.cmecloud.cn:8443");
        map.put("CIDC-RP-41", "https://console-shanxi-1.cmecloud.cn:8443");
        map.put("CIDC-RP-42", "https://console-liaoning-1.cmecloud.cn:8443");
        map.put("CIDC-RP-43", "https://console-yunnan-2.cmecloud.cn:8443");
        map.put("CIDC-RP-44", "https://console-hebei-1.cmecloud.cn:8443");
        map.put("CIDC-RP-45", "https://console-fujian-1.cmecloud.cn:8443");
        map.put("CIDC-RP-46", "https://console-guangxi-1.cmecloud.cn:8443");
        map.put("CIDC-RP-47", "https://console-anhui-1.cmecloud.cn:8443");
        map.put("CIDC-RP-48", "https://console-huhehaote-1.cmecloud.cn:8443");
        map.put("CIDC-RP-49", "https://console-guiyang-1.cmecloud.cn:8443");
        map.put("CIDC-CORE-00", "https://ecloud.10086.cn");
        map.put("CIDC-RP-53", "https://console-hainan-1.cmecloud.cn:8443");
        map.put("CIDC-RP-54", "https://console-xinjiang-1.cmecloud.cn:8443");
        map.put("CIDC-RP-55", "http://console-heilongjiang-1.cmecloud.cn:18080");
        map.put("CIDC-BRP-25", "");
        map.put("CIDC-RP-60", "");
        map.put("CIDC-RP-61", "");
        map.put("CIDC-RP-62", "");
        return Collections.unmodifiableMap(map);
    }


    @Override
    public <T extends ECloudResponseBody> ECloudSdkResponse<T> execute(ECloudAcsRequest<T> request) {
        long startTime = System.currentTimeMillis();
        HibernateValidator.validate(request);
        try {
            ECloudSdkResponse<T> response = this.doExecute(request);
            this.dealwithResult(startTime, request, response);
            return response;
        } catch (Exception ex) {
            this.dealwithResult(startTime, request, ex);
            throw ex;
        }
    }

    @Override
    public String getPoolId() {
        return this.poolId;
    }

    @Override
    public String getRegionId() {
        return this.regionId;
    }

    private <T extends ECloudResponseBody> ECloudSdkResponse<T> doExecute(ECloudAcsRequest<T> request) {
        ECloudSdkResponse<T> result = new ECloudSdkResponse<T>();
        ApiResponse<LinkedTreeMap> resp;
        try {
            Type returnType = (new TypeToken<LinkedTreeMap>() {
            }).getType();
            resp = this.apiClient.excute(request.getParams(), this.runtimeConfig, returnType);
            return this.parseResult(resp.getData(), request);
        } catch (SdkException e) {
            e.printStackTrace();
            result.setState(StateEnum.ERROR);
            return result;
        }
    }

    private <T extends ECloudResponseBody> ECloudSdkResponse<T> parseResult(LinkedTreeMap map, ECloudAcsRequest<T> request) {
        ECloudSdkResponse<T> result = new ECloudSdkResponse<>();
        result = JSON.parseObject(JSON.toJSONString(map), ECloudSdkResponse.class);
        Object body = map.get("body");
        if (body == null) {
            return result;
        }
        //反射处理
        if (body instanceof String || body instanceof Boolean) {
            T t = this.reflectContent(body, request);
            result.setList(Lists.newArrayList(t));
            return result;
        }

        String json = new Gson().toJson(body);
        if (body instanceof List) {
            result.setList(JSON.parseArray(json, request.getResponseClass()));
        } else {
            T t = (T) JSON.parseObject(json, request.getResponseClass());
            result.setList(Lists.newArrayList(t));
        }
        return result;
    }

    /**
     * 移动云官方接口直接返回的是字符串或者布尔类型，需要反射处理封装成对象
     * 对象的属性名固定为content
     */
    private <T extends ECloudResponseBody> T reflectContent(Object content, ECloudAcsRequest<T> request) {
        Class<T> responseClass = request.getResponseClass();
        try {
            Constructor<T> constructor = responseClass.getDeclaredConstructor();
            constructor.setAccessible(true);
            T t = constructor.newInstance();
            Field contentField = responseClass.getDeclaredField("content");
            if (contentField == null) {
                return t;
            }
            contentField.setAccessible(true);
            contentField.set(t, content);
            return t;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void dealwithResult(long startTime, ECloudAcsRequest request, Object response) {
        long endTime = System.currentTimeMillis();
        mongoDbLogEventPublisher.publishEvent(new CallOutApiEvent(this, startTime, endTime, this.config.getPoolId(), this.config.getAccessKey(), request.getParams(), response));
    }
}
