package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcSubnetListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcSubnetListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSubnetListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSubnetListRequest extends ECloudAcsRequest<GetSubnetListResponse> {
    @ApiModelProperty("子网ID列表")
    private List<String> rangeInSubnetIds;

    @ApiModelProperty("子网IP协议类型：4-IPv4；6-IPv6")
    private VersionsEnum ipVersion;

    @ApiModelProperty("子网所属的VPC路由ID")
    private String routerId;

    @ApiModelProperty("子网所属的VPC ID")
    private String vpcId;

    @ApiModelProperty("NAT网关ID，用于SNAT规则创建时获取可用子网列表")
    private String natGatewayId;

    @ApiModelProperty("子网所属的网络ID")
    private String networkId;

    @ApiModelProperty("根据子网名称搜索")
    private String queryKey;

    @ApiModelProperty("子网是否已经被IPSec VPN占用")
    private Boolean ipsecVpnUsed;

    @ApiModelProperty("列表分页页数，默认值为1")
    private Integer page;

    @ApiModelProperty("列表分页每页大小，默认值为10")
    private Integer pageSize;

    @ApiModelProperty("子网可用区")
    private String region;

    @ApiModelProperty("网所属的网络类型：VM-云主机子网；IRONIC-裸金属子网；INTER_CONNECTION-互联子网； EBM-云主机（同VM合并）；VMWARE-专属云；DBAUDIT-数据库审计")
    private NetworkTypeEnum networkTypeEnum;

    @ApiModelProperty("客户ID")
    private String customerId;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("listSubnets")
                .uri("/customer/v3/subnet")
                .gatewayUri("/api/openapi-vpc/customer/v3/subnet")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcSubnetListRequest toRequest() {
        VpcSubnetListQuery query = VpcSubnetListQuery.builder()
                .rangeInSubnetIds(this.rangeInSubnetIds)
                .ipVersion(this.ipVersion)
                .routerId(this.routerId)
                .vpcId(this.vpcId)
                .natGatewayId(this.natGatewayId)
                .networkId(this.networkId)
                .queryKey(this.queryKey)
                .ipsecVpnUsed(this.ipsecVpnUsed)
                .page(this.page)
                .pageSize(this.pageSize)
                .region(this.region)
                .networkTypeEnum(this.networkTypeEnum)
                .customerId(this.customerId)
                .build();
        return VpcSubnetListRequest.builder()
                .listSubnetsQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetSubnetListResponse.class;
    }
}
