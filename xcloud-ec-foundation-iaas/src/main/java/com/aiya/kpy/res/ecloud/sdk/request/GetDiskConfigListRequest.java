package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetDiskConfigListResponseContent;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetDiskConfigListRequest extends ECloudAcsRequest<GetDiskConfigListResponseContent> {

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listVolumeConfig")
                .uri("/customer/v3/volume/volumeType/list")
                .gatewayUri("/api/v2/volume/customer/volumeType/list")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.GET)
                .build();
    }

    @Override
    public Object toRequest() {
        return null;
    }

    @Override
    public Class getResponseClass() {
        return GetDiskConfigListResponseContent.class;
    }
}
