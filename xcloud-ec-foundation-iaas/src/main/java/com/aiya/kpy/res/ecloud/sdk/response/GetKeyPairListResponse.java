package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetKeyPairListResponse implements ECloudResponseBody {
    @SerializedName("total")
    private Integer total;
    @SerializedName("content")
    private List<GetKeyPairListResponseContent> content;
}
