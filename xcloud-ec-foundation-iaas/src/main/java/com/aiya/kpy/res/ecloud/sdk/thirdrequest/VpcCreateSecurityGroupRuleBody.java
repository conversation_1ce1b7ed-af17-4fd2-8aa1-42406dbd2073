package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.DirectionEnum;
import com.aiya.kpy.res.ecloud.enums.EtherTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ProtocolEnum;
import com.aiya.kpy.res.ecloud.enums.RemoteTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class VpcCreateSecurityGroupRuleBody extends Body {
    @SerializedName("securityGroupId")
    private String securityGroupId;
    @SerializedName("remoteType")
    private RemoteTypeEnum remoteType;
    @SerializedName("protocol")
    private ProtocolEnum protocol;
    @SerializedName("remoteSecurityGroupId")
    private String remoteSecurityGroupId;
    @SerializedName("minPortRange")
    private Integer minPortRange;
    @SerializedName("etherType")
    private EtherTypeEnum etherType;
    @SerializedName("description")
    private String description;
    @SerializedName("remoteIpPrefix")
    private String remoteIpPrefix;
    @SerializedName("direction")
    private DirectionEnum direction;
    @SerializedName("maxPortRange")
    private Integer maxPortRange;
}
