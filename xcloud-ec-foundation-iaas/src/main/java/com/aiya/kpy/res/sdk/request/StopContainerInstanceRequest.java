package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.StopContainerInstanceResponse;

/**
 * 停止容器实例请求
 * 用于容器云平台停止指定容器实例的API请求类。通过容器UUID
 * 进行停止操作，支持容器实例的状态管理。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class StopContainerInstanceRequest extends KpyRpcAcsRequest<StopContainerInstanceResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private String uuid;

    public StopContainerInstanceRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "StopContainerInstance");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "Uuid", description = "容器UUID", required = true)
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public Class<StopContainerInstanceResponse> getResponseClass() {
        return StopContainerInstanceResponse.class;
    }
}
