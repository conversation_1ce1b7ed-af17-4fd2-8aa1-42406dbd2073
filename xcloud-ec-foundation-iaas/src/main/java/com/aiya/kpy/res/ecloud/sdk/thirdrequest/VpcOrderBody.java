package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.SpecsEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VpcOrderBody extends Body {
    @SerializedName("specs")
    private SpecsEnum specs;
    @SerializedName("cidrV6")
    private String cidrV6;
    @SerializedName("tagId")
    private String tagId;
    @SerializedName("name")
    private String name;
    @SerializedName("networkName")
    private String networkName;
    @SerializedName("cidr")
    private String cidr;
    @SerializedName("region")
    private String region;
    @SerializedName("networkTypeEnum")
    private NetworkTypeEnum networkTypeEnum;
    @SerializedName("tags")
    private List<RequestTags> tags;
}
