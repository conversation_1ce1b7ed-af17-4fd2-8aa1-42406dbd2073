package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmDeletePath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmDeleteQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmDeleteRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteInstanceResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteInstanceRequest extends ECloudAcsRequest<DeleteInstanceResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @ApiModelProperty("是否一起退订云硬盘")
    private Boolean dataVolumeDelete;

    @ApiModelProperty("是否一起退订IPV4")
    private Boolean publicIpDelete;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmDelete")
                .uri("/acl/v3/server/order/{serverId}")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/order/{serverId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmDeleteRequest toRequest() {
        return VmDeleteRequest.builder()
                .vmDeletePath(VmDeletePath.builder().serverId(this.serverId).build())
                .vmDeleteQuery(VmDeleteQuery.builder()
                        .dataVolumeDelete(this.dataVolumeDelete)
                        .publicIpDelete(this.publicIpDelete)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteInstanceResponse.class;
    }
}
