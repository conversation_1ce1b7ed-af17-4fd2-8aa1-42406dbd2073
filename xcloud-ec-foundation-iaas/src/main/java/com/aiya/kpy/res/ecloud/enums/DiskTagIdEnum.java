package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum DiskTagIdEnum {
    ANHUI_SYSTEM("SRY_<PERSON><PERSON><PERSON>_SYSTEM"),
    <PERSON>OMEN_SYSTEM("SRY_AoMen_SYSTEM"),
    BEIJING_SYSTEM("SRY_BeiJing_SYSTEM"),
    CHONGQING_SYSTEM("SRY_ChongQing_SYSTEM"),
    FUJIAN_SYSTEM("SRY_FuJian_SYSTEM"),
    GUANGDONGERJI_EJYG("SRY_<PERSON><PERSON>DongErJi_EJYG"),
    GUANGDONG_ESOP("SRY_GuangDong_ESOP"),
    GANSU_SYSTEM("SRY_GanSu_SYSTEM"),
    GUANGXI_SYSTEM("SRY_GuangXi_SYSTEM"),
    G<PERSON>ZHOU_SYSTEM("SRY_<PERSON><PERSON><PERSON><PERSON>_SYSTEM"),
    HEBEI_SYSTEM("SRY_HeB<PERSON>_SYSTEM"),
    HU<PERSON>I_SYSTEM("SRY_HuBei_SYSTEM"),
    XIANGGANG_SYSTEM("SRY_XiangGang_SYSTEM"),
    HEILONGJIANG_SYSTEM("SRY_HeiLongJiang_SYSTEM"),
    HENAN_SYSTEM("SRY_HeNan_SYSTEM"),
    HUNAN_SYSTEM("SRY_HuNan_SYSTEM"),
    HAINAN_SYSTEM("SRY_HaiNan_SYSTEM"),
    JILIN_SYSTEM("SRY_JiLin_SYSTEM"),
    JIANGSU_SYSTEM("SRY_JiangSu_SYSTEM"),
    JIANGXI_SYSTEM("SRY_JiangXi_SYSTEM"),
    LIAONING_SYSTEM("SRY_LiaoNing_SYSTEM"),
    NEIMENGGU_SYSTEM("SRY_NeiMengGu_SYSTEM"),
    NINGXIA_SYSTEM("SRY_NingXia_SYSTEM"),
    QINGHAI_SYSTEM("SRY_QingHai_SYSTEM"),
    SICHUAN_SYSTEM("SRY_SiChuan_SYSTEM"),
    SHANDONG_SYSTEM("SRY_ShanDong_SYSTEM"),
    SHANGHAI_SYSTEM("SRY_ShangHai_SYSTEM"),
    SHANXI_SYSTEM("SRY_ShanXi_SYSTEM"),
    SHAANXI_SYSTEM("SRY_ShaanXi_SYSTEM"),
    TIANJIN_SYSTEM("SRY_TianJin_SYSTEM"),
    TAIWAN_SYSTEM("SRY_TaiWan_SYSTEM"),
    XINJIANG_SYSTEM("SRY_XinJiang_SYSTEM"),
    XIZANG_SYSTEM("SRY_XiZang_SYSTEM"),
    YUNNAN_SYSTEM("SRY_YunNan_SYSTEM"),
    ZHEJIANGWANGGUAN_WG("SRY_ZheJiangWangGuan_WG"),
    ZHEJIANGYEZHI_YZ("SRY_ZheJiangYeZhi_YZ");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
