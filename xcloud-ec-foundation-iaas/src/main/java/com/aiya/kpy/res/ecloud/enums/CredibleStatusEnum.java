package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Getter
@AllArgsConstructor
public enum CredibleStatusEnum {
    CREDIBLE("CREDIBLE"),
    INCREDIBLE("INCREDIBLE"),
    UNMEASURED("UNMEASURED"),
    UNOPENED("UNOPENED");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
