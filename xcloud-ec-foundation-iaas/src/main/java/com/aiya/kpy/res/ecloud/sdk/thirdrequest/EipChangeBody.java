package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
@Builder
public class EipChangeBody extends Body {
    @SerializedName("vpoolId")
    private String vpoolId;
    @SerializedName("tagId")
    private String tagId;
    @SerializedName("ip")
    private String ip;
    @SerializedName("bandwidthSize")
    private Integer bandwidthSize;
    @SerializedName("bandwidthId")
    private String bandwidthId;
    @SerializedName("chargeModeEnum")
    private ChargeModeEnum chargeModeEnum;
    @SerializedName("vPoolId")
    private String vPoolId;
    @SerializedName("vaz")
    private String vaz;
}
