package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class VpcGetSecurityGroupListQuery extends Query {
    @SerializedName("types")
    private List<ResourceTypeEnum> types;
    @SerializedName("securityGroupIds")
    private List<String> securityGroupIds;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("page")
    private Integer page;
    @SerializedName("queryWord")
    private String queryWord;
    @SerializedName("portId")
    private String portId;
    @SerializedName("region")
    private String region;
    @SerializedName("vaz")
    private String vaz;
    @SerializedName("vPoolId")
    private String vPoolId;
}
