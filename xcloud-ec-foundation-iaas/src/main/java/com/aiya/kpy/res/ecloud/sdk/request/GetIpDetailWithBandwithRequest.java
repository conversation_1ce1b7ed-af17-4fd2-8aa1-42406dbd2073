package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwithDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwithDetailRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetIpDetailWithBandwithResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetIpDetailWithBandwithRequest extends ECloudAcsRequest<GetIpDetailWithBandwithResponse> {
    @ApiModelProperty("ipid")
    @NotNull(message = "ipid不能为空")
    private String ipId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("getFipWithBandwidth")
                .uri("/acl/v3/floatingip/getRespWithBw/{ipId}")
                .gatewayUri("/api/openapi-eip/acl/v3/floatingip/getRespWithBw/{ipId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipWithBandwithDetailRequest toRequest() {
        EipWithBandwithDetailPath path = EipWithBandwithDetailPath.builder().ipId(ipId).build();
        return EipWithBandwithDetailRequest.builder().getFipWithBandwidthPath(path).build();
    }

    @Override
    public Class getResponseClass() {
        return GetIpDetailWithBandwithResponse.class;
    }
}
