package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteNetworkPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteNetworkRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteNetworkResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteNetworkRequest extends ECloudAcsRequest<DeleteNetworkResponse> {
    @ApiModelProperty("网络Id")
    @NotNull(message = "网络Id不能为空")
    private String networkId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("deleteNetwork")
                .uri("/customer/v3/network/{networkId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/network/{networkId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcDeleteNetworkRequest toRequest() {
        return VpcDeleteNetworkRequest.builder()
                .deleteNetworkPath(VpcDeleteNetworkPath.builder()
                        .networkId(this.networkId)
                        .build()
                ).build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteNetworkResponse.class;
    }
}
