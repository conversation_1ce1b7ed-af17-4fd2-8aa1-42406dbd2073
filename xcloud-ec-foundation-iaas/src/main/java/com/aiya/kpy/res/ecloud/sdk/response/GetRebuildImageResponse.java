package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.ImsStatusEnum;
import com.aiya.kpy.res.ecloud.enums.OsTypeEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRebuildImageResponse implements ECloudResponseBody {
    @ApiModelProperty("更新时间")
    private String modifiedTime;

    @ApiModelProperty("备注")
    private String note;

    @ApiModelProperty("源镜像ID")
    private String sourceImageId;

    @ApiModelProperty("镜像来源类型")
    private Integer type;

    @ApiModelProperty("主机规格需要的系统盘最小大小，单位GB")
    private Long minDisk;

    @ApiModelProperty("云主机id")
    private String serverId;

    @ApiModelProperty("镜像路径")
    private String url;

    @ApiModelProperty("原镜像所属资源池ID")
    private String sourceImagePoolId;

    @ApiModelProperty("镜像大小,单位M")
    private Long size;

    @ApiModelProperty("镜像名称")
    private String name;

    @ApiModelProperty("操作系统类型")
    private OsTypeEnum osType;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("镜像id")
    private String id;

    @ApiModelProperty("镜像状态")
    private ImsStatusEnum status;
}
