package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.aiya.kpy.res.ecloud.enums.ChargePeriodEnum;
import com.aiya.kpy.res.ecloud.enums.IpTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipOrderCreateBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipOrderCreateRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.RequestTags;
import com.aiya.kpy.res.ecloud.sdk.response.CreateEipResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateEipRequest extends ECloudAcsRequest<CreateEipResponse> {
    @ApiModelProperty("是否开启自动续订")
    private Boolean autoRenew;

    @ApiModelProperty("购买带宽大小，单位：Mbps，当前可支持的带宽值范围为1~500Mbps，如果需要更大带宽，请提交工单")
    private Integer bandwidthKbSize;

    @ApiModelProperty("公网IP的带宽大小, 单位: MB")
    @NotNull(message = "公网IP的带宽大小不能为空")
    private Integer bandwidthSize;

    @ApiModelProperty("计费方式")
    @NotNull(message = "计费方式不能为空")
    private ChargeModeEnum chargeModeEnum;

    @ApiModelProperty("计费周期")
    private ChargePeriodEnum chargePeriodEnum;

    @ApiModelProperty("弹性公网IP名称，5-128位数字、字母和下划线的组合（必须以字母开头），批量购买的所有弹性公网IP均使用相同IP名称")
    private String description;

    @ApiModelProperty("购买时长，计费周期为month或者year时必填：计费周期为month时，取值为1~12（月），取值为0时默认订购1个月同时开启自动续订；计费周期为year时，取值为[年数*12月]，最多5年")
    private Integer duration;

    @ApiModelProperty("指定弹性公网IP创建IP地址，只能指定当前用户历史订购且已退订的弹性公网IP，指定IP地址创建时，可能因IP地址被其他客户占用或移动云不存在该地址导致最终订购失败")
    private String floatingIpAddress;

    @ApiModelProperty("是否查询mop订单详情接口返回订单详情，包括订单最终状态")
    private Boolean isQueryOrderExts;

    @ApiModelProperty("订购数量，默认为1，支持批量购买公网IP，取值为1~10个")
    @NotNull(message = "订购数量不能为空")
    private Integer quantity;

    @ApiModelProperty("系统级别标签，订购时传入该入参则订购的弹性公网IP仅能通过API接口退订")
    private String tagId;

    @ApiModelProperty("标签结构体")
    private List<RequestTags> tags;

    @ApiModelProperty("虚拟可用区资源池选择")
    private String vPoolId;

    @ApiModelProperty("虚拟可用区选择")
    private String vaz;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("eipOrderCreate")
                .uri("/customer/v3/order/create/floatingip")
                .gatewayUri("/api/openapi-eip/customer/v3/order/create/floatingip")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipOrderCreateRequest toRequest() {
        EipOrderCreateBody body = EipOrderCreateBody.builder()
                .chargePeriodEnum(this.chargePeriodEnum)
                .quantity(this.quantity)
                .tagId(this.tagId)
                .bandwidthSize(this.bandwidthSize)
                .description(this.description)
                .chargeModeEnum(this.chargeModeEnum)
                .floatingIpAddress(this.floatingIpAddress)
                .isQueryOrderExts(this.isQueryOrderExts)
                .vaz(this.vaz)
                .tags(this.tags)
                .duration(this.duration)
                .bandwidthKbSize(this.bandwidthKbSize)
                .autoRenew(this.autoRenew)
                .ipType(IpTypeEnum.MOBILE)
                .vPoolId(this.vPoolId)
                .build();
        return EipOrderCreateRequest.builder()
                .eipOrderCreateBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateEipResponse.class;
    }
}
