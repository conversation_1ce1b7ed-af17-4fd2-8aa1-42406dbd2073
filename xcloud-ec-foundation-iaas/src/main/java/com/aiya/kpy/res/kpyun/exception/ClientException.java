/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.exception;

/**
 * <AUTHOR>
 */
public class ClientException extends Exception {
	private static final long serialVersionUID = 8618106096274953981L;
	private String requestId;
	private String errCode;
	private String errMsg;

	public ClientException(String errCode, String errMsg, String requestId) {
		this(errCode, errMsg);
		this.requestId = requestId;
	}
	public ClientException(String errCode, String errMsg) {
		super(errCode + " : " + errMsg);
		this.errCode = errCode;
		this.errMsg = errMsg;
	}

	public ClientException(String message) {
		super(message);
	}

	public ClientException(Throwable cause) {
		super(cause);
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getErrCode() {
		return errCode;
	}

	public void setErrCode(String errCode) {
		this.errCode = errCode;
	}

	public String getErrMsg() {
		return errMsg;
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}

	@Override
	public String getMessage() {
		return super.getMessage() + (null == getRequestId() ? "" : "\r\nRequestId : " + getRequestId());
	}
}
