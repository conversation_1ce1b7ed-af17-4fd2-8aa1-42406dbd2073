package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipResponseOrderExtResps;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class RenewEipResponse implements ECloudResponseBody {
    @SerializedName("orderId")
    private String orderId;
    @SerializedName("orderExtResps")
    private List<EipResponseOrderExtResps> orderExtResps;
}
