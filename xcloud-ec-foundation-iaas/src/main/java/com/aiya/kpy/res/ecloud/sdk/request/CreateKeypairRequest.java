package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateKeypairBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateKeypairRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateKeypairResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateKeypairRequest extends ECloudAcsRequest<CreateKeypairResponse> {
    @ApiModelProperty("api版本")
    private String apiVersion;

    /**
     * 密钥名称只能包括英文字母、数字，长度为5~128位
     */
    @ApiModelProperty("密钥名称")
    @NotNull(message = "密钥名称不能为空")
    private String name;

    @ApiModelProperty("可用区")
    private String region;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmCreateKeypair")
                .uri("/customer/v3/keypair")
                .gatewayUri("/api/openapi-ecs/customer/v3/keypair")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    public VmCreateKeypairRequest toRequest() {
        return VmCreateKeypairRequest.builder()
                .vmCreateKeypairBody(VmCreateKeypairBody.builder()
                        .apiVersion(this.apiVersion)
                        .name(this.name)
                        .region(this.region)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateKeypairResponse.class;
    }
}
