package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum IpTypeEnum {
    /**
     * 移动类型IP（正常用户可订购）
     */
    MOBILE("MOBILE"),
    /**
     * 多线类型IP（灰度用户开放）
     */
    MULTI_LINE("MULTI_LINE"),
    ENJOYBANDWIDTH("EnjoyBandwidth");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
