package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.sdk.model.PermissionSet;
import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2020/8/28
 * 查看网卡关联的规则列表
 */
public class DescribeNicSecurityGroupsAclsResponse extends KpyRpcAcsResponse {
    private String regionId;
    private PermissionSet Permissions;

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public PermissionSet getPermissions() {
        return Permissions;
    }

    public void setPermissions(PermissionSet permissions) {
        Permissions = permissions;
    }
}
