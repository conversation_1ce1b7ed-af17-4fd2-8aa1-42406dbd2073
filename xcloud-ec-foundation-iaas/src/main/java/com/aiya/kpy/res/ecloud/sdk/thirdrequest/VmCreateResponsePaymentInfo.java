package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmCreateResponsePaymentInfo {
    @ApiModelProperty("产品名称")
    @SerializedName("ProductName")
    private String productName;

    @ApiModelProperty("交易代码")
    @SerializedName("ActivityCode")
    private String activityCode;

    @ApiModelProperty("商户自定义参数")
    @SerializedName("CustomParam")
    private String customParam;

    @ApiModelProperty("订单号")
    @SerializedName("OrderNo")
    private String orderNo;

    @ApiModelProperty("产品编号")
    @SerializedName("ProductID")
    private String productID;

    @ApiModelProperty("支付成功跳转返回页面URL")
    @SerializedName("ReturnURL")
    private String returnURL;

    @ApiModelProperty("请求方渠道编码")
    @SerializedName("ReqChannel")
    private String reqChannel;

    @ApiModelProperty("中国移动用户号码")
    @SerializedName("IDValue")
    private String idValue;

    @ApiModelProperty("中国移动用户标识类型")
    @SerializedName("IDType")
    private String idType;

    @ApiModelProperty("微信公众号ID/应用ID")
    @SerializedName("WeiXinAppId")
    private String weiXinAppId;

    @ApiModelProperty("商品展示网址")
    @SerializedName("ProductURL")
    private String productURL;

    @ApiModelProperty("支付链接，paymentInfo节点下的所有以大写字母开头的参数，即序号为5.3~5.24的参数，用post+form表单的形式跳转到此链接")
    @SerializedName("payLink")
    private String payLink;

    @ApiModelProperty("业务类型")
    @SerializedName("BusiType")
    private String busiType;

    @ApiModelProperty("用户支付金额")
    @SerializedName("Payment")
    private String payment;

    @ApiModelProperty("该笔订单允许的最晚付款时间")
    @SerializedName("TimeoutExpress")
    private String timeoutExpress;

    @ApiModelProperty("证书标识串")
    @SerializedName("CerID")
    private String cerID;

    @ApiModelProperty("客户编码")
    @SerializedName("CustomerNumber")
    private String customerNumber;

    @ApiModelProperty("归属省份")
    @SerializedName("HomeProv")
    private String homeProv;

    @ApiModelProperty("订单总金额")
    @SerializedName("OrderMoney")
    private String orderMoney;

    @ApiModelProperty("订单结果通知URL")
    @SerializedName("NotifyURL")
    private String notifyURL;

    @ApiModelProperty("产品描述")
    @SerializedName("ProductDesc")
    private String productDesc;

    @ApiModelProperty("移动云全网渠道编码")
    @SerializedName("BusinessChannels")
    private String businessChannels;

    @ApiModelProperty("充值金额")
    @SerializedName("ChargeMoney")
    private String chargeMoney;

    @ApiModelProperty("签名值")
    @SerializedName("SignValue")
    private String signValue;
}
