package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipResponseOrderExtResps;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class CreateEipResponse implements ECloudResponseBody {
    private String orderId;
    private List<EipResponseOrderExtResps> orderExtResps;
}
