package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.BandwidthTypeEnum;
import com.aiya.kpy.res.ecloud.enums.BindTypeEnum;
import com.aiya.kpy.res.ecloud.enums.IpTypeEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EipWithBandwidthListQuery extends Query {
    @SerializedName("openIpv6Converter")
    private Boolean openIpv6Converter;
    @SerializedName("occupy")
    private Boolean occupy;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("bound")
    private Boolean bound;
    @SerializedName("bindType")
    private BindTypeEnum bindType;
    @SerializedName("idQueryWord")
    private String idQueryWord;
    @SerializedName("frozen")
    private Boolean frozen;
    @SerializedName("natGatewayId")
    private String natGatewayId;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("bandwidthType")
    private BandwidthTypeEnum bandwidthType;
    @SerializedName("availableForSbw")
    private Boolean availableForSbw;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("page")
    private Integer page;
    @SerializedName("queryWord")
    private String queryWord;
    @SerializedName("ipType")
    private IpTypeEnum ipType;
}
