package com.aiya.kpy.res.sdk.model;

import java.util.Date;

/**
 * ContainerVol 容器存储卷模型
 *
 * 用于容器云平台的容器存储卷配置模型，包含存储卷的大小、
 * 类型、挂载路径等存储配置信息。支持容器的持久化存储
 * 管理和数据卷挂载。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class ContainerVol {
    private Integer id;
    private String uuid;
    private String description;
    private String dstPath;
    private String host;
    private Integer regionId;
    private Integer size;
    private Integer state;
    private Integer volType;
    private String dockerUuid;
    private String deviceNo;
    private Date createdAt;
    private Date updatedAt;
    private String creator;
    private Integer userId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDstPath() {
        return dstPath;
    }

    public void setDstPath(String dstPath) {
        this.dstPath = dstPath;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Integer getVolType() {
        return volType;
    }

    public void setVolType(Integer volType) {
        this.volType = volType;
    }

    public String getDockerUuid() {
        return dockerUuid;
    }

    public void setDockerUuid(String dockerUuid) {
        this.dockerUuid = dockerUuid;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }
}
