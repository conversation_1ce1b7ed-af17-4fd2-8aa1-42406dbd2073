package com.aiya.kpy.res.ecloud.enums;

import com.aiya.kpy.common.enums.BillingCycleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum BillingTypeEnum {
    MONTH("MONTH"),
    YEAR("YEAR"),
    HOUR("HOUR");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }


    public static BillingTypeEnum valueOf(BillingCycleEnum billingCycleEnum) {
        if (Objects.isNull(billingCycleEnum)) {
            return null;
        }
        return BillingTypeEnum.valueOf(billingCycleEnum.name());
    }
}
