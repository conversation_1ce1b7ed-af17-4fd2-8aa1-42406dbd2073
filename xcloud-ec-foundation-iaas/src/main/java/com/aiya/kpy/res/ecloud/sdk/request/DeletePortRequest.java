package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeletePortPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeletePortRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeletePortResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeletePortRequest extends ECloudAcsRequest<DeletePortResponse> {
    @ApiModelProperty("虚拟网卡id")
    @NotNull(message = "虚拟网卡id不能为空")
    private String portId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("deletePort")
                .uri("/customer/v3/port/portId/{portId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/port/portId/{portId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcDeletePortRequest toRequest() {
        VpcDeletePortPath path = VpcDeletePortPath.builder().portId(this.portId).build();
        return VpcDeletePortRequest.builder().deletePortPath(path).build();
    }

    @Override
    public Class getResponseClass() {
        return DeletePortResponse.class;
    }
}
