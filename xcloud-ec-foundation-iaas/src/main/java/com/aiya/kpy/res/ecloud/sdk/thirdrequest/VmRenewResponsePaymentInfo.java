package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmRenewResponsePaymentInfo {
    @ApiModelProperty("产品名称")
    private String productName;

    @ApiModelProperty("交易代码")
    private String activityCode;

    @ApiModelProperty("商户自定义参数")
    private String customParam;

    @ApiModelProperty("订单号")
    private String orderNo;

    @ApiModelProperty("产品编号")
    private String productID;

    @ApiModelProperty("支付成功跳转返回页面URL")
    private String returnURL;

    @ApiModelProperty("请求方渠道编码")
    private String reqChannel;

    @ApiModelProperty("中国移动用户号码")
    private String idValue;

    @ApiModelProperty("中国移动用户标识类型")
    private String idType;

    @ApiModelProperty("微信公众号ID/应用ID")
    private String weiXinAppId;

    @ApiModelProperty("商品展示网址")
    private String productURL;

    @ApiModelProperty("支付链接")
    private String payLink;

    @ApiModelProperty("业务类型")
    private String busiType;

    @ApiModelProperty("用户支付金额")
    private String payment;

    @ApiModelProperty("该笔订单允许的最晚付款时间")
    private String timeoutExpress;

    @ApiModelProperty("证书标识串")
    private String cerID;

    @ApiModelProperty("客户编码")
    private String customerNumber;

    @ApiModelProperty("归属省份")
    private String homeProv;

    @ApiModelProperty("订单总金额")
    private String orderMoney;

    @ApiModelProperty("订单结果通知URL")
    private String notifyURL;

    @ApiModelProperty("产品描述")
    private String productDesc;

    @ApiModelProperty("移动云全网渠道编码")
    private String businessChannels;

    @ApiModelProperty("充值金额")
    private String chargeMoney;

    @ApiModelProperty("签名值")
    private String signValue;
}
