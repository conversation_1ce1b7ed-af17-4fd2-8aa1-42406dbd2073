package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum ResourceTypeEnum {
    /**
     * 云主机
     */
    VM("vm"),
    /**
     * 裸金属
     */
    IRONIC("ironic"),
    /**
     * NAS网卡
     */
    NAS("nas"),
    /**
     * 东西向网卡
     */
    EW("ew"),
    ELB("elb"),
    BW("bw"),
    SBW("sbw"),
    RDS("rds"),
    HAVIP("havip"),
    BASTION("bastion"),
    SNAT("snat"),
    DNAT("dnat"),
    SSLVPN("sslvpn"),
    IPSECVPN("ipsecvpn"),
    VMWARE("vmware"),
    LOGAUDIT("logaudit"),
    /**
     * 云主机（同VM合并）
     */
    EBM("ebm"),
    PORT("port"),
    PRIVATESERVICE("privateservice"),
    SECONDARYPORTIP("secondaryPortIp"),
    MYSQL("mysql"),
    DBAUDIT("dbaudit"),
    REDIS("redis"),
    VOLUME("VOLUME");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
