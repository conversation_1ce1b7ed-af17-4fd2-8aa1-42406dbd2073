package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerSshkeyResponse;

/**
 * 创建容器SSH密钥请求
 *
 * @Author: liuyingjie
 * @Date: 2025/1/24
 * @Description: 用于创建容器SSH密钥的请求类，支持配置密钥名称和描述，基于container.swagger.json生成
 */
public class CreateContainerSshkeyRequest extends KpyRpcAcsRequest<CreateContainerSshkeyResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private String description;
    private String name;

    public CreateContainerSshkeyRequest() {
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "CreateContainerSshkey");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "Description", description = "Description")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @KpyParam(value = "Name", description = "Key name")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Class<CreateContainerSshkeyResponse> getResponseClass() {
        return CreateContainerSshkeyResponse.class;
    }
}
