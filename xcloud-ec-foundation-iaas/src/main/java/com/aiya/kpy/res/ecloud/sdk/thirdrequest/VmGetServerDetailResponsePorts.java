package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmGetServerDetailResponsePorts {
    @ApiModelProperty("mac地址")
    @SerializedName("macAddress")
    private String macAddress;

    @ApiModelProperty("公网ip列表")
    @SerializedName("ipId")
    private List<String> ipId;

    @ApiModelProperty("内网ip列表")
    @SerializedName("privateIp")
    private List<String> privateIp;

    @ApiModelProperty("公网IP的带宽,单位:Mb")
    @SerializedName("bandwidthSize")
    private Integer bandwidthSize;

    @ApiModelProperty("网卡名称")
    @SerializedName("name")
    private String name;

    @ApiModelProperty("公网ip列表")
    @SerializedName("publicIp")
    private List<String> publicIp;

    @ApiModelProperty("公网IP的带宽id")
    @SerializedName("bandwidthId")
    private String bandwidthId;

    @ApiModelProperty("网卡id")
    @SerializedName("id")
    private String id;
}
