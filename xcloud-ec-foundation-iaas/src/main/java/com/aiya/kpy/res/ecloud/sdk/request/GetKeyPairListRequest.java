package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmKeyPairListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmKeyPairListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetKeyPairListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetKeyPairListRequest extends ECloudAcsRequest<GetKeyPairListResponse> {
    @ApiModelProperty("密钥对名称,模糊查询")
    private String keyName;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @ApiModelProperty("分页")
    private Integer page;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmListKeyPair")
                .uri("/customer/v3/keypair")
                .gatewayUri("/api/openapi-ecs/customer/v3/keypair")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    public VmKeyPairListRequest toRequest() {
        return VmKeyPairListRequest.builder()
                .vmListKeyPairQuery(VmKeyPairListQuery.builder()
                        .size(this.pageSize)
                        .keyName(this.keyName)
                        .page(this.page)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetKeyPairListResponse.class;
    }
}
