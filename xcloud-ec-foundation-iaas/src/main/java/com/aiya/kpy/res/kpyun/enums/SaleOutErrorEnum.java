package com.aiya.kpy.res.kpyun.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/5/30
 */
@Getter
@AllArgsConstructor
public enum SaleOutErrorEnum {
    /**
     * 用户配额超限
     */
    CSLQUOTA_QUOTA_OUT_OF_USER_QUOTA("CSLQUOTA_QUOTA_OUT_OF_USER_QUOTA", SaleOutSrategyEnum.REGION),
    /**
     * 该操作已经超出配额限制
     */
    CSLOPENSTACK_VOLUME_EBS_BIZ_QUOTA_EXCEED("CSLOPENSTACK_VOLUME_EBS_BIZ_QUOTA_EXCEED", SaleOutSrategyEnum.REGION),
    /**
     * 弹性公网ip规格已售罄或剩余可售数量不足(无法主动查询剩余库存,手动售罄)
     */
    //MOP_SERVER_UNAVAILABLE("MOP_SERVER_UNAVAILABLE", SaleOutSrategyEnum.REGION),
    /**
     * 云硬盘未查询到可用区(无法主动查询剩余库存,手动售罄)
     */
    //CSLOPENSTACK_VOLUME_EBS_BIZ_CREATE_REGION_NOT_FOUND("CSLOPENSTACK_VOLUME_EBS_BIZ_CREATE_REGION_NOT_FOUND", SaleOutSrategyEnum.REGION),
    /**
     * 云主机规格售罄
     */
    CSLOPENSTACK_COMPUTE_SERVER_SERVER_PRODUCT_NOT_ONLINE("CSLOPENSTACK_COMPUTE_SERVER_SERVER_PRODUCT_NOT_ONLINE", SaleOutSrategyEnum.MODEL),
    /**
     * 无可用的宿主机
     */
    NO_AVAILABLE_HOST_AVAILABLE("无可用的宿主机", SaleOutSrategyEnum.MODEL),
    /**
     * 底层申请公网IP失败(当前版本逻辑不完善,手动售罄)
     */
    //ALLOCIATE_PUBLIC_IP_FAIL("底层申请公网IP失败", SaleOutSrategyEnum.REGION),
    ;

    private final String value;
    private final SaleOutSrategyEnum saleOutStategy;

    @Override
    public String toString() {
        return this.value;
    }

    public static SaleOutErrorEnum fromValue(String value) {
        for (SaleOutErrorEnum modelSaleOutEnum : SaleOutErrorEnum.values()) {
            if (modelSaleOutEnum.value.equals(value)) {
                return modelSaleOutEnum;
            }
        }
        return null;
    }

    public static String containsValue(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        for (SaleOutErrorEnum modelSaleOutEnum : SaleOutErrorEnum.values()) {
            if (value.contains(modelSaleOutEnum.value)) {
                return modelSaleOutEnum.value;
            }
        }
        return null;
    }
}
