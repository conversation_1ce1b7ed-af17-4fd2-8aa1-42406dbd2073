package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.SetServerAutoRenewResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmSetServerAutoRenewBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmSetServerAutoRenewRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetServerAutoRenewRequest extends ECloudAcsRequest<SetServerAutoRenewResponse> {
    @ApiModelProperty("设置/取消自动续订")
    @NotNull(message = "设置/取消自动续订不能为空")
    private Boolean autoRenew;

    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmSetServerAutoRenew")
                .uri("/acl/v3/server/autoRenew")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/autoRenew")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmSetServerAutoRenewRequest toRequest() {
        VmSetServerAutoRenewBody body = VmSetServerAutoRenewBody.builder()
                .serverId(this.serverId)
                .autoRenew(this.autoRenew)
                .build();
        return VmSetServerAutoRenewRequest.builder()
                .vmSetServerAutoRenewBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return SetServerAutoRenewResponse.class;
    }
}
