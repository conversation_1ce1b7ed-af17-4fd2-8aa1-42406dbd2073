package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsGetDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsGetDetailRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetDiskDetailResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDiskDetailRequest extends ECloudAcsRequest<GetDiskDetailResponse> {
    @ApiModelProperty("硬盘id")
    @NotNull(message = "硬盘id不能为空")
    private String volumeId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("queryVolumeDetail")
                .uri("/acl/v3/volume/volumeDetail/{volumeId}")
                .gatewayUri("/api/v2/volume/volume/volumeDetail/{volumeId}")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsGetDetailRequest toRequest() {
        EbsGetDetailPath path = EbsGetDetailPath.builder().volumeId(this.volumeId).build();
        return EbsGetDetailRequest.builder()
                .queryVolumeDetailPath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetDiskDetailResponse.class;
    }
}
