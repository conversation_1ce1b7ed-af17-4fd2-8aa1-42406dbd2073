package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.OsTypeEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class ImsGetCustomImagesQuery extends Query {
    @SerializedName("imageId")
    private String imageId;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("name")
    private String name;
    @SerializedName("imageOsTypes")
    private List<OsTypeEnum> imageOsTypes;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("page")
    private Integer page;
    @SerializedName("serverId")
    private String serverId;
}
