package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSecurityGroupRuleListResponse implements ECloudResponseBody {
    private Integer total;
    private Integer page;
    private Integer pageSize;
    private List<GetSecurityGroupRuleListResponseContent> content;
    private Boolean empty;
}
