package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.sdk.model.NicSet;
import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2020/8/28
 * 查看安全组关联的网卡列表
 */
public class DescribeSecurityGroupNicsResponse extends KpyRpcAcsResponse {
    private String regionId;
    private String securityGroupId;
    private NicSet Nics;

    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    public String getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(String securityGroupId) {
        this.securityGroupId = securityGroupId;
    }

    public NicSet getNics() {
        return Nics;
    }

    public void setNics(NicSet nics) {
        Nics = nics;
    }
}
