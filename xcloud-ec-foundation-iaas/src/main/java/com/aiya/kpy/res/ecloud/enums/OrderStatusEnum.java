package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Getter
@AllArgsConstructor
public enum OrderStatusEnum {
    NEW(0, "新建"),

    PENDING_REVIEW(1, "待审核"),
    FROZEN(10, "已冻结"),
    FROZEN_FAILED(11, "冻结失败"),
    CLOSING(13, "正在关闭"),
    CLOSED(14, "已关闭"),
    CLOSED_FAILED(15, "关闭失败"),
    RECOVERING(17, "正在恢复"),
    RECOVERY_FAILED(18, "恢复失败"),
    WAIT_EFFECT(19, "等待生效"),

    AUDIT_NOT_PASSED(2, "审核未通过"),
    CANCELED(20, "已取消"),
    WAIT_CHANGE(21, "等待变更"),
    CHANGING(22, "正在变更"),
    RENE<PERSON>(28, "已续订"),
    CLOSED_PENDING_DELETE(29, "关闭待删除"),

    WAIT_ACTIVE(3, "等待开通"),
    REFUNDING(31, "退款中"),
    REFUND_FAILED(35, "退款失败"),
    REFUND_SUCCESS(36, "退款成功"),

    OPENING(4, "正在开通"),
    OPENING_PENDING_FEEDBACK(40, "开通待反馈"),
    OPENING_PENDING_ARCHIVING(41, "开通待归档"),
    UNSUBSCRIBE_PENDING_FEEDBACK(42, "退订待反馈"),
    UNSUBSCRIBE_PENDING_ARCHIVING(43, "退订待归档"),
    UNSUBSCRIBE_PENDING_AUDIT(44, "退订待审核"),
    AUDIT_WAITING_EBOSS_FEEDBACK(46, "同步审批接口成功待eboss反馈"),
    AUDIT_WAITING_EBOSS_ARCHIVING(47, "同步审批接口成功待eboss归档"),
    WAITING_FOR_PAYMENT(48, "等待支付"),
    FROZEN_PENDING_FEEDBACK(49, "冻结待反馈"),

    OPENING_FOR_SYNC(5, "开通待同步"),
    RENEWAL_PENDING_FEEDBACK(50, "恢复待反馈"),
    RENEWAL_PENDING_SUBSCRIPTION(51, "冻结待续订"),
    UNSUBSCRIBE_FOR_SYNC(52, "冻结待续订"),
    RENEWING(53, "正在续订"),
    UPGRADING_TO_COMMERCIAL_USE(54, "正在试用转商用"),
    RENEWAL_PENDING_RESOURCE_RECOVERY(55, "续订待资源恢复"),
    REFUND_SUCCESS_AND_RESOURCE_RETIRED(56, "退款成功且资源清退成功"),
    CHANGE_CLOSING(57, "变更关闭中"),
    CHANGED(58, "已变更"),
    CHANGE_FAILED(59, "变更失败"),

    OPENED(6, "已开通"),
    COUPON_BINDING_PENDING(63, "等待绑券"),
    COUPON_BINDING(64, "正在绑券"),
    COUPON_BOUND(65, "已绑券"),
    COUPON_BINDING_FAILED(66, "绑券失败"),

    OPEN_FAILED(7, "开通失败"),
    FREEZING(9, "正在冻结"),
    ;

    private Integer code;
    private String value;

    public static OrderStatusEnum fromCode(Integer code) {
        for (OrderStatusEnum status : values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return null;
    }
}
