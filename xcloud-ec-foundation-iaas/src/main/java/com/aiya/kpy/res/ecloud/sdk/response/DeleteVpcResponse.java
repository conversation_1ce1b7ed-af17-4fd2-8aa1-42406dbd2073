package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteOrderExtResps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class DeleteVpcResponse implements ECloudResponseBody {
    @ApiModelProperty("退订返回的订单ID")
    private String orderId;
    @ApiModelProperty("退订返回订单包含的订单项列表")
    private List<VpcDeleteOrderExtResps> orderExtResps;
}
