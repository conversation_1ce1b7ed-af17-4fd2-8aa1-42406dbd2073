package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class VpcDeleteOrderExtResps {
    @ApiModelProperty("VPC订单项ID")
    private String orderExtId;
    @ApiModelProperty("VPC退订产品类型")
    private EipProductTypeEnum productType = EipProductTypeEnum.ROUTER;
}
