package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteMopBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteMopRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteVpcResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteVpcRequest extends ECloudAcsRequest<DeleteVpcResponse> {
    @ApiModelProperty("VPC的路由id")
    @NotNull(message = "VPC的路由id不能为空")
    private String resourceId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("commonMopOrderDeleteVpc")
                .uri("/customer/v3/order/delete")
                .gatewayUri("/api/openapi-vpc/customer/v3/order/delete")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcDeleteMopRequest toRequest() {
        VpcDeleteMopBody body = VpcDeleteMopBody.builder()
                .productType(EipProductTypeEnum.ROUTER)
                .resourceId(this.resourceId)
                .build();
        return VpcDeleteMopRequest.builder()
                .commonMopOrderDeleteVpcBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteVpcResponse.class;
    }
}
