package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChargeTypeEnum;
import com.aiya.kpy.res.ecloud.enums.FeeTypeEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class ImsGetServerPublicImagesQuery extends Query {
    @SerializedName("specsName")
    private String specsName;

    @SerializedName("chargeType")
    private ChargeTypeEnum chargeType;

    @SerializedName("feeType")
    private FeeTypeEnum feeType;

}
