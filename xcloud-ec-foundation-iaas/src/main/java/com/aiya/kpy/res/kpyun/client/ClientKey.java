package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;

/**
 * <AUTHOR> on 2017/4/6.
 */
public class ClientKey {

	/**
	 * 访问阿里云的帐号
	 */
	private final String accessKeyId;

	/**
	 * 访问阿里云的密码
	 */
	private final String secret;

	/**
	 * 该产品默认的节点，注意：因为目前阿里的产品我们只接了福建节点，所以只有一个，后面如果多个，得调整下代码结构，根据regions生成多个节点的接入点
	 * ，在请求的时候传具体节点
	 */
	private final String defaultRegion;

	/**
	 * 产品
	 */
	private final KpyunProduct product;
	/**
	 * 域名
	 */
	private final String domain;

	/**
	 * 可用区编码
	 */
	private final String zoneCode;

	public ClientKey(String zoneCode, String accessKeyId, String secret, String defaultRegion, KpyunProduct product, String domain) {
		super();
		this.accessKeyId = accessKeyId;
		this.secret = secret;
		this.defaultRegion = defaultRegion;
		this.product = product;
		this.domain = domain;
		this.zoneCode = zoneCode;
	}

	public String getAccessKeyId() {
		return accessKeyId;
	}

	public String getSecret() {
		return secret;
	}

	public String getDefaultRegion() {
		return defaultRegion;
	}

	public KpyunProduct getProduct() {
		return product;
	}

	public String getDomain() {
		return domain;
	}

	public String getZoneCode() {
		return zoneCode;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((accessKeyId == null) ? 0 : accessKeyId.hashCode());
		result = prime * result + ((defaultRegion == null) ? 0 : defaultRegion.hashCode());
		result = prime * result + ((domain == null) ? 0 : domain.hashCode());
		result = prime * result + ((product == null) ? 0 : product.hashCode());
		result = prime * result + ((secret == null) ? 0 : secret.hashCode());
		result = prime * result + ((zoneCode == null) ? 0 : zoneCode.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj) {
			return true;
		}
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		ClientKey other = (ClientKey) obj;
		if (accessKeyId == null) {
			if (other.accessKeyId != null) {
				return false;
			}
		} else if (!accessKeyId.equals(other.accessKeyId)) {
			return false;
		}
		if (defaultRegion == null) {
			if (other.defaultRegion != null) {
				return false;
			}
		} else if (!defaultRegion.equals(other.defaultRegion)) {
			return false;
		}
		if (domain == null) {
			if (other.domain != null) {
				return false;
			}
		} else if (!domain.equals(other.domain)) {
			return false;
		}
		if (product != other.product) {
			return false;
		}
		if (secret == null) {
			if (other.secret != null) {
				return false;
			}
		} else if (!secret.equals(other.secret)) {
			return false;
		}
		if (zoneCode == null) {
			return other.zoneCode == null;
		} else {
			return zoneCode.equals(other.zoneCode);
		}
	}
}
