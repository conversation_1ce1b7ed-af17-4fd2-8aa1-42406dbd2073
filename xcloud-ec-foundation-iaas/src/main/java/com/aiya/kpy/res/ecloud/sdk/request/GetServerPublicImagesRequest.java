package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChargeTypeEnum;
import com.aiya.kpy.res.ecloud.enums.FeeTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetServerPublicImagesResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetServerPublicImagesQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetServerPublicImagesRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetServerPublicImagesRequest extends ECloudAcsRequest<GetServerPublicImagesResponse> {
    @ApiModelProperty("主机规格名称")
    @NotNull(message = "主机规格名称不能为空")
    private String specsName;

    @ApiModelProperty("付费方式")
    private ChargeTypeEnum chargeType;

    @ApiModelProperty("缴费周期")
    private FeeTypeEnum feeType;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listServerPublicImageV2")
                .uri("/image/public")
                .gatewayUri("/api/openapi-ims/user/v5/image/public")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public ImsGetServerPublicImagesRequest toRequest() {
        ImsGetServerPublicImagesQuery query =
                ImsGetServerPublicImagesQuery.builder()
                        .specsName(this.specsName)
                        .chargeType(this.chargeType)
                        .feeType(this.feeType)
                        .build();
        return ImsGetServerPublicImagesRequest.builder()
                .listServerPublicImageV2Query(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetServerPublicImagesResponse.class;
    }
}
