package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.BindTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EipBindBody extends Body {
    @SerializedName("resourceId")
    private String resourceId;
    @SerializedName("ipId")
    private String ipId;
    @SerializedName("vpoolId")
    private String vpoolId;
    @SerializedName("portId")
    private String portId;
    @SerializedName("type")
    private BindTypeEnum type;
    @SerializedName("vPoolId")
    private String vPoolId;
    @SerializedName("vaz")
    private String vaz;
}
