package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ScaleEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetVpcListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetVpcListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetVpcListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetVpcListRequest extends ECloudAcsRequest<GetVpcListResponse> {
    @ApiModelProperty("VPC是否绑定了NAT网关")
    private Boolean natGatewayBind;

    @ApiModelProperty("VPC是否可见")
    private Boolean visible;

    @ApiModelProperty("VPC规格")
    private ScaleEnum scale;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("标签ID列表")
    private List<String> tagIds;

    @ApiModelProperty("列表分页大小")
    private Integer pageSize;

    @ApiModelProperty("列表分页页数")
    private Integer page;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listVpc")
                .uri("/customer/v3/vpc")
                .gatewayUri("/api/openapi-vpc/customer/v3/vpc")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmGetVpcListRequest toRequest() {
        VmGetVpcListQuery query = VmGetVpcListQuery.builder()
                .natGatewayBind(this.natGatewayBind)
                .visible(this.visible)
                .scale(this.scale)
                .region(this.region)
                .tagIds(this.tagIds)
                .pageSize(this.pageSize)
                .page(this.page)
                .build();
        return VmGetVpcListRequest.builder()
                .listVpcQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetVpcListResponse.class;
    }
}
