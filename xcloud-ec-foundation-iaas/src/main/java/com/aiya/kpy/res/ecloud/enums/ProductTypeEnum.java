package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 产品类型
 *
 * <AUTHOR>
 * @date 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum ProductTypeEnum {
    NORMAL("NORMAL"),
    DE_CLOUD("DE_CLOUD"),
    AUTOSCALING("AUTOSCALING"),
    VO("VO"),
    CDN("CDN"),
    PAAS_MASTER("PAAS_MASTER"),
    PAAS_SLAVE("PAAS_SLAVE"),
    VCPE("VCPE"),
    EMR("EMR"),
    LOGAUDIT("LOGAUDIT"),
    MSE("MSE"),
    BASTION("BASTION"),
    NEXT_GENERATION_FIREWALL("NEXT_GENERATION_FIREWALL");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
