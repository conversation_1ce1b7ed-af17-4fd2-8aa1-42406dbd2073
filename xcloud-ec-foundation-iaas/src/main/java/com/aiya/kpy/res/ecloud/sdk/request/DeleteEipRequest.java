package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipDeleteMopBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipDeleteMopRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteEipResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteEipRequest extends ECloudAcsRequest<DeleteEipResponse> {
    @ApiModelProperty("退订的弹性公网IP ID")
    @NotNull(message = "退订的弹性公网IP ID不能为空")
    private String relatedResourceId;

    @ApiModelProperty("退订的弹性公网IP所关联的带宽ID")
    @NotNull(message = "退订的弹性公网IP所关联的带宽ID不能为空")
    private String resourceId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("commonMopOrderDeleteEip")
                .uri("/customer/v3/order/delete")
                .gatewayUri("/api/openapi-eip/customer/v3/order/delete")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipDeleteMopRequest toRequest() {
        EipDeleteMopBody body = EipDeleteMopBody.builder()
                .productType(EipProductTypeEnum.IP)
                .resourceId(this.resourceId)
                .relatedResourceId(this.relatedResourceId)
                .build();
        return EipDeleteMopRequest.builder()
                .commonMopOrderDeleteEipBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteEipResponse.class;
    }
}
