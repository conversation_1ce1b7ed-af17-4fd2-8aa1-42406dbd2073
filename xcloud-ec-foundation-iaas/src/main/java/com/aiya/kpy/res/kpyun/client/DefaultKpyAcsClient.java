/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.auth.Credential;
import com.aiya.kpy.res.kpyun.auth.ISigner;
import com.aiya.kpy.res.kpyun.enums.FormatType;
import com.aiya.kpy.res.kpyun.exception.ClientException;
import com.aiya.kpy.res.kpyun.exception.KpyAcsError;
import com.aiya.kpy.res.kpyun.exception.ServerException;
import com.aiya.kpy.res.kpyun.parser.ResponseParser;
import com.aiya.kpy.res.kpyun.profile.DefaultProfile;
import com.aiya.kpy.res.kpyun.profile.Endpoint;
import com.aiya.kpy.res.kpyun.profile.IClientProfile;
import com.aiya.kpy.res.kpyun.profile.ProductDomain;
import com.aiya.kpy.res.kpyun.request.HttpRequest;
import com.aiya.kpy.res.kpyun.request.KpyAcsRequest;
import com.aiya.kpy.res.kpyun.response.HttpResponse;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;
import com.alibaba.fastjson.JSON;
import lombok.Data;

import java.io.IOException;
import java.net.SocketTimeoutException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
@Data
public class DefaultKpyAcsClient implements IKpyAcsClient {
    private static final Logger logger = Logger.getLogger(DefaultKpyAcsClient.class.getName());
    private int maxRetryNumber = 3;
    private boolean autoRetry = true;
    //	private Integer connectTimeOut = 5000;
    private Integer connectTimeOut = 10000;
    private Integer readTimeOut = 60000;
    private IClientProfile clientProfile = null;

    public DefaultKpyAcsClient() {
        this.clientProfile = DefaultProfile.getProfile();
    }

    public DefaultKpyAcsClient(IClientProfile profile) {
        this.clientProfile = profile;
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request)
            throws ClientException, ServerException {
        return this.doAction(request, autoRetry, maxRetryNumber, this.clientProfile);
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request,
                                                            boolean autoRetry, int maxRetryCounts) throws ClientException, ServerException {
        return this.doAction(request, autoRetry, maxRetryCounts, this.clientProfile);
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, IClientProfile profile)
            throws ClientException, ServerException {
        return this.doAction(request, this.autoRetry, this.maxRetryNumber, profile);
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, String regionId, Credential credential)
            throws ClientException, ServerException {
        boolean retry = this.autoRetry;
        int retryNumber = this.maxRetryNumber;
        ISigner signer = null;
        FormatType format = null;
        List<Endpoint> endpoints = null;
        if (null != this.clientProfile) {
            signer = clientProfile.getSigner();
            format = clientProfile.getFormat();
            try {
                endpoints = clientProfile.getEndpoints(request.getAcsProduct());
            } catch (Throwable e) {
                endpoints = clientProfile.getEndpoints();
            }
        }

        return this.doAction(request, retry, retryNumber, regionId, credential, signer, format, endpoints);
    }

    @Override
    public <T extends KpyAcsResponse> T getAcsResponse(KpyAcsRequest<T> request)
            throws ServerException, ClientException {
        HttpResponse baseResponse = this.doAction(request);
        return parseAcsResponse(request.getResponseClass(), baseResponse);
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, boolean autoRetry,
                                                            int maxRetryCounts, IClientProfile profile) throws ClientException, ServerException {
        if (null == profile) {
            throw new ClientException("SDK.InvalidProfile", "No active profile found.");
        }
        String region = profile.getRegionId();
        Credential credential = profile.getCredential();
        ISigner signer = profile.getSigner();
        FormatType format = profile.getFormat();
        List<Endpoint> endpoints;
        try {
            endpoints = clientProfile.getEndpoints(request.getAcsProduct());
        } catch (Throwable e) {
            endpoints = clientProfile.getEndpoints();
        }
        return this.doAction(request, autoRetry, maxRetryCounts, region, credential, signer, format, endpoints);
    }

    private <T extends KpyAcsResponse> T parseAcsResponse(Class<T> clasz, HttpResponse baseResponse)
            throws ServerException, ClientException {

        FormatType format = baseResponse.getContentType();

        if (baseResponse.isSuccess()) {
            return readResponse(clasz, baseResponse, format);
        } else {
            KpyAcsError error = readError(baseResponse, format);
            if (500 <= baseResponse.getStatus()) {
                throw new ServerException(error.getCode(), error.getMessage(), error.getRequestId());
            } else {
                throw new ClientException(error.getCode(), error.getMessage(), error.getRequestId());
            }
        }
    }

    @Override
    public <T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request,
                                                            boolean autoRetry, int maxRetryNumber,
                                                            String regionId, Credential credential,
                                                            ISigner signer, FormatType format,
                                                            List<Endpoint> endpoints) throws ClientException, ServerException {
        try {
            FormatType requestFormatType = request.getAcsAcceptFormat();
            if (null != requestFormatType) {
                format = requestFormatType;
            }
            if (null == request.getAcsRegionId()) {
                request.setAcsRegionId(regionId);
            }
            ProductDomain domain = Endpoint.findProductDomain(regionId, request.getAcsProduct(), endpoints);
            if (null == domain) {
                throw new ClientException("SDK.InvalidRegionId", "Can not find endpoint to access.");
            }
            HttpRequest httpRequest = request.signRequest(signer, credential, format, domain);
            httpRequest.setConnectTimeout(this.getConnectTimeOut());
            httpRequest.setReadTimeout(this.getReadTimeOut());
            int retryTimes = 1;
            logger.info("doAction Request Headers: " + JSON.toJSONString(httpRequest.getHeaderValue("traceparent")));
            HttpResponse response = HttpResponse.getResponse(httpRequest);
            while (500 <= response.getStatus() && autoRetry && retryTimes < maxRetryNumber) {
                httpRequest = request.signRequest(signer, credential, format, domain);
                httpRequest.setConnectTimeout(this.getConnectTimeOut());
                httpRequest.setReadTimeout(this.getReadTimeOut());
                response = HttpResponse.getResponse(httpRequest);
                retryTimes++;
            }
            return response;
        } catch (InvalidKeyException exp) {
            throw new ClientException("SDK.InvalidAccessSecret", "Speicified access secret is not valid.");
        } catch (SocketTimeoutException exp) {
            throw new ClientException("SDK.ServerUnreachable", "SocketTimeoutException has occurred on a socket read or accept.");
        } catch (IOException exp) {
            throw new ClientException("SDK.ServerUnreachable", "Speicified endpoint or uri is not valid.");
        } catch (NoSuchAlgorithmException exp) {
            throw new ClientException("SDK.InvalidMD5Algorithm", "MD5 hash is not supported by client side.");
        } catch (Throwable exp) {
            throw new ClientException("SDK.OtherError", exp.getMessage());
        }
    }

    private <T extends KpyAcsResponse> T readResponse(Class<T> clasz, HttpResponse httpResponse, FormatType format) throws ClientException {
        return format.getResponseParser().parser(httpResponse.getStringContent(), clasz);
    }

    private KpyAcsError readError(HttpResponse httpResponse, FormatType format) throws ClientException {
        String stringContent = httpResponse.getStringContent();
        ResponseParser parser = format.getResponseParser();
        KpyAcsError acsError = null;
        if (parser != null) {
            try {
                acsError = parser.parser(stringContent, KpyAcsError.class);
            } catch (Exception e) {
                logger.warning(e.getMessage());
            }

        }
        if (acsError == null) {
            acsError = new KpyAcsError();
            acsError.setMessage(stringContent);
        }
        acsError.setHttpStatus(httpResponse.getStatus());
        return acsError;
    }
}
