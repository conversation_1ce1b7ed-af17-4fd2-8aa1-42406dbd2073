package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class EipResponseOrderExtResps {
    @ApiModelProperty("带宽的订单项ID")
    private String relOrderExtId;

    @ApiModelProperty("弹性公网IP订单项ID")
    private String orderExtId;

    @ApiModelProperty("关联产品类型")
    private EipProductTypeEnum relProductType = EipProductTypeEnum.BANDWIDTH;

    @ApiModelProperty("带宽的订单项状态：3-等待开通；4-正在开通；5-开通待同步；6-开通成功；7-开通失败")
    private Integer relOrderExtStatus;

    @ApiModelProperty("弹性公网IP的订单项状态：3-等待开通；4-正在开通；5-开通待同步；6-开通成功；7-开通失败")
    private Integer orderExtStatus;

    @ApiModelProperty("退订产品类型")
    private EipProductTypeEnum productType = EipProductTypeEnum.IP;
}
