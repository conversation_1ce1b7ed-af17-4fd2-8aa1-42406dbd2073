package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum ServerVmTypeEnum {
    HIGHIO("highIO"),
    EXCLUSIVE("exclusive"),
    MEMI<PERSON>ROVE("memImprove"),
    COMM<PERSON>("common"),
    GPU("gpu"),
    HIGHPERFORMANCE("highPerformance"),
    COMMONINTRODUCTORY("commonIntroductory"),
    COMMONNETIMPROVE("commonNetImprove"),
    COMPUTE("compute"),
    COMPUTENETIMPROVE("computeNetImprove"),
    MEMNETIMPROVE("memNetImprove"),
    LOCALSTORAGE("localStorage"),
    XLARGEMEMORY("xlargeMemory"),
    HIGHFREQUENCY("highFrequency"),
    VGPU("vgpu"),
    FPGA("fpga"),
    NORMALCOMPUTEIMPROVE("normalComputeImprove"),
    NORMALNETENHANCE("normalNetEnhance"),
    STOREENHANC<PERSON>("storeEnhance"),
    COMPUTEENHANCE("computeEnhance"),
    EDGECOMMONNETIMPROVE("edgeCommonNetImprove"),
    EDGEGPU("edgeGPU"),
    GENERAL("general"),
    NPU("npu"),
    POP("pop"),
    MEMORYDOUBLECARD("memoryDoubleCard"),
    COMPUTEG2("computeG2"),
    MEMORYHBACARD("memoryHBACard"),
    LOCALSTORAGELOW("localStorageLow"),
    LOCALSTORAGEDOUBLECARD("localStorageDoubleCard"),
    SUPERCOMPUTE("superCompute"),
    EDGEBALANCE("edgeBalance"),
    EDGECOMPUTE("edgeCompute"),
    EDGELOCALSTORAGE("edgeLocalStorage"),
    EDGECDN("edgeCdn"),
    EDGEMEM("edgeMem"),
    EDGECOMPUTEVM("edgeComputeVM"),
    EDGEDOMESTICARM("edgeDomesticARM"),
    EDGEDOMESTICX86("edgeDomesticX86"),
    UNRECOGNIZED("UNRECOGNIZED");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
