package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.DirectionEnum;
import com.aiya.kpy.res.ecloud.enums.EtherTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ProtocolEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
public class GetSecurityGroupRuleDetailResponse implements ECloudResponseBody {
    @ApiModelProperty("远端安全组ID")
    private String aimSgid;

    @ApiModelProperty("最小端口号")
    private Integer minPortRange;

    @ApiModelProperty("所属安全组ID")
    private String secgroupId;

    @ApiModelProperty("安全组规则描述")
    private String description;

    @ApiModelProperty("CIDR网络地址")
    private String remoteIpPrefix;

    @ApiModelProperty("是否是默认安全组规则")
    private Boolean defaultRule;

    @ApiModelProperty("协议类型")
    private ProtocolEnum protocol;

    @ApiModelProperty("IP协议类型")
    private EtherTypeEnum etherType;

    @ApiModelProperty("安全组规则创建时间")
    private String createdTime;

    @ApiModelProperty("安全组规则ID")
    private String id;

    @ApiModelProperty("安全组匹配流量的方向")
    private DirectionEnum direction;

    @ApiModelProperty("最大端口号")
    private Integer maxPortRange;

    @ApiModelProperty("安全组规则状态")
    private Integer status;
}
