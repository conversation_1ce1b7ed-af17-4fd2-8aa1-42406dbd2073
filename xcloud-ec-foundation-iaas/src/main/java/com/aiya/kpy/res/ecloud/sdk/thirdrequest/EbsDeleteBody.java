package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class EbsDeleteBody extends Body {
    @SerializedName("resourceId")
    private String resourceId;
    @SerializedName("resourceType")
    private ResourceTypeEnum resourceType;
}
