package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.DiskTypeEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmCreateRequestDataVolume {
    @SerializedName("size")
    private Integer size;
    @SerializedName("isShare")
    private Boolean isShare;
    @SerializedName("resourceType")
    private DiskTypeEnum resourceType;
}
