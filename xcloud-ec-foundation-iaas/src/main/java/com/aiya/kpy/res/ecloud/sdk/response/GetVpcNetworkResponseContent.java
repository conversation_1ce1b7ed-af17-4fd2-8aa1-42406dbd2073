package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.EcStatusEnum;
import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcNetworkResponseSubnets;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class GetVpcNetworkResponseContent {
    @ApiModelProperty("是否是共享的")
    private Boolean shared;

    @ApiModelProperty("底层状态")
    private EcStatusEnum ecStatus;

    @ApiModelProperty("资源来源")
    private String orderSource;

    private String vpoolId;

    @ApiModelProperty("自定义路由表名称")
    private String routTableName;

    @ApiModelProperty("是否可用")
    private Boolean enabled;

    private String vaz;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("是否已删除")
    private Boolean deleted;

    @ApiModelProperty("路由Id")
    private String routerId;

    @ApiModelProperty("网络名称")
    private String name;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("网络返回体中的子网信息")
    private List<VpcNetworkResponseSubnets> subnets;

    @ApiModelProperty("网络id")
    private String id;

    @ApiModelProperty("自定义路由表Id")
    private String routerTableId;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("网络的类型, 云主机类型网络不可绑定物理机, 反之亦然")
    private NetworkTypeEnum networkTypeEnum;

    private String vPoolId;
}
