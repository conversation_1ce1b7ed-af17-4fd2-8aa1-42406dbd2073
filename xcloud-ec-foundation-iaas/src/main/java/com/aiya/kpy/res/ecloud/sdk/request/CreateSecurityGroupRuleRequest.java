package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.DirectionEnum;
import com.aiya.kpy.res.ecloud.enums.EtherTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ProtocolEnum;
import com.aiya.kpy.res.ecloud.enums.RemoteTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateSecurityGroupRuleResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateSecurityGroupRuleBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateSecurityGroupRuleRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSecurityGroupRuleRequest extends ECloudAcsRequest<CreateSecurityGroupRuleResponse> {
    @ApiModelProperty("安全组规则描述，100字符以内")
    private String description;

    @ApiModelProperty("安全组匹配流量的方向")
    @NotNull(message = "安全组匹配流量的方向不能为空")
    private DirectionEnum direction;

    @SerializedName("ip类型")
    private EtherTypeEnum etherType;

    @ApiModelProperty("安全组id")
    @NotNull(message = "安全组id不能为空")
    private String securityGroupId;

    @ApiModelProperty("最大端口号")
    private Integer maxPortRange;

    @ApiModelProperty("最小端口号")
    private Integer minPortRange;

    @ApiModelProperty("协议类型")
    @NotNull(message = "协议类型不能为空")
    private ProtocolEnum protocol;

    @ApiModelProperty("CIDR网络地址，remoteType为cidr时必填")
    private String remoteIpPrefix;

    @ApiModelProperty("远端安全组ID，remoteType为security_group时必填")
    private String remoteSecurityGroupId;

    @ApiModelProperty("授权类型")
    @NotNull(message = "授权类型不能为空")
    private RemoteTypeEnum remoteType;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("createSecurityGroupRule")
                .uri("/customer/v3/SecurityGroupRule")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroupRule")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcCreateSecurityGroupRuleRequest toRequest() {
        VpcCreateSecurityGroupRuleBody body = VpcCreateSecurityGroupRuleBody.builder()
                .description(description)
                .direction(direction)
                .etherType(etherType)
                .securityGroupId(securityGroupId)
                .maxPortRange(maxPortRange)
                .minPortRange(minPortRange)
                .protocol(protocol)
                .remoteIpPrefix(remoteIpPrefix)
                .remoteSecurityGroupId(remoteSecurityGroupId)
                .remoteType(remoteType)
                .build();
        return VpcCreateSecurityGroupRuleRequest.builder()
                .createSecurityGroupRuleBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateSecurityGroupRuleResponse.class;
    }
}
