package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetFlavorByRegionQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetFlavorByRegionRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetFlavorByRegionResponseContent;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetFlavorByRegionRequest extends ECloudAcsRequest<GetFlavorByRegionResponseContent> {
    @ApiModelProperty("商品编码")
    @NotNull(message = "商品编码不能为空")
    private String offerId;

    @ApiModelProperty("可用区")
    @NotNull(message = "可用区不能为空")
    private String region;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmgetFlavorByRegion")
                .uri("/acl/v3/server/specsName")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/specsName")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmGetFlavorByRegionRequest toRequest() {
        return VmGetFlavorByRegionRequest.builder()
                .vmgetFlavorByRegionQuery(VmGetFlavorByRegionQuery.builder()
                        .region(this.region)
                        .offerId(this.offerId)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetFlavorByRegionResponseContent.class;
    }
}
