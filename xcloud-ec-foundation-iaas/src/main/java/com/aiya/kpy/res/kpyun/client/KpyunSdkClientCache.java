package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.entity.ResZoneProviderAccountEntity;
import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR> on 2017/4/6.
 */
@Component
public class KpyunSdkClientCache implements ApplicationContextAware {

	private static final ConcurrentMap<ClientKey, KpyunSdkClient> CLIENT_CACHE_MAP = new ConcurrentHashMap<>();
	private ApplicationContext applicationContext;

	public KpyunSdkClient getCacheKpyunSdkClient(ResZoneProviderAccountEntity account, KpyunProduct product) {
		Assert.notNull(account, "account must not be null");
		Assert.notNull(product, "product must not be null");
		Assert.hasText(account.getAccessKeyId(), "accessKeyId must not be null");
		Assert.hasText(account.getAccessKeySecret(), "accessKeySecret must not be null");
		Assert.hasText(account.getRegionCode(), "regionCode must not be null");
		Assert.hasText(account.getReqUrl(), "reqUrl must not be null");

		ClientKey key = new ClientKey(account.getZone(), account.getAccessKeyId(), account.getAccessKeySecret(), account.getRegionCode(),
				product, account.getReqUrl());
		if (!CLIENT_CACHE_MAP.containsKey(key)) {
			KpyunSdkClient client = applicationContext.getBean(KpyunSdkClient.class, key);
			CLIENT_CACHE_MAP.put(key, client);
		}
		return CLIENT_CACHE_MAP.get(key);
	}


	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		this.applicationContext = applicationContext;
	}
}
