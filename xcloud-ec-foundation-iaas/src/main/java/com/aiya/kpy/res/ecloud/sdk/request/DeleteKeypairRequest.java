package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmDeleteKeyPairPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmDeleteKeyPairRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteKeypairResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteKeypairRequest extends ECloudAcsRequest<DeleteKeypairResponse> {
    @ApiModelProperty("密钥对Id")
    @NotNull(message = "密钥对Id不能为空")
    private String id;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmDeleteKeyPair")
                .uri("/customer/v3/keypair/{id}")
                .gatewayUri("/api/openapi-ecs/customer/v3/keypair/{id}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmDeleteKeyPairRequest toRequest() {
        return VmDeleteKeyPairRequest.builder()
                .vmDeleteKeyPairPath(VmDeleteKeyPairPath.builder().id(this.id).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteKeypairResponse.class;
    }
}
