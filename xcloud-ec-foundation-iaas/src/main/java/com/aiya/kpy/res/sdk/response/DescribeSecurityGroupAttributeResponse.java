package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.sdk.model.PermissionType;
import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

/**
 * Created by ChenXing on 2017/4/10.
 * 查询安全组规则
 */
public class DescribeSecurityGroupAttributeResponse extends KpyRpcAcsResponse {
	private String securityGroupId;
	private String securityGroupName;
	private String regionId;
	private PermissionType[] permissions;

	public String getSecurityGroupId() {
		return securityGroupId;
	}

	public void setSecurityGroupId(String securityGroupId) {
		this.securityGroupId = securityGroupId;
	}

	public String getSecurityGroupName() {
		return securityGroupName;
	}

	public void setSecurityGroupName(String securityGroupName) {
		this.securityGroupName = securityGroupName;
	}

	public String getRegionId() {
		return regionId;
	}

	public void setRegionId(String regionId) {
		this.regionId = regionId;
	}

	public PermissionType[] getPermissions() {
		return permissions;
	}

	public void setPermissions(PermissionType[] permissions) {
		this.permissions = permissions;
	}
}
