package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsMountBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsMountRequest;
import com.aiya.kpy.res.ecloud.sdk.response.MountDiskResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MountDiskRequest extends ECloudAcsRequest<MountDiskResponse> {
    @ApiModelProperty("硬盘id")
    @NotNull(message = "硬盘id不能为空")
    private String volumeId;

    @ApiModelProperty("主机ID")
    @NotNull(message = "主机ID不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("mount")
                .uri("/acl/v3/volume/mount")
                .gatewayUri("/api/ebs/acl/v3/volume/mount")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsMountRequest toRequest() {
        return EbsMountRequest.builder()
                .mountBody(EbsMountBody.builder()
                        .serverId(this.serverId)
                        .volumeId(this.volumeId)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return MountDiskResponse.class;
    }
}
