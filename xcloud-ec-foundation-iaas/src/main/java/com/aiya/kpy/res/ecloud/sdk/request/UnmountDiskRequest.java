package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsUnmountBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsUnmountRequest;
import com.aiya.kpy.res.ecloud.sdk.response.UnmountDiskResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnmountDiskRequest extends ECloudAcsRequest<UnmountDiskResponse> {
    @ApiModelProperty("硬盘id")
    @NotNull(message = "硬盘id不能为空")
    private String volumeId;

    @ApiModelProperty("主机ID")
    @NotNull(message = "主机ID不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("unmount")
                .uri("/acl/v3/volume/unmount")
                .gatewayUri("/api/ebs/acl/v3/volume/unmount")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();

    }

    @Override
    public EbsUnmountRequest toRequest() {
        return EbsUnmountRequest.builder()
                .unmountBody(EbsUnmountBody.builder()
                        .serverId(this.serverId)
                        .volumeId(this.volumeId)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return UnmountDiskResponse.class;
    }
}
