package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum BandwidthTypeEnum {
    /**
     * 共享带宽
     */
    SHARED("shared"),
    /**
     * 普通带宽
     */
    EXCLUSIVE("exclusive");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
