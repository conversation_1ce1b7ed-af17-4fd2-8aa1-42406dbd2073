package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 云主机OP侧状态
 *
 * <AUTHOR>
 * @date 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum OpStatusEnum {
    CREATING("CREATING"),
    OK("OK"),
    REBOOTING("REBOOTING"),
    HARD_REBOOTING("HARD_REBOOTING"),
    FRO<PERSON><PERSON>("FROZEN"),
    BACKUP_REBUILDING("BACKUP_REBUILDING"),
    REBUILDING("REBUILDING"),
    RESIZING("RESIZING"),
    SHELVING("SHELVING"),
    UNSHELVING("UNSHELVING"),
    STARTING("STARTING"),
    STOPING("STOPING"),
    HARD_STOPING("HARD_STOPING"),
    PASSWORD_UPDATING("PASSWORD_UPDATING"),
    BACKUPING("BACKUPING"),
    IMAGE_CREATING("IMAGE_CREATING"),
    UNRECOGNIZED("UNRECOGNIZED"),
    BOOT_VOLUME_RESIZING("BOOT_VOLUME_RESIZING"),
    DELETING("DELETING"),
    SECOND_DELETING("SECOND_DELETING"),
    MIGRATING("MIGRATING"),
    CLONE("CLONE"),
    ORDERGET("ORDERGET");
    
    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
