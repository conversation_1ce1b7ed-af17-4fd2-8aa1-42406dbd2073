package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum BindTypeEnum {
    /**
     * 云主机
     */
    VM("vm"),
    /**
     * 裸金属
     */
    IRONIC("ironic"),
    /**
     * 负载均衡
     */
    ELB("elb"),
    /**
     * 带宽
     */
    BW("bw"),
    /**
     * 共享带宽
     */
    SBW("sbw"),
    /**
     * 云数据库RDS
     */
    RDS("rds"),
    /**
     * HA VIP
     */
    HAVIP("havip"),
    /**
     * 云堡垒机
     */
    BASTION("bastion"),
    /**
     * SNAT规则
     */
    SNAT("snat"),
    /**
     * DNAT规则
     */
    DNAT("dnat"),
    /**
     * SSL VPN
     */
    SSLVPN("sslvpn"),
    /**
     * IPSec VPN
     */
    IPSECVPN("ipsecvpn"),
    /**
     * 专属云
     */
    VMWARE("vmware"),
    /**
     * 日志审计
     */
    LOGAUDIT("logaudit"),
    /**
     * 弹性裸金属
     */
    EBM("ebm"),
    /**
     * 虚拟网卡
     */
    PORT("port"),
    /**
     * 私有服务
     */
    PRIVATESERVICE("privateservice"),
    /**
     * 辅助私网IP
     */
    SECONDARYPORTIP("secondaryPortIp"),
    /**
     * 云数据库MySQL
     */
    MYSQL("mysql"),
    /**
     * 数据库审计
     */
    DBAUDIT("dbaudit"),
    /**
     * 云数据库Redis
     */
    REDIS("redis");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
