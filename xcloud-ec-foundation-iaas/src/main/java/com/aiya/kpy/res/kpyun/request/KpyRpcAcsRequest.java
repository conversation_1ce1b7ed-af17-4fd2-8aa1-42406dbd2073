/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.request;

import com.aiya.kpy.res.kpyun.auth.Credential;
import com.aiya.kpy.res.kpyun.auth.ISigner;
import com.aiya.kpy.res.kpyun.auth.RpcSignatureComposer;
import com.aiya.kpy.res.kpyun.enums.FormatType;
import com.aiya.kpy.res.kpyun.enums.MethodType;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.parser.KpyParamConvertor;
import com.aiya.kpy.res.kpyun.profile.ProductDomain;
import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.security.InvalidKeyException;
import java.util.HashMap;
import java.util.Map;

/**
 * @param <T>
 * <AUTHOR>
 */
public abstract class KpyRpcAcsRequest<T extends KpyRpcAcsResponse> extends KpyAcsRequest<T> {

    public KpyRpcAcsRequest(String product) {
        super(product);
        initialize();
    }

    public KpyRpcAcsRequest(String product, String version) {
        super(product);
        this.setAcsVersion(version);
        initialize();
    }

    public KpyRpcAcsRequest(String product, String version, String action) {
        super(product);
        this.setAcsVersion(version);
        this.setAcsActionName(action);
        if (Version.SDK_VERSION_HEADER_VALUE.equals(version)) {
            // 目前只有gpu主机创建走的2.0网关 更新显卡数量也走
            this.headers.put(Version.SDK_VERSION_HEADER_KEY, Version.SDK_VERSION_HEADER_VALUE);
        }
        initialize();
    }

    private void initialize() {
        this.setMethod(MethodType.GET);
        this.setAcsAcceptFormat(FormatType.JSON);
        this.composer = RpcSignatureComposer.getComposer();
    }

    @Override
    public void setAcsActionName(String actionName) {
        super.setAcsActionName(actionName);
        this.putQueryParameter("Action", actionName);
    }

    @Override
    public void setAcsVersion(String version) {
        super.setAcsVersion(version);
        this.putQueryParameter("Version", version);
    }

    @Override
    public void setAcsSecurityToken(String securityToken) {
        super.setAcsSecurityToken(securityToken);
        this.putQueryParameter("SecurityToken", securityToken);
    }

    @Override
    public void setAcsAcceptFormat(FormatType acceptFormat) {
        super.setAcsAcceptFormat(acceptFormat);
        this.putQueryParameter("Format", acceptFormat.toString());
    }

    @Override
    public String composeUrl(String endpoint, Map<String, String> queries) throws UnsupportedEncodingException {
        Map<String, String> mapQueries = (queries == null) ? this.getQueryParameters() : queries;
        StringBuilder urlBuilder = new StringBuilder("");
        urlBuilder.append(this.getAcsProtocol().toString());
        urlBuilder.append("://").append(endpoint);
        if (-1 == urlBuilder.indexOf("?")) {
            urlBuilder.append("/?");
        }
        String query = concatQueryString(mapQueries);
        return urlBuilder.append(query).toString();
    }

    @Override
    public HttpRequest signRequest(ISigner signer, Credential credential, FormatType format, ProductDomain domain)
            throws InvalidKeyException, IllegalStateException, UnsupportedEncodingException {
        this.initializeQueryParameters();
        Map<String, String> imutableMap = new HashMap<String, String>(this.getQueryParameters());
        if (null != signer && null != credential) {
            String accessKeyId = credential.getAccessKeyId();
            String accessSecret = credential.getAccessSecret();
            imutableMap = this.composer.refreshSignParameters(this.getQueryParameters(), signer, accessKeyId, format);
            imutableMap.put("RegionId", getAcsRegionId());
            String strToSign = this.composer.composeStringToSign(this.getMethod(), null, signer, imutableMap, null,
                    null);
            String signature = signer.signString(strToSign, accessSecret + "&");
            imutableMap.put("Signature", signature);
        }
        setUrl(this.composeUrl(domain.getDomianName(), imutableMap));
        return this;
    }

    private void initializeQueryParameters() {
        Class<?> clazz = getClass();
        BeanInfo beanInfo;
        try {
            beanInfo = Introspector.getBeanInfo(clazz);
        } catch (IntrospectionException e) {
            throw new RuntimeException(e.getMessage());
        }
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        if (propertyDescriptors == null || propertyDescriptors.length == 0) {
            return;
        }
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            Method readMethod = propertyDescriptor.getReadMethod();
            KpyParam param = readMethod.getAnnotation(KpyParam.class);
            if (param == null) {
                continue;
            }
            String paramName = param.value();
            Object value;
            try {
                value = readMethod.invoke(this);
            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException e) {
                throw new RuntimeException(e.getMessage());
            }
            if (value == null) {
                continue;
            }
            KpyParamConvertor paramConvertor = readMethod.getAnnotation(KpyParamConvertor.class);
            if (paramConvertor != null) {
                value = paramConvertor.convertor().converToString(value);
            }
            this.putQueryParameter(paramName, value);
        }
    }
}
