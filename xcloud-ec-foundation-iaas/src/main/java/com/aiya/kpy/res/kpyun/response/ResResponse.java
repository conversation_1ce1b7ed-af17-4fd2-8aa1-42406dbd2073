package com.aiya.kpy.res.kpyun.response;

import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public abstract class ResResponse implements Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -5238083307579228113L;

    /**
     * HTTP状态吗
     */
    private int httpStatusCode;

    /**
     * HTTP报文内容
     */
    private String httpErrorContent;

    /**
     * 错误编码
     */
    private String errCode;

    /**
     * 错误消息
     */
    private String errMsg;

    /**
     * 请求ID
     */
    private String requestId;

    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    public void setHttpStatusCode(int httpStatusCode) {
        this.httpStatusCode = httpStatusCode;
    }

    public String getHttpErrorContent() {
        return httpErrorContent;
    }

    public void setHttpErrorContent(String httpErrorContent) {
        this.httpErrorContent = httpErrorContent;
    }

    public String getErrCode() {
        return errCode;
    }

    public void setErrCode(String errCode) {
        this.errCode = errCode;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public boolean hasError() {
        return hasBusizError() || hasHttpError();
    }

    public boolean hasHttpError() {
        if (StringUtils.isNotBlank(httpErrorContent)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean hasBusizError() {
        if (StringUtils.isNotBlank(errCode)) {
            return true;
        } else {
            return false;
        }
    }

    public void setErroInfo(String requestId, String errCode, String errMsg) {
        this.requestId = requestId;
        this.errCode = errCode;
        this.errMsg = errMsg;
    }

    public void setHttpErrorContentInfo(String requestId, int httpStatusCode, String httpErrorContent) {
        this.requestId = requestId;
        this.httpStatusCode = httpStatusCode;
        this.httpErrorContent = httpErrorContent;

    }
}