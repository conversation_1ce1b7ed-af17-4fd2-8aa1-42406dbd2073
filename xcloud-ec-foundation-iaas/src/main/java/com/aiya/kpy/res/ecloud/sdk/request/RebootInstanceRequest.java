package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmRebootPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmRebootRequest;
import com.aiya.kpy.res.ecloud.sdk.response.RebootInstanceResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RebootInstanceRequest extends ECloudAcsRequest<RebootInstanceResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmReboot")
                .uri("/acl/v3/server/{serverId}/reboot")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/{serverId}/reboot")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmRebootRequest toRequest() {
        return VmRebootRequest.builder()
                .vmRebootPath(VmRebootPath.builder().serverId(this.serverId).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return RebootInstanceResponse.class;
    }
}
