package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.DiskTypeEnum;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmGetServerDetailResponseVolumes {
    @ApiModelProperty("数据盘大小")
    @SerializedName("size")
    private Integer size;

    @ApiModelProperty("数据盘名称")
    @SerializedName("name")
    private String name;

    @ApiModelProperty("数据盘id")
    @SerializedName("id")
    private String id;

    @ApiModelProperty("数据盘类型")
    @SerializedName("type")
    private DiskTypeEnum type;

    @ApiModelProperty("数据盘状态")
    @SerializedName("status")
    private String status;
}
