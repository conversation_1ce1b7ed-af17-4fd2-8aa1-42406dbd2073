package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum EipStatusEnum {
    /**
     * 绑定
     */
    BINDING("BINDING"),
    /**
     * 未绑定
     */
    UNBOUND("UNBOUND"),
    /**
     * 冻结
     */
    FROZEN("FROZEN"),
    /**
     * 占用
     */
    OCCUPY("OCCUPY");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
