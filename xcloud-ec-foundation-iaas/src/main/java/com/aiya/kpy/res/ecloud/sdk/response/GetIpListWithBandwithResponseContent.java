package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipBindResourceResp;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwidthResponseNatGatewayDnatRuleResps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class GetIpListWithBandwithResponseContent {
    @ApiModelProperty("计费方式")
    private String chargingMode;

    @ApiModelProperty("虚拟网卡名称")
    private String nicName;

    @ApiModelProperty("订单来源")
    private OrderTypeEnum orderType;

    @ApiModelProperty("绑定的资源ID")
    private String resourceId;

    @ApiModelProperty("虚拟可用区资源池信息")
    private String vpoolId;

    @ApiModelProperty("公网IP是否占用资源")
    private Boolean occupy;

    @ApiModelProperty("是否为共享IP")
    private Boolean sharedIp;

    @ApiModelProperty("带宽大小，单位：Kbps")
    private Integer bandwidthSize;

    @ApiModelProperty("IPv6共享带宽ID")
    private String mixedId;

    @ApiModelProperty("公网IP名称")
    private String description;

    @ApiModelProperty("公网IP关联带宽ID")
    private String bandwidthId;

    @ApiModelProperty("计费方式")
    private ChargeModeEnum chargeModeEnum;

    @ApiModelProperty("共享NAT ID")
    private String sharedNatId;

    @ApiModelProperty("带宽类型")
    private BandwidthTypeEnum bandwidthType;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("公网IP绑定的DNAT规则信息，仅绑定类型为DNAT时返回")
    private List<EipWithBandwidthResponseNatGatewayDnatRuleResps> natGatewayDnatRuleResps;

    @ApiModelProperty("IPv6地址")
    private String ipv6;

    @ApiModelProperty("关联共享带宽所属的路由ID")
    private String routerId;

    @ApiModelProperty("公网IP创建时间")
    private String createdTime;

    @ApiModelProperty("公网IP ID")
    private String id;

    @ApiModelProperty("虚拟可用区资源池信息")
    private String vPoolId;

    @ApiModelProperty("带宽大小，单位：Mbps")
    private Integer bandwidthMbSize;

    @ApiModelProperty("公网IP地址")
    private String dummyFip;

    @ApiModelProperty("资源是否可见")
    private Boolean visible;

    @ApiModelProperty("公网IP是否使用")
    private Boolean bound;

    @ApiModelProperty("公网IP绑定资源类型")
    private BindTypeEnum bindType;

    @ApiModelProperty("产品类型")
    private String fipProductType;

    @ApiModelProperty("公网IP是否冻结")
    private Boolean frozen;

    @ApiModelProperty("绑定的资源名称")
    private String resourceName;

    @ApiModelProperty("订单版本")
    private OrderVersionEnum orderVersion;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("公网IP绑定的资源信息")
    private EipBindResourceResp floatingIpBindResourceResp;

    @ApiModelProperty("网卡所属的网络ID")
    private String portNetworkId;

    @ApiModelProperty("IPv6共享带宽是否为新订购类型：true-是（IPv6共享带宽）；false-否（混合带宽")
    private Boolean newProductFlag;

    @ApiModelProperty("IPv4地址")
    private String name;

    @ApiModelProperty("是否为预分配IP")
    private Boolean preAllocatedForHY;

    @ApiModelProperty("备案状态")
    private IcpStatusEnum icpStatus;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("公网IP接入类型")
    private IpTypeEnum ipType;

    @ApiModelProperty("状态")
    private EipStatusEnum status;
}
