package com.aiya.kpy.res.kpyun.enums;

import com.aiya.kpy.res.kpyun.parser.ParameterHelper;
import com.alibaba.fastjson.JSONArray;

import java.util.Date;

/**
 *  <AUTHOR> on 2017/4/13.
 *  request参数类型转string
 */
public enum RequestParamConvertor {
	JSONArrayConvertor {
		public String converToString(Object param) {
			return param == null ? "" : JSONArray.toJSONString(param);
		}
	},
	/**
	 * 时间格式转换
	 */
	ISOUTCDateConvertor() {
		@Override
		public String converToString(Object param) {
			if(!(param instanceof Date)) {
				throw new UnsupportedOperationException("can only convert type of date!");
			}
			return ParameterHelper.getISO8601Time((Date)param);
		}
	};


	abstract public String converToString(Object param);
}
