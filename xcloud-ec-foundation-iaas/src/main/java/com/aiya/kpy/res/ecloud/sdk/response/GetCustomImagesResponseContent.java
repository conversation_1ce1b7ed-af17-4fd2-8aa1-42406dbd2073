package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
public class GetCustomImagesResponseContent {
    @ApiModelProperty("镜像说明")
    private String note;

    @ApiModelProperty("备份方式")
    private BackupWayEnum backupWay;

    @ApiModelProperty("公共镜像类型")
    private PublicImageTypeEnum publicImageType;

    @ApiModelProperty("创建镜像的主机id")
    private String serverId;

    @ApiModelProperty("备份触发类型")
    private ImsBackupTypeEnum backupType;

    @ApiModelProperty("是否按名字上升顺序排序")
    private Boolean isAscendOrder;

    @ApiModelProperty("镜像操作系统类型")
    private OsTypeEnum osType;

    @ApiModelProperty("是否是公用的镜像")
    private Integer isPublic;

    @ApiModelProperty("镜像类型")
    private ImageTypeEnum imageType;

    @ApiModelProperty("开启多网卡")
    private String hwVifMultiQueueEnabled;

    @ApiModelProperty("镜像别名")
    private String imageAlias;

    @ApiModelProperty("镜像id")
    private String imageId;

    @ApiModelProperty("快照Id")
    private String snapshotId;

    @ApiModelProperty("源镜像id")
    private String sourceImageId;

    @ApiModelProperty("可支持创建主机类型")
    private String supportVmType;

    @ApiModelProperty("镜像操作系统名称")
    private String osName;

    @ApiModelProperty("最小系统盘大小")
    private Integer minDisk;

    @ApiModelProperty("镜像下载地址")
    private String url;

    @ApiModelProperty("是否已删除")
    private Boolean deleted;

    @ApiModelProperty("镜像大小")
    private Long size;

    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("主机类型")
    private ServerTypeEnum serverType;

    @ApiModelProperty("镜像名称")
    private String name;

    @ApiModelProperty("进度条信息")
    private String progressInfo;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("镜像状态")
    private ImsStatusEnum status;
}
