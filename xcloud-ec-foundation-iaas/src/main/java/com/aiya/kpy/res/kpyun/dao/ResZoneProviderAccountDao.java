package com.aiya.kpy.res.kpyun.dao;

import com.aiya.kpy.common.enums.ResourceProvider;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.res.kpyun.entity.ResZoneProviderAccountEntity;
import com.aiya.platform.orm.PlatformCommonDAO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.cache.annotation.Cacheable;

import java.util.List;

public interface ResZoneProviderAccountDao extends PlatformCommonDAO<ResZoneProviderAccountEntity> {
    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where ZONE_CODE=#{zoneCode}"})
    ResZoneProviderAccountEntity selectZoneProviderAccountByZoneCode(@Param("zoneCode") String var1);

    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where KPY_REGION_CODE=#{regionCode} limit 1"})
    ResZoneProviderAccountEntity selectFirstByKpyRegionCode(@Param("regionCode") String var1);

    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where KPY_REGION_CODE=#{regionCode} and RESOURCE_PROVIDER=#{resourceProvider}"})
    List<ResZoneProviderAccountEntity> selectByRegionCodeAndResourceProvider(@Param("regionCode") String var1, @Param("resourceProvider") ResourceProvider var2);

    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where ZONE_CODE=#{zoneCode} and RESOURCE_TYPE=#{resourceType}"})
    ResZoneProviderAccountEntity selectByZoneCodeAndResourceType(@Param("zoneCode") String var1, @Param("resourceType") ResourceType var2);

    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where KPY_REGION_CODE=#{regionCode} and RESOURCE_TYPE=#{resourceType} limit 1"})
    ResZoneProviderAccountEntity selectFirstByRegionCodeAndResourceType(@Param("regionCode") String var1, @Param("resourceType") ResourceType var2);

    @Cacheable
    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where KPY_REGION_CODE=#{kpyRegionCode} and ZONE_CODE=#{kpyZoneCode} limit 1"})
    ResZoneProviderAccountEntity queryEntityBykpyRegionCodeAndKpyZoneCode(@Param("kpyRegionCode") String var1, @Param("kpyZoneCode") String var2);

    @Select({"<script>",
            "select * from RES_ZONE_PROVIDER_ACCOUNT where KPY_REGION_CODE=#{platformRegionCode}",
            "<if test='platformZoneCode != null'>",
            "and ZONE_CODE=#{platformZoneCode}",
            "</if>",
            "limit 1",
            "</script>"})
    ResZoneProviderAccountEntity selectZoneProviderAccount(@Param("platformRegionCode") String platformRegionCode, @Param("platformZoneCode") String platformZoneCode);

    @Select("<script>"
            + "select * from RES_ZONE_PROVIDER_ACCOUNT "
            + "where KPY_REGION_CODE IN "
            + "<foreach collection='kpyRegionCodes' item='item' open='(' separator=',' close=')'>"
            + "  #{item}"
            + "</foreach>"
            + "</script>")
    List<ResZoneProviderAccountEntity> selectAllByKpyRegionCodes(@Param("kpyRegionCodes") List<String> kpyRegionCodes);

    @Select("SELECT A.* " +
            "FROM RES_ZONE_PROVIDER_ACCOUNT A " +
            "         JOIN RES_ZONE_PROVIDER_ACCOUNT B " +
            "              ON A.REGION_CODE = B.REGION_CODE " +
            "WHERE B.KPY_REGION_CODE = #{kpyRegionCode}")
    List<ResZoneProviderAccountEntity> selectAllByKpyRegionCodeMapping(@Param("kpyRegionCode") String kpyRegionCode);

    @Select({"select * from RES_ZONE_PROVIDER_ACCOUNT where RESOURCE_PROVIDER=#{resourceProvider}"})
    List<ResZoneProviderAccountEntity> selectAllByResourceProvider(@Param("resourceProvider") ResourceProvider resourceProvider);
}
