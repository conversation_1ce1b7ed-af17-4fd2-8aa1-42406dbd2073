package com.aiya.kpy.res.kpyun.entity;

import com.aiya.kpy.ec.api.enums.VlanStatus;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR> Ma
 * @description
 * @date 2024/4/12 13:44
 */
@Entity
@Table(name = "ECLOUD_VLAN")
@Data
public class ECloudVlanEntity {
    /**
     * 主键ID
     */
    @Id
    @Column(name = "ID", unique = true, nullable = false)
    private Long id;
    /**
     * 移动云vpc id
     */
    @Column(name = "VPC_ID")
    private String vpcId;
    /**
     * 移动云vpc子网id
     */
    @Column(name = "SUBNET_ID")
    private String subnetId;

    /**
     * vlan id
     */
    @Column(name = "THIRD_PARTY_RESOURCE_UUID")
    private String thirdPartyResourceUuid;
    /**
     * ip
     */
    @Column(name = "IP")
    private String ip;

    /**
     * Available：占用中
     * Deleted：已释放
     */
    @Column(name = "STATUS")
    private VlanStatus status;

    @Column(name = "PLATFORM_REGION_CODE")
    private String platformRegionCode;

    @Column(name = "PLATFORM_ZONE_CODE")
    private String platformZoneCode;

    @Column(name = "CREATE_TIME")
    private Date createTime;

    @Column(name = "UPDATE_TIME")
    private Date updateTime;

    /**
     * 逻辑删除时间戳
     * 为0时代表未删除，否则为已删除
     */
    @Column(name = "DELETE_TIME")
    private Long deleteTime;
}
