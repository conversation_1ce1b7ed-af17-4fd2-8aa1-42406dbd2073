package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VpcGetNetworkListQuery extends Query {
    @SerializedName("rangeInNetworkIds")
    private List<String> rangeInNetworkIds;
    @SerializedName("shared")
    private Boolean shared;
    @SerializedName("visible")
    private Boolean visible;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("ipVersions")
    private List<VersionsEnum> ipVersions;
    @SerializedName("page")
    private Integer page;
    @SerializedName("queryWord")
    private String queryWord;
    @SerializedName("region")
    private String region;
    @SerializedName("networkTypeEnum")
    private List<NetworkTypeEnum> networkTypeEnum;
}
