package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Getter
@AllArgsConstructor
public enum RegionComponentEnum {
    /**
     * 鉴权
     */
    KEYSTONE("KEYSTONE"),
    /**
     * 主机
     */
    NOVA("NOVA"),
    /**
     * 镜像
     */
    GLANCE("GLANCE"),
    /**
     * 网络
     */
    NEUTRON("NEUTRON"),
    /**
     * 云硬盘
     */
    CINDER("CINDER"),
    /**
     * 裸金属
     */
    IRONIC("IRONIC"),
    /**
     * 文件存储
     */
    MANILA("MANILA"),
    /**
     * 该组件未接入
     */
    SENLIN("SENLIN"),
    /**
     * 对象存储
     */
    ONEST("ONEST");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
