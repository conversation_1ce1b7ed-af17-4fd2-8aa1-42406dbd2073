package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcPortAttachServerBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcPortAttachServerRequest;
import com.aiya.kpy.res.ecloud.sdk.response.PortAttachServerResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortAttachServerRequest extends ECloudAcsRequest<PortAttachServerResponse> {

    @ApiModelProperty("虚拟网卡id")
    @NotNull(message = "虚拟网卡id不能为空")
    private String id;

    @ApiModelProperty("绑定服务器id")
    @NotNull(message = "绑定服务器id不能为空")
    private String resourceId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("portAttachServer")
                .uri("/acl/v3/port/attachServer")
                .gatewayUri("/api/openapi-vpc/acl/v3/port/attachServer")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcPortAttachServerRequest toRequest() {
        VpcPortAttachServerBody body = VpcPortAttachServerBody.builder()
                .id(this.id)
                .resourceId(this.resourceId)
                .build();
        return VpcPortAttachServerRequest.builder()
                .portAttachServerBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return PortAttachServerResponse.class;
    }
}
