package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/24
 */
@Getter
@AllArgsConstructor
public enum OperationFlagEnum {
    ATTACH("AT<PERSON>CH"),
    <PERSON><PERSON><PERSON>_HDR("AT<PERSON>CH_HDR"),
    <PERSON><PERSON><PERSON>("DETACH"),
    DETACH_HDR("DETACH_HDR"),
    BACKUP("BACKUP"),
    SNA<PERSON><PERSON><PERSON>("SNAPSHOT"),
    <PERSON><PERSON><PERSON><PERSON>("FROZEN"),
    RESTORE("RESTORE"),
    CREATE("CREATE"),
    DELETE("DELETE"),
    RESIZ<PERSON>("RESIZE"),
    R<PERSON><PERSON><PERSON>("RENEW"),
    UPDATE("UPDATE"),
    NONE("NONE"),
    ERROR("ERROR"),
    DELETE_BACKUP("DELETE_BACKUP"),
    DELETE_SNAPSHOT("DELETE_SNAPSHOT"),
    PRE_DELETE("PRE_DELETE"),
    MIGRATE("MIGRATE"),
    INITIALIZE("INITIALIZE");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
