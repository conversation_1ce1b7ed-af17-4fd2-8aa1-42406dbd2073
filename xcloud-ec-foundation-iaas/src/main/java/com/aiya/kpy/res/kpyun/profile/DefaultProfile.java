/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.profile;

import com.aiya.kpy.res.kpyun.auth.Credential;
import com.aiya.kpy.res.kpyun.auth.ISigner;
import com.aiya.kpy.res.kpyun.auth.ShaHmac1Singleton;
import com.aiya.kpy.res.kpyun.enums.FormatType;
import com.aiya.kpy.res.kpyun.exception.ClientException;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@SuppressWarnings("AlibabaLowerCamelCaseVariableNaming")
public class DefaultProfile implements IClientProfile {
	private static DefaultProfile profile = null;
	private static List<Endpoint> endpoints = null;

	private Credential credential = null;
	private String regionId = null;
	private final FormatType acceptFormat = null;
	private ISigner isigner = null;
	private IEndpointsProvider iendpoints = null;

	private DefaultProfile() {
		this.iendpoints = new InternalEndpointsParser();
	}

	private DefaultProfile(String region, Credential creden) {
		this.iendpoints = new InternalEndpointsParser();
		this.credential = creden;
		this.regionId = region;
	}

	private DefaultProfile(String region, Credential creden, IEndpointsProvider provider) {
		this.iendpoints = provider;
		this.credential = creden;
		this.regionId = region;
	}

	public synchronized static DefaultProfile getProfile() {
		if (null == profile) {
			profile = new DefaultProfile();
		}
		return profile;
	}

	public synchronized static DefaultProfile getProfile(String regionId, String accessKeyId, String secret) {
		Credential creden = new Credential(accessKeyId, secret);
		profile = new DefaultProfile(regionId, creden);
		return profile;
	}

	public synchronized static DefaultProfile getProfile(String regionId, IEndpointsProvider provider,
														 String accessKeyId, String secret) {
		Credential creden = new Credential(accessKeyId, secret);
		profile = new DefaultProfile(regionId, creden, provider);
		return profile;
	}

	public synchronized static void addEndpoint(String endpointName, String regionId, String product, String domain)
			throws ClientException {
		if (null == endpoints) {
			endpoints = getProfile().getEndpoints();
		}
		Endpoint endpoint = findEndpointByRegionId(regionId);
		if (null == endpoint) {
			addEndpoint_(endpointName, regionId, product, domain);
		} else {
			updateEndpoint(regionId, product, domain, endpoint);
		}
	}

	private static void addEndpoint_(String endpointName, String regionId, String product, String domain) {
		Set<String> regions = new HashSet<String>();
		regions.add(regionId);

		List<ProductDomain> productDomains = new ArrayList<ProductDomain>();
		productDomains.add(new ProductDomain(product, domain));
		Endpoint endpoint = new Endpoint(endpointName, regions, productDomains);
		endpoints.add(endpoint);
	}

	private static void updateEndpoint(String regionId, String product, String domain, Endpoint endpoint) {
		Set<String> regionIds = endpoint.getRegionIds();
		regionIds.add(regionId);

		List<ProductDomain> productDomains = endpoint.getProductDomains();
		ProductDomain productDomain = findProductDomain(productDomains, product);
		if (null == productDomain) {
			productDomains.add(new ProductDomain(product, domain));
		} else {
			productDomain.setDomianName(domain);
		}
	}

	private static Endpoint findEndpointByRegionId(String regionId) {
		for (Endpoint endpoint : endpoints) {
			if (endpoint.getRegionIds().contains(regionId)) {
				return endpoint;
			}
		}
		return null;
	}

	private static ProductDomain findProductDomain(List<ProductDomain> productDomains, String product) {
		for (ProductDomain productDomain : productDomains) {
			if (productDomain.getProductName().equals(product)) {
				return productDomain;
			}
		}
		return null;
	}

	@Override
	public synchronized ISigner getSigner() {
		if (null == isigner) {
			this.isigner = ShaHmac1Singleton.INSTANCE.getInstance();
		}
		return isigner;
	}

	@Override
	public synchronized String getRegionId() {
		return regionId;
	}

	@Override
	public synchronized FormatType getFormat() {
		return acceptFormat;
	}

	@Override
	public synchronized Credential getCredential() {
		return credential;
	}

	@Override
	public synchronized List<Endpoint> getEndpoints() throws ClientException {
		if (null == endpoints) {
			endpoints = iendpoints.getEndpoints();
		}
		return endpoints;
	}

	@Override
	public synchronized List<Endpoint> getEndpoints(String product) throws ClientException {
		if (null == endpoints || Endpoint.findProductDomain(regionId, product, endpoints) == null) {
			if (endpoints == null) {
				endpoints = iendpoints.getEndpoints();
			}
		}
		return endpoints;
	}
}
