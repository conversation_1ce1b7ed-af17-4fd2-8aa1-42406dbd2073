package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.ContainerNic;

/**
 * 创建容器网卡响应
 *
 * @Author: liuyingjie
 * @Date: 2025/1/24
 * @Description: 创建容器网卡操作的响应类，包含请求ID、主机ID和网卡信息，基于container.swagger.json生成
 */
public class CreateContainerNicResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private ContainerNic info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public ContainerNic getInfo() {
        return info;
    }

    public void setInfo(ContainerNic info) {
        this.info = info;
    }
}
