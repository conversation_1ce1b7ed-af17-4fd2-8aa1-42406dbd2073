package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.OrderExtTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ProcedureCodeEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateResponsePaymentInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
public class CreateInstanceResponse implements ECloudResponseBody {

    @ApiModelProperty("订单项id")
    private List<String> orderExts;

    @ApiModelProperty("订单id")
    private String orderId;

    @ApiModelProperty("付费类型")
    private ProcedureCodeEnum procedureCode;

    @ApiModelProperty("资源波动提示")
    private String resourceRemind;

    @ApiModelProperty("订单项类型")
    private List<OrderExtTypeEnum> orderExtTypes;

    @ApiModelProperty("支付信息")
    private VmCreateResponsePaymentInfo paymentInfo;
}
