package com.aiya.kpy.res.sdk.model;

import java.util.Date;

/**
 * ContainerSshkey 容器SSH密钥模型
 *
 * 用于容器云平台的SSH密钥配置模型，包含SSH密钥的名称、
 * 内容等安全认证信息。支持容器的安全登录和密钥管理。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class ContainerSshkey {
    private String uuid;
    private String name;
    private String description;
    private String content;
    private Integer userId;
    private Date createdAt;
    private Date updatedAt;
    private String creator;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
}
