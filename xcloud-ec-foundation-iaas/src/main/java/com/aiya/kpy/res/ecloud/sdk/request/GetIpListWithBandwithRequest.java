package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.BandwidthTypeEnum;
import com.aiya.kpy.res.ecloud.enums.BindTypeEnum;
import com.aiya.kpy.res.ecloud.enums.IpTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwidthListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwidthListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetIpListWithBandwithResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetIpListWithBandwithRequest extends ECloudAcsRequest<GetIpListWithBandwithResponse> {
    @ApiModelProperty("根据公网IP地址搜索")
    private String queryWord;

    @ApiModelProperty("根据公网IP ID搜索")
    private String idQueryWord;

    @ApiModelProperty("标签ID列表")
    private List<String> tagIds;

    @ApiModelProperty("带宽类型")
    private BandwidthTypeEnum bandwidthType;

    @ApiModelProperty("VPC路由ID")
    private String routerId;

    @ApiModelProperty("公网IP是否使用")
    private Boolean bound;

    @ApiModelProperty("公网IP是否占用资源")
    private Boolean occupy;

    @ApiModelProperty("公网IP是否冻结")
    private Boolean frozen;

    @ApiModelProperty("是否开启IPv6转换")
    private Boolean openIpv6Converter;

    @ApiModelProperty("公网IP接入类型")
    private IpTypeEnum ipType;

    @ApiModelProperty("NAT网关ID")
    private String natGatewayId;

    @ApiModelProperty("公网IP是否可加入到共享带宽")
    private Boolean availableForSbw;

    @ApiModelProperty("公网IP绑定资源类型")
    private BindTypeEnum bindType;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @ApiModelProperty("分页")
    private Integer page;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("listFipWithBandwidth")
                .uri("/acl/v3/floatingip/post/list/WithBw")
                .gatewayUri("/api/openapi-eip/acl/v3/floatingip/post/list/WithBw")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipWithBandwidthListRequest toRequest() {
        EipWithBandwidthListQuery query = EipWithBandwidthListQuery.builder()
                .queryWord(this.queryWord)
                .idQueryWord(this.idQueryWord)
                .tagIds(this.tagIds)
                .bandwidthType(this.bandwidthType)
                .routerId(this.routerId)
                .bound(this.bound)
                .occupy(this.occupy)
                .frozen(this.frozen)
                .openIpv6Converter(this.openIpv6Converter)
                .ipType(this.ipType)
                .natGatewayId(this.natGatewayId)
                .availableForSbw(this.availableForSbw)
                .bindType(this.bindType)
                .pageSize(this.pageSize)
                .page(this.page)
                .build();
        return EipWithBandwidthListRequest.builder()
                .listFipWithBandwidthQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetIpListWithBandwithResponse.class;
    }
}
