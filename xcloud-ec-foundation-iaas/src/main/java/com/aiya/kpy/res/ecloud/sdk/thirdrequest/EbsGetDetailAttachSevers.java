package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.EcStatusEnum;
import com.aiya.kpy.res.ecloud.enums.OpStatusEnum;
import com.aiya.kpy.res.ecloud.enums.ProductTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ServerTypeEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/24
 */
@Data
public class EbsGetDetailAttachSevers {
    @SerializedName("ecStatus")
    private EcStatusEnum ecStatus;
    @SerializedName("task")
    private String task;
    @SerializedName("operationFlag")
    private Integer operationFlag;
    @SerializedName("serverType")
    private ServerTypeEnum serverType;
    @SerializedName("isAuthority")
    private Boolean isAuthority;
    @SerializedName("adminPaused")
    private Boolean adminPaused;
    @SerializedName("serverName")
    private String serverName;
    @SerializedName("opStatus")
    private OpStatusEnum opStatus;
    @SerializedName("serverId")
    private String serverId;
    @SerializedName("productType")
    private ProductTypeEnum productType;
}
