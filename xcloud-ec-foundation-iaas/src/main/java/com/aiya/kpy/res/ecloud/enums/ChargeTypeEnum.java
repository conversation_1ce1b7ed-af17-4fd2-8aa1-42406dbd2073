package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 付费方式
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
@Getter
@AllArgsConstructor
public enum ChargeTypeEnum {
    /**
     * 预付费
     */
    PREPAID("PREPAID"),
    /**
     * 后付费
     */
    POSTPAID("POSTPAID"),
    /**
     * 预付费和后付费
     */
    BOTH("BOTH"),
    ;
    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
