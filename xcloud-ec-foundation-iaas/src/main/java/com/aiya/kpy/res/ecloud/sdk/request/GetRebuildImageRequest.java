package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.RebuildImageTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetRebuildImagePath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetRebuildImageQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetRebuildImageRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetRebuildImageResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetRebuildImageRequest extends ECloudAcsRequest<GetRebuildImageResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @ApiModelProperty("type")
    @NotNull(message = "镜像类型不能为空")
    private RebuildImageTypeEnum type;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmGetRebuildImage")
                .uri("/acl/v3/server/{serverId}/rebuild/images")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/{serverId}/rebuild/images")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmGetRebuildImageRequest toRequest() {
        return VmGetRebuildImageRequest.builder()
                .vmGetRebuildImagePath(VmGetRebuildImagePath.builder().serverId(this.serverId).build())
                .vmGetRebuildImageQuery(VmGetRebuildImageQuery.builder().type(this.type.getValue()).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetRebuildImageResponse.class;
    }
}
