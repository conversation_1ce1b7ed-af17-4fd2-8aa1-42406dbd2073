package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Getter
@AllArgsConstructor
public enum PublicImageTypeEnum {
    IMAGE("IMAGE"),
    VM_PUBLIC_IMAGE("VM_PUBLIC_IMAGE"),
    PM_PUBLIC_IMAGE("PM_PUBLIC_IMAGE");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
