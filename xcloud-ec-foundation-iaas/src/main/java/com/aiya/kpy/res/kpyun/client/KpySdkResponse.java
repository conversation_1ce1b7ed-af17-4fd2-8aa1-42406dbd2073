package com.aiya.kpy.res.kpyun.client;


import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;

/**
 * Created by ChenXing on 2017/4/6.
 */
public class KpySdkResponse<T extends KpyAcsResponse> {
	private boolean success;

	private String code;

	private T acsResponse;

	private String requestId;

	private String errMsg;

	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}


	public T getAcsResponse() {
		return acsResponse;
	}

	public void setAcsResponse(T acsResponse) {
		this.acsResponse = acsResponse;
	}

	public String getRequestId() {
		return requestId;
	}

	public void setRequestId(String requestId) {
		this.requestId = requestId;
	}

	public String getErrMsg() {
		return String.format("%s(requestId:%s)",errMsg,requestId);
	}

	public void setErrMsg(String errMsg) {
		this.errMsg = errMsg;
	}
}
