package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipWithBandwidthResponseNatGatewayDnatRuleResps;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
public class GetIpDetailWithBandwithResponse implements ECloudResponseBody {
    @ApiModelProperty("计费方式：bandwidthCharge-按带宽计费；trafficCharge-按流量计费（兼容存量调用）")
    private String chargingMode;

    @ApiModelProperty("网卡名称")
    private String nicName;

    @ApiModelProperty("绑定资源id")
    private String resourceId;

    @ApiModelProperty("带宽大小，单位：Kbps")
    private Integer bandwidthSize;

    @ApiModelProperty("带宽大小，单位：Mbps")
    private Integer bandwidthMbSize;

    @ApiModelProperty("公网IP关联带宽ID")
    private String bandwidthId;

    @ApiModelProperty("计费方式")
    private ChargeModeEnum chargeModeEnum;

    @ApiModelProperty("带宽类型")
    private BandwidthTypeEnum bandwidthType;

    @ApiModelProperty("IPv6地址")
    private String ipv6;

    @ApiModelProperty("IPv6共享带宽ID")
    private String mixedId;

    @SerializedName("routerId")
    private String routerId;

    @ApiModelProperty("公网IP创建时间")
    private String createdTime;

    @ApiModelProperty("公网IP ID")
    private String id;

    @ApiModelProperty("公网IP地址")
    private String dummyFip;

    @ApiModelProperty("是否可见")
    private Boolean visible;

    @ApiModelProperty("公网IP是否使用")
    private Boolean bound;

    @ApiModelProperty("公网IP绑定资源类型")
    private BindTypeEnum bindType;

    @ApiModelProperty("公网IP是否冻结")
    private Boolean frozen;

    @ApiModelProperty("绑定资源名称")
    private String resourceName;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("网卡所属的网络ID")
    private String portNetworkId;

    @ApiModelProperty("公网IPv4地址")
    private String name;

    @ApiModelProperty("备案状态")
    private IcpStatusEnum icpStatus;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("公网IP接入类型")
    private IpTypeEnum ipType;

    @ApiModelProperty("ip绑定状态")
    private EipStatusEnum status;

    @ApiModelProperty("公网IP名称")
    private String description;

    @ApiModelProperty("是否为边缘云")
    private String edge;

    @ApiModelProperty("产品类型")
    private String fipProductType;

    @ApiModelProperty("公网IP绑定的资源信息")
    private String floatingIpBindResourceResp;

    @ApiModelProperty("公网IP绑定的DNAT规则信息，仅绑定类型为DNAT时返回")
    private List<EipWithBandwidthResponseNatGatewayDnatRuleResps> natGatewayDnatRuleResps;

    @ApiModelProperty("IPv6共享带宽是否为新订购类型：true-是（IPv6共享带宽）；false-否（混合带宽）")
    private Boolean newProductFlag;

    @ApiModelProperty("公网IP是否占用资源：true-是；false-否")
    private Boolean occupy;
}
