package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Getter
@AllArgsConstructor
public enum PortTypeEnum {
    /**
     * 云主机网卡
     */
    VM("vm"),
    /**
     * 裸金属网卡
     */
    IRONIC("ironic"),
    /**
     * NAS网卡
     */
    NAS("nas"),
    /**
     * 东西向网卡
     */
    EW("ew"),
    /**
     * 跨资源池VPC互通使用网卡
     */
    VPC_CONNECTION("vpc_connection"),
    /**
     * 弹性裸金属网卡
     */
    EBM("ebm"),
    /**
     * 云堡垒机网卡
     */
    BASTION("bastion"),
    /**
     * K8S云原生使用网卡
     */
    VPC_NATIVE("vpc_native"),
    /**
     * 双栈网卡
     */
    FAKE("fake"),
    /**
     * 云电脑网卡
     */
    CLOUD_COMPUTER("cloud_computer");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
