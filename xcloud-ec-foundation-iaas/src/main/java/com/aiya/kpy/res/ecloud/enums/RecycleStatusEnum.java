package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Getter
@AllArgsConstructor
public enum RecycleStatusEnum {
    UNRECYCLE("UNRECYCLE"),
    RECYCLE_TO_BE_DELETED("RECYCLE_TO_BE_DELETED"),
    RECYCLE_RECOVER_IN_PROGRESS("RECYCLE_RECOVER_IN_PROGRESS"),
    RECYCLE_RECOVER_FAILED("RECYCLE_RECOVER_FAILED");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
