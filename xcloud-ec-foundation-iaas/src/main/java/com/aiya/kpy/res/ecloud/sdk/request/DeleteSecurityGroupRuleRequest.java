package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteSecGroupRulePath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteSecurityGroupRuleRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteSecurityGroupRuleResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteSecurityGroupRuleRequest extends ECloudAcsRequest<DeleteSecurityGroupRuleResponse> {
    @ApiModelProperty("安全组规则id")
    @NotNull(message = "安全组规则id不能为空")
    private String securityGroupRuleId;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("deleteSecGroupRule")
                .uri("/customer/v3/SecurityGroupRule/{securityGroupRuleId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroupRule/{securityGroupRuleId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcDeleteSecurityGroupRuleRequest toRequest() {
        VpcDeleteSecGroupRulePath path = VpcDeleteSecGroupRulePath.builder()
                .securityGroupRuleId(this.securityGroupRuleId)
                .build();
        return VpcDeleteSecurityGroupRuleRequest.builder()
                .deleteSecGroupRulePath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteSecurityGroupRuleResponse.class;
    }
}
