package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreatePortBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreatePortRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreatePortRequestIps;
import com.aiya.kpy.res.ecloud.sdk.response.CreatePortResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePortRequest extends ECloudAcsRequest<CreatePortResponse> {
    @ApiModelProperty("端口关联的主机ID")
    private String bindingHostId;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("子网和指定ip列表")
    private List<VpcCreatePortRequestIps> ips;

    @ApiModelProperty("mac地址")
    private String macAddress;

    @ApiModelProperty("网卡名称")
    private String name;

    @ApiModelProperty("网络id")
    private String networkId;

    @ApiModelProperty("创建port+辅助Ip的总数目")
    private Integer portSumNum;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("安全组id")
    private List<String> sgIds;

    @ApiModelProperty("网卡使用方式")
    private ResourceTypeEnum usagetype;

    @ApiModelProperty("网卡类型")
    private PortTypeEnum type;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("createPort")
                .uri("/customer/v3/port")
                .gatewayUri("/api/openapi-vpc/customer/v3/port")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcCreatePortRequest toRequest() {
        VpcCreatePortBody body = VpcCreatePortBody.builder()
                .bindingHostId(this.bindingHostId)
                .edge(this.edge)
                .ips(this.ips)
                .macAddress(this.macAddress)
                .name(this.name)
                .networkId(this.networkId)
                .portSumNum(this.portSumNum)
                .region(this.region)
                .sgIds(this.sgIds)
                .usagetype(this.usagetype)
                .type(this.type)
                .build();
        return VpcCreatePortRequest.builder().createPortBody(body).build();
    }

    @Override
    public Class getResponseClass() {
        return CreatePortResponse.class;
    }
}
