package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.Container;

import java.util.List;

/**
 * 查询容器实例列表响应
 *
 * 容器云平台查询容器实例列表API的响应类。包含分页信息
 * 和容器实例详细信息列表，支持容器实例的批量查询和管理。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class DescribeContainerInstancesResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private Integer pageNumber;
    private Integer pageSize;
    private Integer totalCount;
    private List<Container> info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<Container> getInfo() {
        return info;
    }

    public void setInfo(List<Container> info) {
        this.info = info;
    }
}
