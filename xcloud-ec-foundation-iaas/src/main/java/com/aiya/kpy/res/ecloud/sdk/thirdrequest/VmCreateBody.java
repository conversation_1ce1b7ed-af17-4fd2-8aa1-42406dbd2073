package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.BillingTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VmTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class VmCreateBody extends Body {
    @SerializedName("bootVolume")
    private VmCreateRequestBootVolume bootVolume;
    @SerializedName("specsName")
    private String specsName;
    @SerializedName("imageName")
    private String imageName;
    @SerializedName("quantity")
    private Integer quantity;
    @SerializedName("userData")
    private String userData;
    @SerializedName("keypairName")
    private String keypairName;
    @SerializedName("bandwidth")
    private VmCreateRequestBandwidth bandwidth;
    @SerializedName("ip")
    private VmCreateRequestIp ip;
    @SerializedName("cpu")
    private Integer cpu;
    @SerializedName("description")
    private String description;
    @SerializedName("networks")
    private VmCreateRequestNetworks networks;
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("vmType")
    private VmTypeEnum vmType;
    @SerializedName("password")
    private String password;
    @SerializedName("bind")
    private VmCreateRequestBind bind;
    @SerializedName("billingType")
    private BillingTypeEnum billingType;
    @SerializedName("dataVolume")
    private List<VmCreateRequestDataVolume> dataVolume;
    @SerializedName("securityGroupIds")
    private List<String> securityGroupIds;
    @SerializedName("name")
    private String name;
    @SerializedName("autoRenew")
    private Boolean autoRenew;
    @SerializedName("region")
    private String region;
    @SerializedName("ram")
    private Integer ram;
    @SerializedName("tags")
    private List<RequestTags> tags;
}
