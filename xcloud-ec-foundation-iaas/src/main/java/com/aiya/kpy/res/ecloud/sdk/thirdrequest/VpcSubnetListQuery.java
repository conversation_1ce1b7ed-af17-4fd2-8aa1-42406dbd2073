package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VpcSubnetListQuery extends Query {
    @SerializedName("queryKey")
    private String queryKey;
    @SerializedName("natGatewayId")
    private String natGatewayId;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("ipsecVpnUsed")
    private Boolean ipsecVpnUsed;
    @SerializedName("ipVersion")
    private VersionsEnum ipVersion;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("rangeInSubnetIds")
    private List<String> rangeInSubnetIds;
    @SerializedName("vpcId")
    private String vpcId;
    @SerializedName("customerId")
    private String customerId;
    @SerializedName("networkId")
    private String networkId;
    @SerializedName("page")
    private Integer page;
    @SerializedName("region")
    private String region;
    @SerializedName("networkTypeEnum")
    private NetworkTypeEnum networkTypeEnum;
}
