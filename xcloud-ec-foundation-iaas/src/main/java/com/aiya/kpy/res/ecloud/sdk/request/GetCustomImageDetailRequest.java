package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetCustomImageDetailResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetDetailRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/4/3
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCustomImageDetailRequest extends ECloudAcsRequest<GetCustomImageDetailResponse> {
    private String imageId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("getImageRespV2")
                .uri("/image/{imageId}")
                .gatewayUri("/api/openapi-ims/user/v5/image/{imageId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public ImsGetDetailRequest toRequest() {
        ImsGetDetailPath path = ImsGetDetailPath.builder().imageId(imageId).build();
        return ImsGetDetailRequest.builder()
                .getImageRespV2Path(path)
                .build();
    }

    @Override
    public Class<GetCustomImageDetailResponse> getResponseClass() {
        return GetCustomImageDetailResponse.class;
    }
}
