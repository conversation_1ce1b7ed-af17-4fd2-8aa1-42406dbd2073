package com.aiya.kpy.res.ecloud.client;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.response.ECloudSdkResponse;

import javax.persistence.Transient;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
public interface ECloudSdkClient {

    <T extends ECloudResponseBody> ECloudSdkResponse<T> execute(ECloudAcsRequest<T> request);

    String getPoolId();

    @Transient
    String getRegionId();
}
