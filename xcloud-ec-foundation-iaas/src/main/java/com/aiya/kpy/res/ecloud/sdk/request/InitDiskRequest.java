package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsInitVolumePath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsInitVolumeRequest;
import com.aiya.kpy.res.ecloud.sdk.response.InitDiskResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InitDiskRequest extends ECloudAcsRequest<InitDiskResponse> {
    @ApiModelProperty("硬盘id")
    @NotNull(message = "硬盘id不能为空")
    private String volumeId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("initializeVolume")
                .uri("/acl/v3/volume/initialize/{volumeId}")
                .gatewayUri("/api/ebs/acl/v3/volume/initialize/{volumeId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsInitVolumeRequest toRequest() {
        return EbsInitVolumeRequest.builder()
                .initializeVolumePath(EbsInitVolumePath.builder().volumeId(this.volumeId).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return InitDiskResponse.class;
    }
}
