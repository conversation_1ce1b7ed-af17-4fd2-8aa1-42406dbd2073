package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.platform.foundation.enviroment.Enviroment;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RequestTags {
    @SerializedName("value")
    private String value;
    @SerializedName("key")
    private String key;

    private static AtomicReference<RequestTags> requestTags = new AtomicReference<>();

    public static RequestTags getInstanceByEnvironment() {
        return Optional.ofNullable(requestTags.get()).orElseGet(() -> {
            RequestTags newTag = RequestTags.builder()
                    .key("env")
                    .value(Enviroment.instance().getEnviromentType().getCode())
                    .build();
            requestTags.compareAndSet(null, newTag);
            return requestTags.get();
        });
    }
}
