package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetVpcDetailResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetDetailQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetDetailRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetVpcDetailByRouterIdRequest extends ECloudAcsRequest<GetVpcDetailResponse> {
    @ApiModelProperty("routerId")
    @NotNull(message = "routerId不能为空")
    private String routerId;

    @ApiModelProperty("vpc是否可见")
    private Boolean visible;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("getVpcDetailRespByRouterId")
                .uri("/customer/v3/vpc/router/{routerId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/vpc/router/{routerId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetDetailRequest toRequest() {
        return VpcGetDetailRequest.builder()
                .getVpcDetailRespByVpcIdPath(VpcGetDetailPath.builder().routerId(this.routerId).build())
                .getVpcDetailRespByVpcIdQuery(VpcGetDetailQuery.builder().visible(this.visible).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetVpcDetailResponse.class;
    }
}
