package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSecurityGroupRuleListResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupRuleListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupRuleListRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSecurityGroupRuleListRequest extends ECloudAcsRequest<GetSecurityGroupRuleListResponse> {
    @ApiModelProperty("安全组ID")
    private String securityGroupId;

    @ApiModelProperty("分页")
    private Integer page;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listSecurityGroupRule")
                .uri("/customer/v3/SecurityGroupRule")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroupRule")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetSecurityGroupRuleListRequest toRequest() {
        VpcGetSecurityGroupRuleListQuery query = VpcGetSecurityGroupRuleListQuery.builder()
                .securityGroupId(this.securityGroupId)
                .page(this.page)
                .pageSize(this.pageSize)
                .build();
        return VpcGetSecurityGroupRuleListRequest.builder().listSecurityGroupRuleQuery(query).build();
    }

    @Override
    public Class getResponseClass() {
        return GetSecurityGroupRuleListResponse.class;
    }
}
