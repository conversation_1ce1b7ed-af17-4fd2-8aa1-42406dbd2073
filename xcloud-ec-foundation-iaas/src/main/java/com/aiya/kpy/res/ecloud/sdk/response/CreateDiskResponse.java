package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class CreateDiskResponse implements ECloudResponseBody {
    @ApiModelProperty("资源ID")
    private String resourceId;

    @ApiModelProperty("资源对应订单id")
    private String orderId;

    @ApiModelProperty("资源对应订单项id")
    private String orderExtId;

    @ApiModelProperty("预付费支付信息")
    private Map<String, Object> paymentInfo;

    @ApiModelProperty("资源类型")
    private ResourceTypeEnum resourceType;

    @Getter
    @AllArgsConstructor
    public enum ResourceTypeEnum {
        VOLUMEBACK("VOLUMEBACK"),
        VOLUMESNAPSHOT("VOLUMESNAPSHOT"),
        VOLUME("VOLUME"),
        VOLUME_HDR("VOLUME_HDR"),
        VMSERVER("VMSERVER"),
        IRONICSERVER("IRONICSERVER"),
        DECLOUD_NODE("DECLOUD_NODE"),
        NAS("NAS"),
        DEFAULT("DEFAULT"),
        VOLUMEBACK_SERVICE("VOLUMEBACK_SERVICE"),
        PFS("PFS"),
        VOLUMEBACK_PACKAGE("VOLUMEBACK_PACKAGE");

        private String value;
    }
}
