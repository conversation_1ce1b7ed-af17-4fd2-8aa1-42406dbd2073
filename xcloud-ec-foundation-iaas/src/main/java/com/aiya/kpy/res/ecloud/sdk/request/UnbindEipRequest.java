package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipUnbindPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipUnbindQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipUnbindRequest;
import com.aiya.kpy.res.ecloud.sdk.response.UnbindEipResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UnbindEipRequest extends ECloudAcsRequest<UnbindEipResponse> {
    @ApiModelProperty("ipId")
    @NotNull(message = "ipId不能为空")
    private String ipId;

    @ApiModelProperty("是否异步绑定：sync-同步绑定（默认）；async-异步绑定")
    private String asyncType;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("unbindFloatingIp")
                .uri("/acl/v3/floatingip/unbind/{ipId}")
                .gatewayUri("/api/openapi-eip/acl/v3/floatingip/unbind/{ipId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipUnbindRequest toRequest() {
        EipUnbindPath path = EipUnbindPath.builder().ipId(this.ipId).build();
        EipUnbindQuery query = EipUnbindQuery.builder().asyncType(this.asyncType).build();
        return EipUnbindRequest.builder()
                .unbindFloatingIpPath(path)
                .unbindFloatingIpQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return UnbindEipResponse.class;
    }
}
