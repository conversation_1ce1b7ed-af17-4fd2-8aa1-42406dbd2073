package com.aiya.kpy.res.sdk.model;

import java.util.Date;
import java.util.List;

/**
 * 容器实例模型
 * 用于容器云平台的容器实例管理，包含容器的基本配置信息、网络配置和存储配置
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class Container {
    private String uuid;
    private String description;
    private String flavorUuid;
    private Integer cpus;
    private Integer mem;
    private Integer gpuNum;
    private String hostSn;
    private String keyUuid;
    private String pswd;
    private Integer regionId;
    private Integer rootSize;
    private List<String> runArgs;
    private Integer state;
    private String zoneUuid;
    private String containerNetType;
    private String imagePath;
    private Date createdAt;
    private Date updatedAt;
    private List<ContainerNic> nics;
    private List<ContainerVol> vols;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFlavorUuid() {
        return flavorUuid;
    }

    public void setFlavorUuid(String flavorUuid) {
        this.flavorUuid = flavorUuid;
    }

    public Integer getCpus() {
        return cpus;
    }

    public void setCpus(Integer cpus) {
        this.cpus = cpus;
    }

    public Integer getMem() {
        return mem;
    }

    public void setMem(Integer mem) {
        this.mem = mem;
    }

    public Integer getGpuNum() {
        return gpuNum;
    }

    public void setGpuNum(Integer gpuNum) {
        this.gpuNum = gpuNum;
    }

    public String getHostSn() {
        return hostSn;
    }

    public void setHostSn(String hostSn) {
        this.hostSn = hostSn;
    }

    public String getKeyUuid() {
        return keyUuid;
    }

    public void setKeyUuid(String keyUuid) {
        this.keyUuid = keyUuid;
    }

    public String getPswd() {
        return pswd;
    }

    public void setPswd(String pswd) {
        this.pswd = pswd;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getRootSize() {
        return rootSize;
    }

    public void setRootSize(Integer rootSize) {
        this.rootSize = rootSize;
    }

    public List<String> getRunArgs() {
        return runArgs;
    }

    public void setRunArgs(List<String> runArgs) {
        this.runArgs = runArgs;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public String getContainerNetType() {
        return containerNetType;
    }

    public void setContainerNetType(String containerNetType) {
        this.containerNetType = containerNetType;
    }

    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public List<ContainerNic> getNics() {
        return nics;
    }

    public void setNics(List<ContainerNic> nics) {
        this.nics = nics;
    }

    public List<ContainerVol> getVols() {
        return vols;
    }

    public void setVols(List<ContainerVol> vols) {
        this.vols = vols;
    }
}
