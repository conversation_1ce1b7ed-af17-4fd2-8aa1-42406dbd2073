package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
public class GetRegionResponseContent implements ECloudResponseBody {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("可用区名称")
    private String name;

    @ApiModelProperty("组件")
    private String component;

    @ApiModelProperty("资源池id")
    private String poolid;

    @ApiModelProperty("逻辑删除")
    private Boolean deleted;

    @ApiModelProperty("是否可见")
    private Boolean visible;
}
