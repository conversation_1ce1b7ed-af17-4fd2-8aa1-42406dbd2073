package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EipUnbindRequest {
    @SerializedName("unbindFloatingIpPath")
    private EipUnbindPath unbindFloatingIpPath;
    @SerializedName("unbindFloatingIpQuery")
    private EipUnbindQuery unbindFloatingIpQuery;
}
