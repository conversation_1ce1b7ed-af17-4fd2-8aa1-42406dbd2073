package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

/**
 * 删除容器实例响应
 *
 * @Author: liuyingjie
 * @Date: 2025/1/24
 * @Description: 删除容器实例操作的响应类，包含请求ID、主机ID和任务UUID等信息，基于container.swagger.json生成
 */
public class DeleteContainerInstanceResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private String taskUuid;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public String getTaskUuid() {
        return taskUuid;
    }

    public void setTaskUuid(String taskUuid) {
        this.taskUuid = taskUuid;
    }
}
