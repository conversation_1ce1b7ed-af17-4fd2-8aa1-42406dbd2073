package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Getter
@AllArgsConstructor
public enum ChangeTypeEnum {
    CHANGE("CHANGE"),
    TURN_MONTH("TURN_MONTH"),
    TURN_YEAR("TURN_YEAR"),
    TURN_AMOUNT("TURN_AMOUNT");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
