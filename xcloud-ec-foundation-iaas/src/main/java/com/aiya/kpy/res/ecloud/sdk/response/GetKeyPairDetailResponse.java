package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
public class GetKeyPairDetailResponse implements ECloudResponseBody {
    @ApiModelProperty("密钥对修改时间")
    private String modifiedTime;

    @ApiModelProperty("密钥对私钥")
    private String privateKey;

    @ApiModelProperty("密钥对名称")
    private String name;

    @ApiModelProperty("密钥对创建时间")
    private String createdTime;

    @ApiModelProperty("密钥对id")
    private String id;

    @ApiModelProperty("公钥")
    private String publicKey;

    @ApiModelProperty("密钥对可用区")
    private String region;

    @ApiModelProperty("密钥类型")
    private String type;

    @ApiModelProperty("密钥对指纹")
    private String fingerPrint;

    @ApiModelProperty("密钥对状态")
    private String status;
}
