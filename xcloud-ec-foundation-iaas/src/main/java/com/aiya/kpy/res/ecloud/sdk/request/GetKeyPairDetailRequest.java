package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetKeyPairDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetKeyPairDetailQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetKeyPairDetailRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetKeyPairDetailResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetKeyPairDetailRequest extends ECloudAcsRequest<GetKeyPairDetailResponse> {
    @ApiModelProperty("密钥对名称")
    private String keypairName;
    @ApiModelProperty("可用区")
    private String region;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmGetKeyPairDetail")
                .uri("/customer/v3/keypair/{keypairName}")
                .gatewayUri("/api/openapi-ecs/customer/v3/keypair/{keypairName}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest()).build();
    }

    @Override
    public VmGetKeyPairDetailRequest toRequest() {
        return VmGetKeyPairDetailRequest.builder()
                .vmGetKeyPairDetailPath(VmGetKeyPairDetailPath.builder().keypairName(this.keypairName).build())
                .vmGetKeyPairDetailQuery(VmGetKeyPairDetailQuery.builder().region(this.region).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetKeyPairDetailResponse.class;
    }
}
