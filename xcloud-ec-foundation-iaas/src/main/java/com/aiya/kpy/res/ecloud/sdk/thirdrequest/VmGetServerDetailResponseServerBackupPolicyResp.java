package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.BackupIntervalEnum;
import com.aiya.kpy.res.ecloud.enums.PreservedTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ServerBackUpEnum;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmGetServerDetailResponseServerBackupPolicyResp {
    @ApiModelProperty("备份保留数量")
    @SerializedName("preservedNum")
    private Integer preservedNum;

    @ApiModelProperty("是否被删除")
    @SerializedName("deleted")
    private Boolean deleted;

    @ApiModelProperty("策略id")
    @SerializedName("policyId")
    private String policyId;

    @ApiModelProperty("创建者")
    @SerializedName("createdBy")
    private String createdBy;

    @ApiModelProperty("备份重复周期")
    @SerializedName("backupInterval")
    private List<BackupIntervalEnum> backupInterval;

    @ApiModelProperty("所属客户")
    @SerializedName("customerId")
    private String customerId;

    @ApiModelProperty("策略name")
    @SerializedName("name")
    private String name;

    @ApiModelProperty("创建时间")
    @SerializedName("createdTime")
    private String createdTime;

    @ApiModelProperty("备份创建时间,每天0~23点整")
    @SerializedName("startTime")
    private List<String> startTime;

    @ApiModelProperty("策略类型")
    @SerializedName("preservedType")
    private PreservedTypeEnum preservedType;

    @ApiModelProperty("资源类型")
    @SerializedName("resourceType")
    private ServerBackUpEnum resourceType;
}
