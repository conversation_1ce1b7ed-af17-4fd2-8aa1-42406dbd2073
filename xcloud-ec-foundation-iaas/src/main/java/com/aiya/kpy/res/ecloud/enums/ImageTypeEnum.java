package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Getter
@AllArgsConstructor
public enum ImageTypeEnum {
    SERVER_BACKUP("SERVER_BACKUP"),
    IMAGE("IMAGE"),
    USER_INPUT_IMAGE("USER_INPUT_IMAGE"),
    USER_COPY_IMAGE("USER_COPY_IMAGE"),
    USER_INPUT_BIT_STREAM("USER_INPUT_BIT_STREAM"),
    COPY_INPUT_IMAGE("COPY_INPUT_IMAGE"),
    COPY_CREATE_IMAGE("COPY_CREATE_IMAGE");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
