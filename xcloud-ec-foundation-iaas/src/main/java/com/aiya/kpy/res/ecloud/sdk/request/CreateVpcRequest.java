package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.SpecsEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.RequestTags;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcOrderBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcOrderRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateVpcResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateVpcRequest extends ECloudAcsRequest<CreateVpcResponse> {
    @ApiModelProperty("IPv4协议子网网段，选择创建IPv4协议或IPv4+IPv6双栈协议子网时必选")
    private String cidr;

    @ApiModelProperty("IPv6协议子网网段，选择创建IPv6协议或IPv4+IPv6双栈协议子网时必选，且cidrV6只支持填写64")
    private String cidrV6;

    @ApiModelProperty("VPC名称，5~64位的中文、英文、数字、下划线，不可使用数字开头")
    @NotNull(message = "VPC名称不能为空")
    private String name;

    @ApiModelProperty("VPC首个子网名称，5~64位的中文、英文、数字、下划线。")
    @NotNull(message = "VPC首个子网名称不能为空")
    private String networkName;

    @ApiModelProperty("VPC子网类型")
    private NetworkTypeEnum networkTypeEnum;

    @ApiModelProperty("VPC首个子网所属可用区AZ，可以通过“根据routerid查看VPC详情”查询获取。")
    @NotNull(message = "VPC首个子网所属可用区不能为空")
    private String region;

    @ApiModelProperty("VPC规格")
    @NotNull(message = "VPC规格不能为空")
    private SpecsEnum specs = SpecsEnum.HIGH;

    @ApiModelProperty("系统级别标签")
    private String tagId;

    @ApiModelProperty("标签结构体")
    private List<RequestTags> tags;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vpcOrder")
                .uri("/customer/v3/order/create/vpc")
                .gatewayUri("/api/openapi-vpc/customer/v3/order/create/vpc")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcOrderRequest toRequest() {
        VpcOrderBody body = VpcOrderBody.builder()
                .cidr(cidr)
                .cidrV6(cidrV6)
                .name(name)
                .networkName(networkName)
                .networkTypeEnum(networkTypeEnum)
                .region(region)
                .specs(specs)
                .tagId(tagId)
                .tags(tags)
                .build();
        return VpcOrderRequest.builder()
                .vpcOrderBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateVpcResponse.class;
    }
}
