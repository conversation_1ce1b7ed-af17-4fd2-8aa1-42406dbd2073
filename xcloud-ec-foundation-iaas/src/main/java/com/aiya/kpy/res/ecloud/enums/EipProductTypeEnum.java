package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum EipProductTypeEnum {
    ROUTER("router"),
    NATGATEWAY("natgateway"),
    IPV6BANDWIDTH("ipv6bandwidth"),
    IP("ip"),
    BANDWIDTH("bandwidth"),
    CBWP("cbwp"),
    SHAREDFP("sharedfp"),
    IPSECVPN("ipsecvpn"),
    SSLVPN("sslvpn"),
    ELB("elb"),
    EDGEROUTER("edgerouter"),
    EDGEIP("edgeip"),
    EDGEBANDWIDTH("edgebandwidth"),
    EDGEIPV6BANDWIDTH("edgeipv6bandwidth"),
    EDGEIPV6CBWP("edgeipv6cbwp"),
    IPV6CBWP("ipv6cbwp"),
    EDGENATGATEWAY("edgenatgateway"),
    EDGECBWP("edgecbwp"),
    VPCNODE("vpcnode"),
    POPIP("popIp"),
    MCC("mcc");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
