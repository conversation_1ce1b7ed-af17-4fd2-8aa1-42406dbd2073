package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/4/12
 */
@Getter
@AllArgsConstructor
public enum DiskCinderTypeEnum {
    EBS_CEPH_CACHE("ebs_ceph_cache"),
    EBS_CEPH_SSD("ebs_ceph_ssd"),
    /**
     * 容量型
     */
    EBS_CEPH_DATA("ebs_ceph_data"),
    EBS_CEPH_SSD_IRONIC("ebs_ceph_ssd_ironic"),
    EBS_CEPH_CACHE_ARM("ebs_ceph_cache_arm"),
    ;
    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
