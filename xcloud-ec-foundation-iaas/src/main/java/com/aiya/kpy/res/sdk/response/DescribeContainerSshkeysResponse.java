package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.ContainerSshkey;

import java.util.List;

/**
 * 查询容器SSH密钥列表响应
 * 容器云平台查询SSH密钥列表API的响应类。包含分页信息和SSH密钥详细信息列表，
 * 用于返回查询到的密钥配置数据。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class DescribeContainerSshkeysResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private Integer pageNumber;
    private Integer pageSize;
    private Integer totalCount;
    private List<ContainerSshkey> info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<ContainerSshkey> getInfo() {
        return info;
    }

    public void setInfo(List<ContainerSshkey> info) {
        this.info = info;
    }
}
