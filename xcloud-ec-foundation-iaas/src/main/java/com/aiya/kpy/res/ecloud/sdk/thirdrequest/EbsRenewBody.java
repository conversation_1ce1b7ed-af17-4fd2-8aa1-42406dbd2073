package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class EbsRenewBody extends Body {
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("renewChangeParams")
    private EbsRenewChangeParams renewChangeParams;
    @SerializedName("instanceId")
    private String instanceId;
    @SerializedName("changePeriodType")
    private Boolean changePeriodType;
    @SerializedName("returnUrl")
    private String returnUrl;
    @SerializedName("resourceType")
    private ResourceTypeEnum resourceType;
}
