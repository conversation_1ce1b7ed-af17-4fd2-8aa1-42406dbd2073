package com.aiya.kpy.res.kpyun.entity;

import com.aiya.kpy.common.enums.DataStatus;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.common.enums.ResourceProvider;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Entity
@Table(
		name = "RES_ZONE_PROVIDER_ACCOUNT"
)
public class ResZoneProviderAccountEntity {
	@Id
	@Column(
			name = "ID",
			unique = true,
			nullable = false
	)
	private Long id;
	@Column(
			name = "KPY_REGION_CODE"
	)
	private String kpyRegionCode;
	@Column(
			name = "ZONE_CODE"
	)
	private String zoneCode;
	@Column(
			name = "ZONE_NAME"
	)
	private String zoneName;
	@Column(
			name = "ACCESS_KEY_ID"
	)
	private String accessKeyId;
	@Column(
			name = "ACCESS_KEY_SECRET"
	)
	private String accessKeySecret;
	@Column(
			name = "REQ_URL"
	)
	private String reqUrl;
	@Column(
			name = "CREATE_TIME"
	)
	private Date createTime;
	@Column(
			name = "REGION_CODE"
	)
	private String regionCode;
	@Column(
			name = "LINE_ID"
	)
	private String lineId;
	@Column(
			name = "ZONE"
	)
	private String zone;
	@Column(
			name = "RESOURCE_TYPE"
	)
	private ResourceType resourceType;

	@Column(
			name = "RESOURCE_PROVIDER"
	)
	private ResourceProvider resourceProvider;

	@Column(
			name = "DATA_STATUS"
	)
	public DataStatus dataStatus;

	public ResZoneProviderAccountEntity() {
	}

	public Long getId() {
		return this.id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getZoneCode() {
		return this.zoneCode;
	}

	public void setZoneCode(String zoneCode) {
		this.zoneCode = zoneCode;
	}

	public String getZoneName() {
		return this.zoneName;
	}

	public void setZoneName(String zoneName) {
		this.zoneName = zoneName;
	}

	public String getAccessKeyId() {
		return this.accessKeyId;
	}

	public void setAccessKeyId(String accessKeyId) {
		this.accessKeyId = accessKeyId;
	}

	public String getAccessKeySecret() {
		return this.accessKeySecret;
	}

	public void setAccessKeySecret(String accessKeySecret) {
		this.accessKeySecret = accessKeySecret;
	}

	public String getReqUrl() {
		return this.reqUrl;
	}

	public void setReqUrl(String reqUrl) {
		this.reqUrl = reqUrl;
	}

	public Date getCreateTime() {
		return this.createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getRegionCode() {
		return this.regionCode;
	}

	public void setRegionCode(String regionCode) {
		this.regionCode = regionCode;
	}

	public String getLineId() {
		return this.lineId;
	}

	public void setLineId(String lineId) {
		this.lineId = lineId;
	}

	public String getZone() {
		return this.zone;
	}

	public void setZone(String zone) {
		this.zone = zone;
	}

	public void setKpyRegionCode(String kpyRegionCode) {
		this.kpyRegionCode = kpyRegionCode;
	}

	public String getKpyRegionCode() {
		return this.kpyRegionCode;
	}

	public ResourceType getResourceType() {
		return this.resourceType;
	}

	public void setResourceType(ResourceType resourceType) {
		this.resourceType = resourceType;
	}

	public ResourceProvider getResourceProvider() {
		return resourceProvider;
	}

	public void setResourceProvider(ResourceProvider resourceProvider) {
		this.resourceProvider = resourceProvider;
	}

	public DataStatus getDataStatus() {
		return dataStatus;
	}

	public void setDataStatus(DataStatus dataStatus) {
		this.dataStatus = dataStatus;
	}
}