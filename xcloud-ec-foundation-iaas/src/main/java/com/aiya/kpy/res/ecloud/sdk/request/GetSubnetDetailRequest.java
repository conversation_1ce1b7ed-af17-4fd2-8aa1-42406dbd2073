package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSubnetDetailResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import com.ecloud.sdk.position.Path;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/1 20:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class GetSubnetDetailRequest extends ECloudAcsRequest<GetSubnetDetailResponse> {
    private String subnetId;
    @Override
    public Params getParams() {
        return Params.builder()
                .action("getSubnetDetailResp")
                .uri("customer/v3/subnet/subnetId/{subnetId}/SubnetDetailResp")
                .gatewayUri("/api/openapi-vpc/customer/v3/subnet/subnetId/{subnetId}/SubnetDetailResp")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public Request toRequest() {
        Request.GetSubnetDetailPath path = Request.GetSubnetDetailPath
                .builder()
                .subnetId(this.subnetId)
                .build();
        return Request.builder()
                .getSubnetDetailPath(path)
                .build();
    }

    @Override
    public Class<GetSubnetDetailResponse> getResponseClass() {
        return GetSubnetDetailResponse.class;
    }


    @Data
    @Builder
    public static class Request {
        @SerializedName("getSubnetDetailPath")
        GetSubnetDetailPath getSubnetDetailPath;

        @EqualsAndHashCode(callSuper = true)
        @Data
        @Builder
        private static class GetSubnetDetailPath extends Path {
            @SerializedName("subnetId")
            private String subnetId;
        }
    }
}
