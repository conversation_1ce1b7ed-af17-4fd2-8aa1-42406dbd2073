package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmUpdatePasswordBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmUpdatePasswordRequest;
import com.aiya.kpy.res.ecloud.sdk.response.UpdatePasswordResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePasswordRequest extends ECloudAcsRequest<UpdatePasswordResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @ApiModelProperty("云主机密码 明文8-16位字符，同时包括数字、大小写字母和特殊字符，其中特殊字符最多不能超过3个，且需要在“~ @ # $ % * _ - + = , . ? [ ] { }”范围内）")
    @NotNull(message = "云主机密码不能为空")
    private String password;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmUpdatePassword")
                .uri("/acl/v3/server/password")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/password")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmUpdatePasswordRequest toRequest() {
        VmUpdatePasswordBody body = VmUpdatePasswordBody.builder()
                .serverId(this.serverId)
                .password(this.password)
                .build();
        return VmUpdatePasswordRequest.builder()
                .vmUpdatePasswordBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return UpdatePasswordResponse.class;
    }
}
