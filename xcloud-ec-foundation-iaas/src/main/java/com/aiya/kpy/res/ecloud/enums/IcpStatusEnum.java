package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum IcpStatusEnum {
    /**
     * 服务台审批未通过
     */
    SERVICE_CHECK_NO_PASS("SERVICE_CHECK_NO_PASS"),
    /**
     * 服务台审批中
     */
    SERVICE_CHECK("SERVICE_CHECK"),
    /**
     * 未申请备案
     */
    NO_RECORD("NO_RECORD"),
    /**
     * 初审中
     */
    FIRST_CHECK("FIRST_CHECK"),
    /**
     * 初审未通过
     */
    FIRST_CHECK_NO_PASS("FIRST_CHECK_NO_PASS"),
    /**
     * 管局审核中
     */
    SECOND_CHECK("SECOND_CHECK"),
    /**
     * 管局审核通过
     */
    SUCCESS("SUCCESS"),
    /**
     * 管局审核未通过
     */
    SECOND_CHECK_NO_PASS("SECOND_CHECK_NO_PASS"),
    /**
     * 注销待审核（初审中）
     */
    CANCEL_FIRST_CHECK("CANCEL_FIRST_CHECK"),
    /**
     * 注销初审未通过
     */
    CANCEL_FIRST_NO_PASS("CANCEL_FIRST_NO_PASS"),
    /**
     * 注销初审通过（管局审核中）
     */
    CANCEL_SECOND_CHECK("CANCEL_SECOND_CHECK"),
    /**
     * 已注销（管局审核通过）
     */
    CANCEL("CANCEL"),
    /**
     * 注销失败（管局审核不通过）
     */
    CANCEL_SECOND_NO_PASS("CANCEL_SECOND_NO_PASS"),
    /**
     * 申请失败
     */
    RECORD_FAIL("RECORD_FAIL"),
    /**
     * 变更初审通过
     */
    MODIFY_FIRST_CHECK("MODIFY_FIRST_CHECK"),
    /**
     * 变更初审未通过
     */
    MODIFY_FIRST_CHECK_NO_PASS("MODIFY_FIRST_CHECK_NO_PASS"),
    /**
     * 变更管局审核中
     */
    MODIFY_SECOND_CHECK("MODIFY_SECOND_CHECK"),
    /**
     * 变更管局审核未通过
     */
    MODIFY_SECOND_CHECK_NO_PASS("MODIFY_SECOND_CHECK_NO_PASS"),
    /**
     * 二级域名备案状态, 启用
     */
    SECOND_REGISTER_ENABLE("SECOND_REGISTER_ENABLE"),
    /**
     * 二级域名备案状态, 禁止
     */
    SECOND_REGISTER_DISABLE("SECOND_REGISTER_DISABLE");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
