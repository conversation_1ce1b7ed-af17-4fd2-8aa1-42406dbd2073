package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerNicResponse;

/**
 * 创建容器网卡请求
 *
 * @Author: liuyingjie
 * @Date: 2025/1/24
 * @Description: 用于创建容器网卡的请求类，支持配置IP、网关、DNS等网络参数，基于container.swagger.json生成
 */
public class CreateContainerNicRequest extends KpyRpcAcsRequest<CreateContainerNicResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private String description;
    private String dns;
    private String dockerUuid;
    private String gateway;
    private String ip;
    private String vlanUuid;
    private String zoneUuid;

    public CreateContainerNicRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "CreateContainerNic");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "Description", description = "描述")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @KpyParam(value = "Dns", description = "DNS地址")
    public String getDns() {
        return dns;
    }

    public void setDns(String dns) {
        this.dns = dns;
    }

    @KpyParam(value = "DockerUuid", description = "挂载容器UUID")
    public String getDockerUuid() {
        return dockerUuid;
    }

    public void setDockerUuid(String dockerUuid) {
        this.dockerUuid = dockerUuid;
    }

    @KpyParam(value = "Gateway", description = "网关地址")
    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    @KpyParam(value = "Ip", description = "Ip地址")
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    @KpyParam(value = "VlanUuid", description = "VLAN UUID")
    public String getVlanUuid() {
        return vlanUuid;
    }

    public void setVlanUuid(String vlanUuid) {
        this.vlanUuid = vlanUuid;
    }

    @KpyParam(value = "ZoneUuid", description = "可用区ID")
    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    @Override
    public Class<CreateContainerNicResponse> getResponseClass() {
        return CreateContainerNicResponse.class;
    }
}
