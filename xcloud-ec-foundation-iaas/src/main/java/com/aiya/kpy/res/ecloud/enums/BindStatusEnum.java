package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Getter
@AllArgsConstructor
public enum BindStatusEnum {
    BINDING("BINDING"),
    UNBINDING("UNBINDING"),
    BIND_FAILED("BIND_FAILED"),
    UNBIND_FAILED("UNBIND_FAILED"),
    UNBIND("UNBIND"),
    BOUND("BOUND");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
