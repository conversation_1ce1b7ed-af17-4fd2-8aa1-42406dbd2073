package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChangeTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@Builder
public class EbsChangeBody extends Body {
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("size")
    private Integer size;
    @SerializedName("changeType")
    private ChangeTypeEnum changeType;
    @SerializedName("volumeId")
    private String volumeId;
    @SerializedName("returnUrl")
    private String returnUrl;
}
