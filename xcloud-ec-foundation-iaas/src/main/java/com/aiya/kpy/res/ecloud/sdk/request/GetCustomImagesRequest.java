package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.OsTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetCustomImagesQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.ImsGetCustomImagesRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetCustomImagesResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetCustomImagesRequest extends ECloudAcsRequest<GetCustomImagesResponse> {
    @ApiModelProperty("自定义镜像id")
    private String imageId;

    @ApiModelProperty("云主机id")
    private String serverId;

    @ApiModelProperty("自定义镜像名称")
    private String name;

    @ApiModelProperty("自定义镜像操作系统类型")
    private List<OsTypeEnum> imageOsTypes;

    @ApiModelProperty("标签Id集合")
    private List<String> tagIds;

    @ApiModelProperty("列表分页每页大小")
    private Integer pageSize;

    @ApiModelProperty("列表分页页数")
    private Integer page;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listImageRespV2")
                .uri("/image")
                .gatewayUri("/api/openapi-ims/user/v5/image")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public ImsGetCustomImagesRequest toRequest() {
        ImsGetCustomImagesQuery query = ImsGetCustomImagesQuery.builder()
                .imageId(imageId)
                .serverId(serverId)
                .name(name)
                .imageOsTypes(imageOsTypes)
                .tagIds(tagIds)
                .pageSize(pageSize)
                .page(page)
                .build();
        return ImsGetCustomImagesRequest.builder()
                .listImageRespV2Query(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetCustomImagesResponse.class;
    }
}
