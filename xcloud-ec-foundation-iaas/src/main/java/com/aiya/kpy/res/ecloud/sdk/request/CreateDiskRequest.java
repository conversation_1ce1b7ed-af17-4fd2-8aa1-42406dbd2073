package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChargePeriodEnum;
import com.aiya.kpy.res.ecloud.enums.DiskTagIdEnum;
import com.aiya.kpy.res.ecloud.enums.ProductTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateDiskResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsCreateBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsCreateRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsCreateRequestChannelInfoList;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.RequestTags;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateDiskRequest extends ECloudAcsRequest<CreateDiskResponse> {
    @ApiModelProperty("硬盘cinder类型，调用接口查询")
    @NotNull(message = "硬盘cinder类型不能为空")
    private String cinderType;

    @ApiModelProperty("硬盘名称,5到22位中文英文数字下划线组成")
    @NotNull(message = "硬盘名称不能为空")
    private String name;

    @ApiModelProperty("硬盘大小")
    @NotNull(message = "硬盘大小不能为空")
    private Integer size;

    @ApiModelProperty("硬盘数量")
    @NotNull(message = "硬盘数量不能为空")
    private Integer quantity;

    @ApiModelProperty("是否设置为共享盘")
    @NotNull(message = "是否设置为共享盘不能为空")
    private Boolean share;

    @ApiModelProperty("备份id")
    private String backupId;

    @ApiModelProperty("快照id")
    private String snapshotId;

    @ApiModelProperty("标签")
    private List<RequestTags> tags;

    @ApiModelProperty("加密所需参数，填true，false或加密类型")
    private String encryptedParam;

    @ApiModelProperty("产品类型 固定为NORMAL")
    @NotNull(message = "产品类型不能为空")
    private ProductTypeEnum productType;

    @ApiModelProperty("底层可用区")
    @NotNull(message = "底层可用区不能为空")
    private String region;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("硬盘创建后需挂载的主机id")
    private String serverId;

    @ApiModelProperty("云硬盘备份策略")
    private String backupPolicyId;

    @ApiModelProperty("云硬盘快照策略")
    private String snapshotPolicyId;

    @ApiModelProperty("订购渠道参数列表")
    private List<EbsCreateRequestChannelInfoList> channelInfoList;

    @ApiModelProperty("订购周期类型")
    private ChargePeriodEnum periodType;

    @ApiModelProperty("订购周期数，单位：月，后付费包年和预付费填写")
    private Integer periodNum;

    @ApiModelProperty("标签ID")
    private DiskTagIdEnum tagId;

    @ApiModelProperty("预付费支付成功跳转地址，预付费必填")
    private String returnUrl;

    @ApiModelProperty("互联网客户后付费阀值")
    private String limitMoney;

    @ApiModelProperty("冻结的配额流水号")
    private String serialNo;

    @ApiModelProperty("密钥id，云硬盘加密功能")
    private String keyId;

    @ApiModelProperty("是否自动续订标识，true为自动续订，默认为false")
    private Boolean autoRenew;

    @ApiModelProperty("自动释放时间 yyyy-mm-dd hh:mm:ss")
    private String autoEndTime;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("createVolume")
                .uri("/acl/v3/volume/order/volume")
                .gatewayUri("/api/v2/volume/volume/order/volume")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsCreateRequest toRequest() {
        EbsCreateBody body = EbsCreateBody.builder()
                .periodNum(this.periodNum)
                .backupPolicyId(this.backupPolicyId)
                .tagId(this.tagId)
                .backupId(this.backupId)
                .description(this.description)
                .keyId(this.keyId)
                .serverId(this.serverId)
                .autoEndTime(this.autoEndTime)
                .cinderType(this.cinderType)
                .autoRenew(this.autoRenew)
                .share(this.share)
                .returnUrl(this.returnUrl)
                .productType(this.productType)
                .quantity(this.quantity)
                .snapshotId(this.snapshotId)
                .vaz(this.vaz)
                .tags(this.tags)
                .serialNo(this.serialNo)
                .channelInfoList(this.channelInfoList)
                .periodType(this.periodType)
                .size(this.size)
                .name(this.name)
                .limitMoney(this.limitMoney)
                .region(this.region)
                .encryptedParam(this.encryptedParam)
                .snapshotPolicyId(this.snapshotPolicyId)
                .build();

        return EbsCreateRequest.builder()
                .ebsCreateBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateDiskResponse.class;
    }
}
