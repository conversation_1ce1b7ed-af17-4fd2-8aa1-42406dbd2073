package com.aiya.kpy.res.ecloud.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/4/7
 */
public class HibernateValidator {
    private static final Validator validator = Validation.buildDefaultValidatorFactory().getValidator();

    /**
     * 校验实体类
     */
    public static <T> void validate(T t) {
        Set<ConstraintViolation<T>> constraintViolations = validator.validate(t);
        if (constraintViolations.size() > 0) {
            StringBuilder validateError = new StringBuilder();
            for (ConstraintViolation<T> constraintViolation : constraintViolations) {
                validateError.append(constraintViolation.getPropertyPath() + ":" + constraintViolation.getInvalidValue() + "," + constraintViolation.getMessage()).append(";");
            }
            throw new IllegalArgumentException(validateError.toString());
        }
    }
}
