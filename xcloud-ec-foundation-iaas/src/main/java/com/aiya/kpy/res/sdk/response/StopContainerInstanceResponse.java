package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;

/**
 * 停止容器实例响应
 * 容器云平台停止容器实例API的响应类。包含请求ID、主机ID和任务UUID等信息，
 * 用于跟踪容器停止操作的执行状态。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class StopContainerInstanceResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private String taskUuid;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public String getTaskUuid() {
        return taskUuid;
    }

    public void setTaskUuid(String taskUuid) {
        this.taskUuid = taskUuid;
    }
}
