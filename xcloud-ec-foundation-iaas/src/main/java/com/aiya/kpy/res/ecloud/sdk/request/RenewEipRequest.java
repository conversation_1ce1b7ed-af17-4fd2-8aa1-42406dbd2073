package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.RenewEipResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipRenewBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipRenewRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewEipRequest extends ECloudAcsRequest<RenewEipResponse> {
    @ApiModelProperty("续订时长，续订周期保持为原订单的计费周期：计费周期为month时，取值为1~12（月），取值为0时默认订购1个月同时开启自动续订；计费周期为year时，取值为[年数*12月]，最多5年")
    @NotNull(message = "续订时长不能为空")
    private Integer duration;

    @ApiModelProperty("续订的带宽ID")
    @NotNull(message = "续订的带宽ID不能为空")
    private String resourceId;

    @ApiModelProperty("系统级别标签，续订时传入该入参则续订的弹性公网IP仅能通过API接口退订")
    private String tagId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("commonMopOrderRenewEip")
                .uri("/customer/v3/order/renew")
                .gatewayUri("/api/openapi-eip/customer/v3/order/renew")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipRenewRequest toRequest() {
        EipRenewBody body = EipRenewBody.builder()
                .duration(this.duration)
                .productType(EipProductTypeEnum.BANDWIDTH)
                .resourceId(this.resourceId)
                .tagId(this.tagId)
                .build();
        return EipRenewRequest.builder()
                .commonMopOrderRenewEipBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return RenewEipResponse.class;
    }
}
