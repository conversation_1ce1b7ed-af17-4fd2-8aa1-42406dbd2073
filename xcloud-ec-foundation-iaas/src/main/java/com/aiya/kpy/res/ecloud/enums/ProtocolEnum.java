package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Getter
@AllArgsConstructor
public enum ProtocolEnum {
    /**
     * 可用于完全互相信任的应用场景
     */
    ANY("ANY"),
    /**
     * 用于VPC服务
     */
    GRE("gre"),
    /**
     * 使用PING程序检测实例之间的通信状况
     */
    ICMP("ICMP"),
    /**
     * Internet组管理协议
     */
    IGMP("IGMP"),
    /**
     * 传输控制协议
     */
    TCP("TCP"),
    /**
     * 用户数据报协议
     */
    UDP("UDP"),
    /**
     * 虚拟路由器冗余协议
     */
    VRRP("VRRP");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
