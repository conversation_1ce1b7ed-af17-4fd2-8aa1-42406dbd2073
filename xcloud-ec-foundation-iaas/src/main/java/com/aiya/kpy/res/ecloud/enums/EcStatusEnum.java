package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 云主机状态
 *
 * <AUTHOR>
 * @date 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum EcStatusEnum {
    /**
     * 激活
     */
    ACTIVE("ACTIVE", 1),
    /**
     * 构建
     */
    BUILD("BUILD", 2),
    /**
     * 重构
     */
    REBUILD("REBUILD", 3),
    /**
     * 休眠
     */
    SUSPENDED("SUSPENDED", 4),
    /**
     * 暂停
     */
    PAUSED("PAUSED", 5),
    /**
     * 规格变更
     */
    RESIZE("RESIZE", 6),
    /**
     * 检查是否可规格变更
     */
    VERIFY_RESIZE("VERIFY_RESIZE", 7),
    /**
     * 规格变更回退
     */
    REVERT_RESIZE("REVERT_RESIZE", 8),
    /**
     * 更新密码
     */
    PASSWORD("PASSWORD", 9),
    /**
     * 重启
     */
    REBOOT("REBOOT", 10),
    /**
     * 硬重启
     */
    HARD_REBOOT("HARD_REBOOT", 11),
    /**
     * 已删除
     */
    DELETED("DELETED", 12),
    /**
     * 错误
     */
    ERROR("ERROR", 14),
    /**
     * 已停止
     */
    STOPPED("STOPPED", 15),
    /**
     * 已关闭
     */
    SHUTOFF("SHUTOFF", 16),
    /**
     * 迁移中
     */
    MIGRATING("MIGRATING", 17),
    /**
     * 未知
     */
    UNKNOWN("UNKNOWN", 18),
    /**
     * 备份中
     */
    BACKUPING("BACKUPING", 19),

    RESCUE("RESCUE", 101),
    SHELVED("SHELVED", 102),
    SHELVED_OFFLOADED("SHELVED_OFFLOADED", 103),
    SHELVED_SAVED("SHELVED_SAVED", 104),
    SOFT_DELETED("SOFT_DELETED", 105),
    /**
     * 未知
     */
    UNRECOGNIZED("UNRECOGNIZED", 106),
    /**
     * 离线
     */
    DOWN("DOWN", 107),
    /**
     * 创建中
     */
    PENDING_CREATE("PENDING_CREATE", 108),
    /**
     * 更新中
     */
    PENDING_UPDATE("PENDING_UPDATE", 109),
    /**
     * 删除中
     */
    PENDING_DELETE("PENDING_DELETE", 110),
    /**
     * 等待
     */
    PENDING("PENDING", 111);

    private final String value;
    private final Integer code;

    @Override
    public String toString() {
        return this.value;
    }

    public static EcStatusEnum fromValue(String value) {
        for (EcStatusEnum ecStatusEnum : EcStatusEnum.values()) {
            if (ecStatusEnum.value.equals(value)) {
                return ecStatusEnum;
            }
        }
        return null;
    }

    public static EcStatusEnum fromCode(Integer code) {
        for (EcStatusEnum ecStatusEnum : EcStatusEnum.values()) {
            if (ecStatusEnum.code.equals(code)) {
                return ecStatusEnum;
            }
        }
        return null;
    }
}
