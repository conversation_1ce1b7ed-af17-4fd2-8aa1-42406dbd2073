package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmDeleteQuery extends Query {
    @SerializedName("dataVolumeDelete")
    private Boolean dataVolumeDelete;
    @SerializedName("publicIpDelete")
    private Boolean publicIpDelete;
}
