package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.BindStatusEnum;
import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetPortListResponseContent {
    @ApiModelProperty("子网id")
    private String subnetId;

    @ApiModelProperty("网卡绑定资源操作状态")
    private BindStatusEnum operationStatus;

    @ApiModelProperty("绑定的资源id")
    private String resourceId;

    @ApiModelProperty("vpc名称")
    private String vpcName;

    private String vpoolId;

    @ApiModelProperty("源目的地址检查开关")
    private Boolean addressCheck;

    @ApiModelProperty("公网IP的id")
    private String fipId;

    @ApiModelProperty("内网ip")
    private String privateIp;

    @ApiModelProperty("公网IP的带宽,单位:Mb")
    private Integer bandwidthSize;

    @ApiModelProperty("公网IP的带宽id")
    private String bandwidthId;

    @ApiModelProperty("创建来源")
    private Boolean source;

    @ApiModelProperty("类型")
    private PortTypeEnum type;

    @ApiModelProperty("AZ")
    private String availabilityZone;

    @ApiModelProperty("公网IP的带宽计费方式（按量、按带宽)")
    private ChargeModeEnum chargeModeEnum;

    @ApiModelProperty("子网名称")
    private String subnetName;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("routerId")
    private String routerId;

    @ApiModelProperty("vpcId")
    private String vpcId;

    @ApiModelProperty("是否已绑定公网IP")
    private Boolean fipBind;

    @ApiModelProperty("fixedIpResps")
    private List<GetPortListResponseIp> fixedIpResps;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("网络id")
    private String networkId;

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("是否启动")
    private Boolean adminStateUp;

    @ApiModelProperty("网卡关联的安全组列表")
    private List<GetPortListResponseSecurityGroup> securityGroupResps;

    @ApiModelProperty("网卡关联的辅助IP列表")
    private List<GetPortListResponseSecondary> secondaryIpResps;

    @ApiModelProperty("是否开启了ipv6带宽")
    private Boolean ipv6Status;

    @ApiModelProperty("公网IP")
    private String publicIp;

    @ApiModelProperty("绑定的资源名称")
    private String resourceName;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("mac地址")
    private String macAddress;

    @ApiModelProperty("是否已删除")
    private Boolean deleted;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("是否是基础网络上的")
    private Boolean basic;

    @ApiModelProperty("可用区")
    private String region;
}
