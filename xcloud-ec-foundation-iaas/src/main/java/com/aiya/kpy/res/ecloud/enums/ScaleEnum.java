package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Getter
@AllArgsConstructor
public enum ScaleEnum {
    /**
     * 普通型VPC
     */
    NORMAL("normal"),
    /**
     * 高性能型VPC
     */
    HIGH("high"),
    /**
     * 中间型VPC
     */
    MIDDLE("middle"),
    /**
     * 超级VPC
     */
    MEGA("mega");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
