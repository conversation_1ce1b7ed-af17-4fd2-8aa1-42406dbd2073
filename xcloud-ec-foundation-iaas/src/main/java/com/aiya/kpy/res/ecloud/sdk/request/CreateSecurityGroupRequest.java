package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateSecurityGroupBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateSecurityGroupRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateSecurityGroupResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateSecurityGroupRequest extends ECloudAcsRequest<CreateSecurityGroupResponse> {
    @ApiModelProperty("安全组描述，100字符以内")
    private String description;

    @ApiModelProperty("安全组名称，5-22位数字、字母和下划线的组合（必须以字母开头）")
    @NotNull(message = "安全组名称不能为空")
    private String name;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("安全组是否为有状态")
    private Boolean stateful;

    @ApiModelProperty("安全组类型")
    private ResourceTypeEnum type;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("createSecurityGroup")
                .uri("/customer/v3/SecurityGroup")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroup")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcCreateSecurityGroupRequest toRequest() {
        VpcCreateSecurityGroupBody body = VpcCreateSecurityGroupBody.builder()
                .description(description)
                .name(name)
                .region(region)
                .stateful(stateful)
                .type(type)
                .build();
        return VpcCreateSecurityGroupRequest.builder()
                .createSecurityGroupBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateSecurityGroupResponse.class;
    }
}
