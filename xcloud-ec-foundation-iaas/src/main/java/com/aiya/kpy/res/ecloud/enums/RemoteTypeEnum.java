package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Getter
@AllArgsConstructor
public enum RemoteTypeEnum {
    /**
     * 安全组访问
     */
    SECURITY_GROUP("security_group"),
    /**
     * 地址段访问
     */
    CIDR("cidr");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
