package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetInstancesResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmServerListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmServerListRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/1
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetInstancesRequest extends ECloudAcsRequest<GetInstancesResponse> {
    @ApiModelProperty("云主机状态")
    private List<EcStatusEnum> ecStatusEnums;

    @ApiModelProperty("云主机类型")
    @NotNull(message = "云主机类型不能为空")
    private List<ServerTypeEnum> serverTypeEnums;

    @ApiModelProperty("云主机所属产品类型")
    @NotNull(message = "云主机所属产品类型不能为空")
    private List<ProductTypeEnum> productTypeEnums;

    @ApiModelProperty("云主机操作系统类型")
    private List<OsTypeEnum> osTypeEnums;

    @ApiModelProperty("云主机名称")
    private String serverName;

    @ApiModelProperty("云主机id")
    private String serverId;

    @ApiModelProperty("cpu个数")
    private Integer cpu;

    @ApiModelProperty("内存大小,单位MB")
    private Integer ram;

    @ApiModelProperty("系统盘大小")
    private Integer disk;

    @ApiModelProperty("云主机op侧状态")
    private OpStatusEnum opStatusEnum;

    @ApiModelProperty("公网ip地址")
    private String publicIp;

    @ApiModelProperty("内网ip地址")
    private String privateIp;

    @ApiModelProperty("安全组id")
    private String securityGroupId;

    @ApiModelProperty("云主机密钥名称")
    private String keyName;

    @ApiModelProperty("云主机是否绑定备份策略")
    private Boolean unBindBackupPolicy;

    @ApiModelProperty("云主机是否绑定备份策略id")
    private String backupPolicyId;

    @ApiModelProperty("云主机是否有备份")
    private Boolean hasBackup;

    @ApiModelProperty("云主机是否可见")
    @NotNull(message = "云主机是否可见不能为空")
    private Boolean visible;

    @ApiModelProperty("模糊查询云主机名称或者公网ip")
    private String queryWordName;

    @ApiModelProperty("模糊查询云主机id")
    private String queryWordId;

    @ApiModelProperty("查询分页")
    private Integer page;

    @ApiModelProperty("查询分页大小")
    private Integer pageSize;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmlistServerResp")
                .uri("/v3/server/without/network")
                .gatewayUri("/api/openapi-console-web/v3/server/without/network")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmServerListRequest toRequest() {
        VmServerListQuery query = VmServerListQuery.builder()
                .unBindBackupPolicy(this.unBindBackupPolicy)
                .ecStatus(this.ecStatusEnums)
                .visible(true)
                .backupPolicyId(this.backupPolicyId)
                .serverTypes(this.serverTypeEnums)
                .privateIp(this.privateIp)
                .imageOsTypes(this.osTypeEnums)
                .keyName(this.keyName)
                .serverName(this.serverName)
                .cpu(this.cpu)
                .productTypes(this.productTypeEnums)
                .publicIp(this.publicIp)
                .queryWordName(this.queryWordName)
                .serverId(this.serverId)
                .hasBackup(this.hasBackup)
                .queryWordId(this.queryWordId)
                .securityGroupId(this.securityGroupId)
                .disk(this.disk)
                .size(this.pageSize)
                .page(this.page)
                .opStatus(this.opStatusEnum)
                .ram(this.ram)
                .build();
        return VmServerListRequest.builder()
                .vmlistServerRespQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetInstancesResponse.class;
    }
}
