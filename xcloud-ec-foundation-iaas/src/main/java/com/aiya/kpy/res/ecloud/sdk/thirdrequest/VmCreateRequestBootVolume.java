package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.DiskPerformanceEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VmCreateRequestBootVolume {
    @SerializedName("volumeType")
    private DiskPerformanceEnum volumeType;
    @SerializedName("size")
    private Integer size;
}
