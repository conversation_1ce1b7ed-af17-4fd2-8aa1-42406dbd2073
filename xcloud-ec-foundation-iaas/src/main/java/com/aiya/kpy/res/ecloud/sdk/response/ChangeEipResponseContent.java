package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import com.google.gson.annotations.SerializedName;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
public class ChangeEipResponseContent {
    @SerializedName("orderExtId")
    private String orderExtId;
    @SerializedName("orderExtStatus")
    private Integer orderExtStatus;
    @SerializedName("productType")
    private EipProductTypeEnum productType;
}
