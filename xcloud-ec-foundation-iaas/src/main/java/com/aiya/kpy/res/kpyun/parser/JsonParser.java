package com.aiya.kpy.res.kpyun.parser;

import com.aiya.kpy.res.kpyun.exception.ClientException;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;
import com.alibaba.fastjson.JSON;


/**
 * <AUTHOR> on 2017/4/6.
 */
public class JsonParser implements ResponseParser {
	@Override
	public <T extends KpyAcsResponse> T parser(String content, Class<T> clazz) throws ClientException {
		return JSON.parseObject(content, clazz);
	}
}
