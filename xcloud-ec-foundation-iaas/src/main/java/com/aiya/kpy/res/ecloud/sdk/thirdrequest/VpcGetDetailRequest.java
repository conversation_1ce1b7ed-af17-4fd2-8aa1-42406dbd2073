package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class VpcGetDetailRequest {
    @SerializedName("getVpcDetailRespByVpcIdPath")
    private VpcGetDetailPath getVpcDetailRespByVpcIdPath;
    @SerializedName("getVpcDetailRespByVpcIdQuery")
    private VpcGetDetailQuery getVpcDetailRespByVpcIdQuery;
}
