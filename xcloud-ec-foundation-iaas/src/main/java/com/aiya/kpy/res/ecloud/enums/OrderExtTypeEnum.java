package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum OrderExtTypeEnum {
    VM("vm"),
    PERFORMANCEOPTIMIZATION("performanceOptimization"),
    HIGHPERFORMANCE("highPerformance"),
    PERFORMANCEOPTIMIZATIONYC("performanceOptimizationyc"),
    HIGHPERFORMANCEYC("highPerformanceyc"),
    CAPACITY("capacity"),
    CAPEBS("capebs"),
    SSDEBS("ssdebs"),
    SSD("ssd"),
    CAPEBSYC("capebsyc"),
    SSDEBSYC("ssdebsyc"),
    SSDYC("ssdyc"),
    BANDWIDTH("bandwidth"),
    IP("ip"),
    IPV6BANDWIDTH("ipv6bandwidth"),
    IRONIC("ironic"),
    ESSDL2("essdl2"),
    ESSDL3("essdl3"),
    EDGESSDEBS("edgessdebs");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }

}
