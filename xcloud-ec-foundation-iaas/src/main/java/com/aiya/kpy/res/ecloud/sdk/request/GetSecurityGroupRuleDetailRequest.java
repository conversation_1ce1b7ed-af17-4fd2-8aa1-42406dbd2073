package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSecurityGroupRuleDetailResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupRuleDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupRuleDetailRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/27
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSecurityGroupRuleDetailRequest extends ECloudAcsRequest<GetSecurityGroupRuleDetailResponse> {
    @ApiModelProperty("安全组规则ID")
    @NotNull(message = "安全组规则ID不能为空")
    private String sgRuleId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("getSecurityGroupRuleDetailResp")
                .uri("/customer/v3/SecurityGroupRule/{sgRuleId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroupRule/{sgRuleId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetSecurityGroupRuleDetailRequest toRequest() {
        VpcGetSecurityGroupRuleDetailPath path = VpcGetSecurityGroupRuleDetailPath.builder()
                .sgRuleId(sgRuleId)
                .build();
        return VpcGetSecurityGroupRuleDetailRequest.builder()
                .getSecurityGroupRuleDetailRespPath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetSecurityGroupRuleDetailResponse.class;
    }
}
