package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.RenewDiskResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsRenewBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsRenewChangeParams;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsRenewRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewDiskRequest extends ECloudAcsRequest<RenewDiskResponse> {
    @ApiModelProperty("资源Id")
    @NotNull(message = "资源Id不能为空")
    private String instanceId;

    @ApiModelProperty("订购时长,增加的时长，单位为原来的周期单位或变更后的周期单位")
    @NotNull(message = "订购时长不能为空")
    private Integer duration;

    @ApiModelProperty("预付费支付成功跳转地址")
    private String returnUrl;

    @ApiModelProperty("资源续订变更参数")
    private EbsRenewChangeParams renewChangeParams;

    @ApiModelProperty("是否变更订购周期类型")
    private Boolean changePeriodType;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("renewVolume")
                .uri("/acl/v3/volume/renew/ebs")
                .gatewayUri("/api/v2/volume/volume/renew/ebs")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsRenewRequest toRequest() {
        EbsRenewBody body = EbsRenewBody.builder()
                .instanceId(this.instanceId)
                .duration(this.duration)
                .returnUrl(this.returnUrl)
                .resourceType(ResourceTypeEnum.VOLUME)
                .changePeriodType(this.changePeriodType)
                .renewChangeParams(this.renewChangeParams)
                .build();
        return EbsRenewRequest.builder().renewVolumeBody(body).build();
    }

    @Override
    public Class getResponseClass() {
        return RenewDiskResponse.class;
    }
}
