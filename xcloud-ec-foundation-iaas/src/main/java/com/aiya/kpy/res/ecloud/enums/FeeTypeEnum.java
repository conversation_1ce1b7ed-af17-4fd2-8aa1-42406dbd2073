package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 缴费周期
 *
 * <AUTHOR>
 * @date 2024/4/25
 */
@Getter
@AllArgsConstructor
public enum FeeTypeEnum {
    /**
     * 周期
     */
    DURATION("DURATION"),
    /**
     * 按量
     */
    SIZE("SIZE");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
