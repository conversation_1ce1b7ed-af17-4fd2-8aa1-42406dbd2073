package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmServerBackupPolicy;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class GetInstancesResponseContent {
    @ApiModelProperty("裸金属nodeId")
    @SerializedName("ironicNodeId")
    private String ironicNodeId;

    @ApiModelProperty("operationFlag")
    @SerializedName("operationFlag")
    private Integer operationFlag;

    @ApiModelProperty("群组id")
    @SerializedName("groupId")
    private String groupId;

    @ApiModelProperty("主机是否封堵")
    @SerializedName("adminPaused")
    private Boolean adminPaused;

    @ApiModelProperty("az")
    @SerializedName("availabilityZone")
    private String availabilityZone;

    @ApiModelProperty("内存大小")
    @SerializedName("vmemory")
    private Integer vmemory;

    @ApiModelProperty("是否放入回收站")
    @SerializedName("recycle")
    private Boolean recycle;

    @ApiModelProperty("创建时间")
    @SerializedName("createdTime")
    private String createdTime;

    @ApiModelProperty("云主机id")
    @SerializedName("id")
    private String id;

    @ApiModelProperty("宿主机集群ID")
    @SerializedName("deCloudId")
    private String deCloudId;

    @ApiModelProperty("云主机是否开启实例释放保护")
    @SerializedName("releaseProtect")
    private Boolean releaseProtect;

    @ApiModelProperty("云主机volume类型")
    @SerializedName("bootVolumeType")
    private DiskPerformanceEnum bootVolumeType;

    @ApiModelProperty("云主机底层状态")
    @SerializedName("ecStatus")
    private EcStatusEnum ecStatus;

    @ApiModelProperty("系统镜像名称")
    @SerializedName("imageName")
    private String imageName;

    @ApiModelProperty("云主机是否可见")
    @SerializedName("visible")
    private Boolean visible;

    @ApiModelProperty("边缘计算虚拟可用区")
    @SerializedName("vaz")
    private String vaz;

    @ApiModelProperty("底层任务描述")
    @SerializedName("task")
    private String task;

    @ApiModelProperty("放入回收站次数")
    @SerializedName("recycleCount")
    private Integer recycleCount;

    @ApiModelProperty("是否赋予可见权限")
    @SerializedName("authority")
    private Boolean authority;

    @ApiModelProperty("云主机类型")
    @SerializedName("serverType")
    private ServerTypeEnum serverType;

    @ApiModelProperty("云主机名称")
    @SerializedName("name")
    private String name;

    @ApiModelProperty("规格id")
    @SerializedName("flavorRef")
    private String flavorRef;

    @ApiModelProperty("云主机类型下的主机类型")
    @SerializedName("serverVmType")
    private VmTypeEnum serverVmType;

    @ApiModelProperty("云主机状态")
    @SerializedName("opStatus")
    private OpStatusEnum opStatus;

    @ApiModelProperty("可用区")
    @SerializedName("region")
    private String region;

    @ApiModelProperty("系统盘大小")
    @SerializedName("vdisk")
    private Integer vdisk;

    @ApiModelProperty("最大可挂载硬盘数量")
    @SerializedName("maxVolumes")
    private Integer maxVolumes;

    @ApiModelProperty("资源状态")
    @SerializedName("status")
    private Integer status;

    @ApiModelProperty("放入回收站时间")
    @SerializedName("recycleTime")
    private String recycleTime;

    @ApiModelProperty("镜像操作系统类型")
    @SerializedName("imageOsType")
    private OsTypeEnum imageOsType;

    @ApiModelProperty("规格名称")
    @SerializedName("specsName")
    private String specsName;

    @ApiModelProperty("cpu个数")
    @SerializedName("vcpu")
    private Integer vcpu;

    @ApiModelProperty("云主机描述")
    @SerializedName("description")
    private String description;

    @ApiModelProperty("云主机可信状态")
    @SerializedName("credibleStatus")
    private CredibleStatusEnum credibleStatus;

    @ApiModelProperty("是否跨可用区迁移中。false不是跨可用区迁移中，true跨可用区迁移中")
    @SerializedName("crossRegionMigrate")
    private Boolean crossRegionMigrate;

    @ApiModelProperty("封堵提示")
    @SerializedName("adminPausedTip")
    private String adminPausedTip;

    @ApiModelProperty("云主机属于哪个产品类型")
    @SerializedName("productType")
    private ProductTypeEnum productType;

    @ApiModelProperty("密钥对名称")
    @SerializedName("keyName")
    private String keyName;

    @ApiModelProperty("云主机备份策略返回体")
    @SerializedName("serverBackupPolicyResp")
    private VmServerBackupPolicy serverBackupPolicyResp;

    @ApiModelProperty("系统盘id")
    @SerializedName("systemDiskId")
    private String systemDiskId;

    @ApiModelProperty("用户id")
    @SerializedName("userId")
    private String userId;

    @ApiModelProperty("内循环账号资源是否展示")
    @SerializedName("isShow")
    private Boolean isShow;

    @ApiModelProperty("所属物理机编号")
    @SerializedName("hwHost")
    private String hwHost;

    @ApiModelProperty("回收状态")
    @SerializedName("recycleStatus")
    private RecycleStatusEnum recycleStatus;

    @ApiModelProperty("是否已删除")
    @SerializedName("deleted")
    private Integer deleted;

    @ApiModelProperty("网卡数量")
    @SerializedName("maxPorts")
    private Integer maxPorts;

    @ApiModelProperty("宿主机名称")
    @SerializedName("deCloudServerName")
    private String deCloudServerName;

    @ApiModelProperty("系统镜像id")
    @SerializedName("imageRef")
    private String imageRef;

    @ApiModelProperty("所属物理机id")
    @SerializedName("hwHostId")
    private String hwHostId;

    @ApiModelProperty("关机模式。false关机继续收费、true关机不收费")
    @SerializedName("stoppedMode")
    private Boolean stoppedMode;
}
