package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsDeleteBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsDeleteRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteDiskResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteDiskRequest extends ECloudAcsRequest<DeleteDiskResponse> {
    @ApiModelProperty("资源id")
    private String resourceId;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("preDeleteResources")
                .uri("/acl/v3/common/resource/preDelete")
                .gatewayUri("/api/ebs/acl/v3/common/resource/preDelete")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsDeleteRequest toRequest() {
        EbsDeleteBody body = EbsDeleteBody.builder()
                .resourceId(this.resourceId)
                .resourceType(ResourceTypeEnum.VOLUME)
                .build();
        return EbsDeleteRequest.builder()
                .preDeleteResourcesBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteDiskResponse.class;
    }
}
