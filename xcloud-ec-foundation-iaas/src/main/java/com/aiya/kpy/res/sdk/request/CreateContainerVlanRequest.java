package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerVlanResponse;

/**
 * 创建容器VLAN请求
 *
 * @Author: liuyingjie
 * @Date: 2025/1/24
 * @Description: 用于创建容器VLAN的请求类，支持配置CIDR、网关、DNS等网络参数，基于container.swagger.json生成
 */
public class CreateContainerVlanRequest extends KpyRpcAcsRequest<CreateContainerVlanResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private String cidr;
    private String description;
    private String dns;
    private String exclude;
    private String gateway;
    private Boolean isInternal;
    private Integer vni;
    private String zoneUuid;

    public CreateContainerVlanRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "CreateContainerVlan");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "Cidr", description = "网络配置")
    public String getCidr() {
        return cidr;
    }

    public void setCidr(String cidr) {
        this.cidr = cidr;
    }

    @KpyParam(value = "Description", description = "描述信息")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @KpyParam(value = "Dns", description = "DNS服务器地址")
    public String getDns() {
        return dns;
    }

    public void setDns(String dns) {
        this.dns = dns;
    }

    @KpyParam(value = "Exclude", description = "排除地址范围")
    public String getExclude() {
        return exclude;
    }

    public void setExclude(String exclude) {
        this.exclude = exclude;
    }

    @KpyParam(value = "Gateway", description = "网关地址 (可选)")
    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    @KpyParam(value = "IsInternal", description = "是否内部网络 (true=内部, false=公网)(必填)")
    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    @KpyParam(value = "Vni", description = "网络标识")
    public Integer getVni() {
        return vni;
    }

    public void setVni(Integer vni) {
        this.vni = vni;
    }

    @KpyParam(value = "ZoneUuid", description = "可用区ID (可选)")
    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    @Override
    public Class<CreateContainerVlanResponse> getResponseClass() {
        return CreateContainerVlanResponse.class;
    }
}
