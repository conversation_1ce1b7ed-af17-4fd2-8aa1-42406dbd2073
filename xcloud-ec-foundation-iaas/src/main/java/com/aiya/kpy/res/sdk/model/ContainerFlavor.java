package com.aiya.kpy.res.sdk.model;

import java.util.Date;

/**
 * ContainerFlavor 容器规格模型
 *
 * 用于容器云平台的容器规格配置模型，包含容器的CPU、
 * 内存等硬件资源配置信息。支持容器的资源规格管理
 * 和性能配置。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class ContainerFlavor {
    private String uuid;
    private String alias;
    private Integer cpu;
    private Integer mem;
    private Date createdAt;
    private Date updatedAt;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public Integer getCpu() {
        return cpu;
    }

    public void setCpu(Integer cpu) {
        this.cpu = cpu;
    }

    public Integer getMem() {
        return mem;
    }

    public void setMem(Integer mem) {
        this.mem = mem;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }
}
