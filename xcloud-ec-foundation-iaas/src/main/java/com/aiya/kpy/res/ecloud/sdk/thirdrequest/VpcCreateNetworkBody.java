package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class VpcCreateNetworkBody extends Body {
    @SerializedName("availabilityZoneHints")
    private String availabilityZoneHints;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("networkName")
    private String networkName;
    @SerializedName("subnets")
    private List<VpcCreateNetworkRequestSubnets> subnets;
    @SerializedName("networkTypeEnum")
    private NetworkTypeEnum networkTypeEnum;
    @SerializedName("tags")
    private List<RequestTags> tags;
}
