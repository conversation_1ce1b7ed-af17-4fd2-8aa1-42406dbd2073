package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetIpListWithBandwithResponse implements ECloudResponseBody {
    private Integer total;
    private List<GetIpListWithBandwithResponseContent> content;
    private Boolean empty;
    private Integer page;
    private Integer pageSize;
}
