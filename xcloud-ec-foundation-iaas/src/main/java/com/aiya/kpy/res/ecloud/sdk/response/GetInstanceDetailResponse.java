package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.common.enums.DiskType;
import com.aiya.kpy.ec.foundation.iaas.model.DiskModel;
import com.aiya.kpy.res.ecloud.enums.DiskPerformanceEnum;
import com.aiya.kpy.res.ecloud.enums.EcStatusEnum;
import com.aiya.kpy.res.ecloud.enums.ServerVmTypeEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailResponsePorts;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailResponseServerBackupPolicyResp;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailResponseVolumes;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetInstanceDetailResponse implements ECloudResponseBody {
    @ApiModelProperty("云主机系统盘类型")
    private DiskPerformanceEnum bootVolumeType;

    @ApiModelProperty("修改时间")
    private String modifiedTime;

    @ApiModelProperty("镜像id")
    private String imageId;

    @ApiModelProperty("镜像名称")
    private String imageName;

    @ApiModelProperty("内存大小")
    private Integer memory;

    @ApiModelProperty("云主机规格名称")
    private String specsName;

    @ApiModelProperty("密钥对名称")
    private String keypairName;

    @ApiModelProperty("系统盘id")
    private String systemDisk;

    @ApiModelProperty("规格id")
    private String flavorId;

    @ApiModelProperty("云主机数据盘列表")
    private List<VmGetServerDetailResponseVolumes> volumes;

    @ApiModelProperty("cpu个数")
    private Integer cpu;

    @ApiModelProperty("云主机描述")
    private String description;

    @ApiModelProperty("云主机网卡列表")
    private List<VmGetServerDetailResponsePorts> ports;

    @ApiModelProperty("云主机备份策略")
    private VmGetServerDetailResponseServerBackupPolicyResp serverBackupPolicyResp;

    @ApiModelProperty("az")
    private String availabilityZone;

    @ApiModelProperty("系统盘大小")
    private Integer disk;

    @ApiModelProperty("云主机内网带宽")
    private String maxBandwidth;

    @ApiModelProperty("云主机名称")
    private String name;

    @ApiModelProperty("是否放入回收站")
    private Boolean recycle;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("云主机类型下的主机类型")
    private ServerVmTypeEnum serverVmType;

    @ApiModelProperty("云主机id")
    private String id;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("云主机状态")
    private Integer status;

    @ApiModelProperty("云主机状态")
    private EcStatusEnum ecStatus;

    public GetInstanceDetailResponse parseEcStatus() {
        this.ecStatus = EcStatusEnum.fromCode(this.status);
        return this;
    }

    public String parsePublicIp() {
        if (!CollectionUtils.isEmpty(this.ports)) {
            List<String> publicIps = this.ports.get(0).getPublicIp();
            if (!CollectionUtils.isEmpty(publicIps)) {
                return publicIps.get(0);
            }
        }
        return null;
    }

    public DiskModel parseDataDisk() {
        if (!CollectionUtils.isEmpty(this.volumes)) {
            VmGetServerDetailResponseVolumes data = this.volumes.get(0);
            DiskModel model = new DiskModel();
            model.setDiskType(DiskType.DATA);
            model.setIaasDiskUUID(data.getId());
            return model;
        }
        return null;
    }
}
