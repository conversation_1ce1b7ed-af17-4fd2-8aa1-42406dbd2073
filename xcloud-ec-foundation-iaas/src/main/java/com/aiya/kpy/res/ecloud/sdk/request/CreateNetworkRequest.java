package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateNetworkResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.RequestTags;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateNetworkBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateNetworkRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateNetworkRequestSubnets;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateNetworkRequest extends ECloudAcsRequest<CreateNetworkResponse> {
    @ApiModelProperty("子网可用区")
    @NotNull(message = "子网可用区不能为空")
    private String availabilityZoneHints;

    @ApiModelProperty("网络名称，支持5-64位数字、字母、中划线和下划线组合（必须以字母开头）")
    @NotNull(message = "网络名称不能为空")
    private String networkName;

    @ApiModelProperty("网络的类型, 默认VM, 云主机类型网络不可绑定物理机, 反之亦然")
    @NotNull(message = "网络的类型不能为空")
    private NetworkTypeEnum networkTypeEnum;

    @ApiModelProperty("关联路由器id")
    @NotNull(message = "关联路由器id不能为空")
    private String routerId;

    @ApiModelProperty("子网信息")
    @NotNull(message = "子网信息不能为空")
    private List<VpcCreateNetworkRequestSubnets> subnets;

    @ApiModelProperty("标签结构体")
    private List<RequestTags> tags;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("create")
                .uri("/customer/v3/network")
                .gatewayUri("/api/openapi-vpc/customer/v3/network")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcCreateNetworkRequest toRequest() {
        VpcCreateNetworkBody body = VpcCreateNetworkBody.builder()
                .availabilityZoneHints(this.availabilityZoneHints)
                .routerId(this.routerId)
                .networkName(this.networkName)
                .networkTypeEnum(this.networkTypeEnum)
                .subnets(this.subnets)
                .tags(this.tags)
                .build();
        return VpcCreateNetworkRequest.builder()
                .createBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return CreateNetworkResponse.class;
    }
}
