package com.aiya.kpy.res.kpyun.parser;


import com.aiya.kpy.res.kpyun.exception.ClientException;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;

import java.io.IOException;

/**
 * <AUTHOR> on 2017/4/6.
 */
public class XmlParser implements ResponseParser {
	private static final ThreadLocal<XmlMapper> MAPPER = new ThreadLocal<XmlMapper>() {
		@Override
		protected XmlMapper initialValue() {
			return new XmlMapper();
		}
	};

	@Override
	public <T extends KpyAcsResponse> T parser(String content, Class<T> clazz) throws ClientException {
		XmlMapper xmlMapper = MAPPER.get();
		try {
			return xmlMapper.reader(clazz).readValue(content);
		} catch (IOException e) {
			throw new ClientException(e.getMessage());
		}
	}
}
