package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.ContainerSshkey;

/**
 * 创建容器SSH密钥响应
 * 基于container.swagger.json生成
 */
public class CreateContainerSshkeyResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private ContainerSshkey info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public ContainerSshkey getInfo() {
        return info;
    }

    public void setInfo(ContainerSshkey info) {
        this.info = info;
    }
}
