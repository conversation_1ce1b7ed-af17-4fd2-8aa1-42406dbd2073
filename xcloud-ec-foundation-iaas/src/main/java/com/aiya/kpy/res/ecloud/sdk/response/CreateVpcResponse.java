package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcOrderResponseOrderExtResps;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class CreateVpcResponse implements ECloudResponseBody {

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("订单包含的订单项列表")
    private List<VpcOrderResponseOrderExtResps> orderExtResps;

    @ApiModelProperty("支付地址")
    private String payUrl;
}
