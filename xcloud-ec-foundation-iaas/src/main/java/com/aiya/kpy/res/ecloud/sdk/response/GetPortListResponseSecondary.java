package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetPortListResponseSecondary {
    @SerializedName("vpoolId")
    private String vpoolId;
    @SerializedName("description")
    private String description;
    @SerializedName("scale")
    private String scale;
    @SerializedName("portId")
    private String portId;
    @SerializedName("type")
    private PortTypeEnum type;
    @SerializedName("vaz")
    private String vaz;
    @SerializedName("edge")
    private Boolean edge;
    @SerializedName("countEcs")
    private Integer countEcs;
    @SerializedName("defaulted")
    private Boolean defaulted;
    @SerializedName("name")
    private String name;
    @SerializedName("createdTime")
    private String createdTime;
    @SerializedName("id")
    private String id;
    @SerializedName("region")
    private String region;
    @SerializedName("stateful")
    private Boolean stateful;
    @SerializedName("vPoolId")
    private String vPoolId;
}
