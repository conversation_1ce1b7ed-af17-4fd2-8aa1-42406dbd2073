package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.EipProductTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EipRenewBody extends Body {
    @SerializedName("duration")
    private Integer duration;
    @SerializedName("resourceId")
    private String resourceId;
    @SerializedName("tagId")
    private String tagId;
    @SerializedName("productType")
    private EipProductTypeEnum productType;
}
