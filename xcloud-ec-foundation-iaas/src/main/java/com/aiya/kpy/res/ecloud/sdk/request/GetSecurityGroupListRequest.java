package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetSecurityGroupListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetSecurityGroupListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetSecurityGroupListRequest extends ECloudAcsRequest<GetSecurityGroupListResponse> {
    @ApiModelProperty("按名称搜索安全组")
    private String queryWord;

    @ApiModelProperty("安全组ID（UUID）列表")
    private List<String> securityGroupIds;

    @ApiModelProperty("虚拟网卡ID")
    private String portId;

    @ApiModelProperty("安全组类型")
    private List<ResourceTypeEnum> types;

    @ApiModelProperty("分页")
    private Integer page;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @ApiModelProperty("标签ID列表")
    private List<String> tagIds;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("虚拟可用资源池")
    private String vPoolId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listSecGroup")
                .uri("/customer/v3/SecurityGroup")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroup")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetSecurityGroupListRequest toRequest() {
        VpcGetSecurityGroupListQuery query = VpcGetSecurityGroupListQuery.builder()
                .queryWord(this.queryWord)
                .securityGroupIds(this.securityGroupIds)
                .portId(this.portId)
                .types(this.types)
                .page(this.page)
                .pageSize(this.pageSize)
                .tagIds(this.tagIds)
                .region(this.region)
                .vaz(this.vaz)
                .vPoolId(this.vPoolId)
                .build();
        return VpcGetSecurityGroupListRequest.builder()
                .listSecGroupQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetSecurityGroupListResponse.class;
    }
}
