package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetServerDetailRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetInstanceDetailResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetInstanceDetailRequest extends ECloudAcsRequest<GetInstanceDetailResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @ApiModelProperty("是否查询ports、volumes与云主机备份策略的信息")
    private Boolean detail;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmGetServerDetail")
                .uri("/acl/v3/server/detail/{serverId}")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/detail/{serverId}")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmGetServerDetailRequest toRequest() {
        return VmGetServerDetailRequest.builder()
                .vmGetServerDetailPath(VmGetServerDetailPath.builder().serverId(this.serverId).build())
                .vmGetServerDetailQuery(VmGetServerDetailQuery.builder().detail(this.detail).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetInstanceDetailResponse.class;
    }
}
