package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/22
 */
@Data
public class GetOrderInfoResponseContent implements ECloudResponseBody {
    @ApiModelProperty("资费标识")
    private String measureId;

    @ApiModelProperty("Eboss侧订单编码")
    private String bossOrderId;

    @ApiModelProperty("资源id")
    private String instanceId;

    @ApiModelProperty("订单项id")
    private String tradeId;

    @ApiModelProperty("订单状态")
    private String status;
}
