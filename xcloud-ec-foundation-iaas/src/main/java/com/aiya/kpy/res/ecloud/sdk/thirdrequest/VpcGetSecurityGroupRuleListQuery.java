package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
@Builder
public class VpcGetSecurityGroupRuleListQuery extends Query {
    @SerializedName("securityGroupId")
    private String securityGroupId;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("page")
    private Integer page;
}
