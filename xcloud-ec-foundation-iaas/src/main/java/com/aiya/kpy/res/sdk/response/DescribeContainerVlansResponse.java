package com.aiya.kpy.res.sdk.response;

import com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse;
import com.aiya.kpy.res.sdk.model.ContainerVlan;

import java.util.List;

/**
 * 查询容器VLAN列表响应
 * 容器云平台查询VLAN列表API的响应类。包含分页信息和VLAN详细信息列表，
 * 用于返回查询到的VLAN配置数据。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class DescribeContainerVlansResponse extends KpyRpcAcsResponse {
    private String requestId;
    private String hostId;
    private Integer pageNumber;
    private Integer pageSize;
    private Integer totalCount;
    private List<ContainerVlan> info;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getHostId() {
        return hostId;
    }

    public void setHostId(String hostId) {
        this.hostId = hostId;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<ContainerVlan> getInfo() {
        return info;
    }

    public void setInfo(List<ContainerVlan> info) {
        this.info = info;
    }
}
