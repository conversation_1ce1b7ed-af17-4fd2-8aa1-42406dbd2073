package com.aiya.kpy.res.ecloud.enums;

import com.aiya.kpy.common.enums.BillingCycleEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum ChargePeriodEnum {
    HOUR("hour"),
    MONTH("month"),
    YEAR("year");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }

    public static ChargePeriodEnum valueOf(BillingCycleEnum billingCycleEnum) {
        if (Objects.isNull(billingCycleEnum)) {
            return null;
        }
        return ChargePeriodEnum.valueOf(billingCycleEnum.name());
    }
}
