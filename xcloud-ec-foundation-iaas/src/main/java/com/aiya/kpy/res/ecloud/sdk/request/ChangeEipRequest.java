package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipChangeBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EipChangeRequest;
import com.aiya.kpy.res.ecloud.sdk.response.ChangeEipResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeEipRequest extends ECloudAcsRequest<ChangeEipResponse> {
    @ApiModelProperty("虚拟可用区资源池信息")
    private String vpoolId;

    @ApiModelProperty("系统级别标签，变更时传入该入参则变更的弹性公网IP仅能通过API接口退订")
    private String tagId;

    @ApiModelProperty("变更带宽关联的弹性公网IP地址")
    private String ip;

    @ApiModelProperty("变更带宽大小，单位：Mbps，包年计费仅支持升配不支持降配，当前可支持的带宽值范围为1~500Mbps，如果需要更大带宽，请提交工单")
    private Integer bandwidthSize;

    @ApiModelProperty("变更的弹性公网IP所关联的带宽ID")
    private String bandwidthId;

    @ApiModelProperty("计费方式")
    private ChargeModeEnum chargeModeEnum;

    @ApiModelProperty("虚拟可用区资源池信息")
    private String vPoolId;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("eipOrderChange")
                .uri("/customer/v3/order/change/floatingip")
                .gatewayUri("/api/openapi-eip/customer/v3/order/change/floatingip")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EipChangeRequest toRequest() {
        EipChangeBody body = EipChangeBody.builder()
                .vpoolId(vpoolId)
                .tagId(tagId)
                .ip(ip)
                .bandwidthSize(bandwidthSize)
                .bandwidthId(bandwidthId)
                .chargeModeEnum(chargeModeEnum)
                .vPoolId(vPoolId)
                .vaz(vaz)
                .build();
        return EipChangeRequest.builder().eipOrderChangeBody(body).build();
    }

    @Override
    public Class getResponseClass() {
        return ChangeEipResponse.class;
    }
}
