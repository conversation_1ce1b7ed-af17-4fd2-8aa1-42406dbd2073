package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.DescribeContainerSshkeysResponse;

/**
 * 查询容器SSH密钥列表请求
 * 用于容器云平台查询SSH密钥列表的API请求类。支持分页查询和条件过滤，
 * 包括密钥名称、UUID等筛选条件。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public class DescribeContainerSshkeysRequest extends KpyRpcAcsRequest<DescribeContainerSshkeysResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private Integer pageNumber;
    private Integer pageSize;
    private String uuid;
    private String name;

    public DescribeContainerSshkeysRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "DescribeContainerSshkeys");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "PageNumber", description = "页码")
    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    @KpyParam(value = "PageSize", description = "每页大小")
    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @KpyParam(value = "Uuid", description = "SSH密钥UUID")
    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @KpyParam(value = "Name", description = "SSH密钥名称")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public Class<DescribeContainerSshkeysResponse> getResponseClass() {
        return DescribeContainerSshkeysResponse.class;
    }
}
