/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.enums;


import com.aiya.kpy.res.kpyun.parser.JsonParser;
import com.aiya.kpy.res.kpyun.parser.ResponseParser;
import com.aiya.kpy.res.kpyun.parser.XmlParser;

/**
 * <AUTHOR>
 */
public enum FormatType {
	/**
	 * xml
	 */
	XML(new XmlParser()),
	JSON(new JsonParser()),
	PLAIN(null),;

	private final ResponseParser responseParser;

	FormatType(ResponseParser responseParser) {
		this.responseParser = responseParser;
	}

	public static String mapFormatToAccept(FormatType format) {
		if (FormatType.XML == format) {
			return "application/xml";
		}
		if (FormatType.JSON == format) {
			return "application/json";
		}

		return "application/octet-stream";
	}

	/**
	 * 自家的流氓,只会返回一个text/plain
	 */
	public static FormatType mapAcceptToFormat(String accept) {
		if ("application/xml".equalsIgnoreCase(accept) ||
				"text/xml".equals(accept.toLowerCase())) {
			return FormatType.XML;
		}
		if ("application/json".equals(accept.toLowerCase())) {
			return FormatType.JSON;
		}
		return FormatType.JSON;
	}

	public ResponseParser getResponseParser() {
		return responseParser;
	}

}
