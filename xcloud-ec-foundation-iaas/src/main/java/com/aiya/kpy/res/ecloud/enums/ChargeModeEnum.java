package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum ChargeModeEnum {
    /**
     * 按带宽计费
     */
    BANDWIDTHCHARGE("bandwidthCharge"),
    /**
     * 按流量计费
     */
    TRAFFICCHARGE("trafficCharge");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
