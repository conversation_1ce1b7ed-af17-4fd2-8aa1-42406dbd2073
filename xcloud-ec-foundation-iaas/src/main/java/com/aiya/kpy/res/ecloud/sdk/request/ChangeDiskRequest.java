package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChangeTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.ChangeDiskResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsChangeBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.EbsChangeRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeDiskRequest extends ECloudAcsRequest<ChangeDiskResponse> {
    @ApiModelProperty("变更的原硬盘Id")
    @NotNull(message = "变更的原硬盘Id不能为空")
    private String volumeId;

    @ApiModelProperty("变更类型，规格变更还是时长变更")
    @NotNull(message = "变更类型不能为空")
    private ChangeTypeEnum changeType;

    @ApiModelProperty("包月转包年填写")
    private Integer duration;

    @ApiModelProperty("硬盘大小")
    private Integer size;

    @ApiModelProperty("预付费变更支付成功跳转地址，预付费填写")
    private String returnUrl;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("changeVolume")
                .uri("/acl/v3/volume/change/ebs")
                .gatewayUri("/api/v2/volume/volume/change/ebs")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    @Override
    public EbsChangeRequest toRequest() {
        EbsChangeBody body = EbsChangeBody.builder()
                .volumeId(this.volumeId)
                .changeType(this.changeType)
                .duration(this.duration)
                .size(this.size)
                .returnUrl(this.returnUrl)
                .build();
        return EbsChangeRequest.builder()
                .changeVolumeBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return ChangeDiskResponse.class;
    }
}
