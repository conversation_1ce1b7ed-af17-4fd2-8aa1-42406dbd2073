package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
@Builder
public class EbsGetListQuery extends Query {
    @SerializedName("likeSearch")
    private String likeSearch;
    @SerializedName("size")
    private Integer size;
    @SerializedName("tagIds")
    private List<String> tagIds;
    @SerializedName("volumeId")
    private String volumeId;
    @SerializedName("page")
    private Integer page;
}
