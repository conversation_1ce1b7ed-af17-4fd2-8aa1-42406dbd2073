package com.aiya.kpy.res.sdk.model;

import java.util.Date;

/**
 * ContainerNic 容器网卡模型
 *
 * 用于容器云平台的容器网络接口配置模型，包含网卡的IP地址、
 * MAC地址、网关、DNS等网络配置信息。支持容器的网络管理
 * 和多网卡配置。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class ContainerNic {
    private Integer id;
    private String uuid;
    private String name;
    private String description;
    private String ip;
    private String mac;
    private String gateway;
    private String dns;
    private String deviceNo;
    private String dockerUuid;
    private String vlanUuid;
    private Integer vni;
    private String zoneUuid;
    private Integer regionId;
    private Integer userId;
    private Integer state;
    private Boolean isInternal;
    private Date createdAt;
    private Date updatedAt;
    private String creator;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getMac() {
        return mac;
    }

    public void setMac(String mac) {
        this.mac = mac;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getDns() {
        return dns;
    }

    public void setDns(String dns) {
        this.dns = dns;
    }

    public String getDeviceNo() {
        return deviceNo;
    }

    public void setDeviceNo(String deviceNo) {
        this.deviceNo = deviceNo;
    }

    public String getDockerUuid() {
        return dockerUuid;
    }

    public void setDockerUuid(String dockerUuid) {
        this.dockerUuid = dockerUuid;
    }

    public String getVlanUuid() {
        return vlanUuid;
    }

    public void setVlanUuid(String vlanUuid) {
        this.vlanUuid = vlanUuid;
    }

    public Integer getVni() {
        return vni;
    }

    public void setVni(Integer vni) {
        this.vni = vni;
    }

    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public Boolean getIsInternal() {
        return isInternal;
    }

    public void setIsInternal(Boolean isInternal) {
        this.isInternal = isInternal;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }
}
