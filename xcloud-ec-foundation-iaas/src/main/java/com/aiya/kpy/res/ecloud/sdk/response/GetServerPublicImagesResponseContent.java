package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.ImsStatusEnum;
import com.aiya.kpy.res.ecloud.enums.ServerTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
public class GetServerPublicImagesResponseContent {
    @ApiModelProperty("镜像ID")
    private String imageId;

    @ApiModelProperty("主机类型")
    private ServerTypeEnum serverType;

    @ApiModelProperty("镜像名称")
    private String name;

    @ApiModelProperty("操作系统类型")
    private String osType;

    @ApiModelProperty("操作系统名称")
    private String osName;

    @ApiModelProperty("系统盘最小值")
    private String minDisk;

    @ApiModelProperty("镜像状态")
    private ImsStatusEnum status;
}
