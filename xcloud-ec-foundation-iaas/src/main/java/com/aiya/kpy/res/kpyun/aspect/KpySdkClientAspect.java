package com.aiya.kpy.res.kpyun.aspect;

import com.aiya.kpy.res.kpyun.request.KpyAcsRequest;
import io.opentelemetry.api.GlobalOpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.context.Context;
import io.opentelemetry.context.propagation.TextMapPropagator;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2025/1/21
 */
@Slf4j
@Aspect
@Component
public class KpySdkClientAspect {
    private final TextMapPropagator propagator = GlobalOpenTelemetry.getPropagators().getTextMapPropagator();
    private static final String CUSTOM_CONTEXT = "customContext";

    @Before("execution(* com.aiya.kpy.res.kpyun.client.DefaultKpyunSdkClient.execute(..))")
    public void beforeExcute(JoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        Context context = Context.current();
        Span span = Span.current();
        for (Object arg : args) {
            if (arg instanceof KpyAcsRequest) {
                HashMap<String, String> headers = new HashMap<>(((KpyAcsRequest<?>) arg).getHeaders());
                propagator.inject(context, headers, (carrier, key, value) -> carrier.put(key, value));
                ((KpyAcsRequest<?>) arg).putHeaderParameter("traceparent", headers.get("traceparent"));
            }
        }
    }
}
