package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmgetOrderInfoQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmgetOrderInfoRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetOrderInfoResponseContent;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/22
 */
@Data
@Builder
public class GetOrderInfoRequest extends ECloudAcsRequest<GetOrderInfoResponseContent> {
    @ApiModelProperty("订单Id")
    @NotNull(message = "订单Id不能为空")
    private String orderId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmgetOrderInfoByOrderId")
                .uri("/acl/v3/server/order/relation/info")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/order/relation/info")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmgetOrderInfoRequest toRequest() {
        VmgetOrderInfoQuery query = VmgetOrderInfoQuery.builder().orderId(this.orderId).build();
        return VmgetOrderInfoRequest.builder()
                .vmgetOrderInfoByOrderIdQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetOrderInfoResponseContent.class;
    }
}
