package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
public class GetSecurityGroupListResponseContent {
    @ApiModelProperty("虚拟可用区资源池信息")
    private String vpoolId;

    @ApiModelProperty("安全组描述")
    private String description;

    @ApiModelProperty("安全组规格（null：普通安全组)")
    private String scale;

    @ApiModelProperty("虚拟网卡ID")
    private String portId;

    @ApiModelProperty("安全组类型")
    private ResourceTypeEnum type;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("云主机数目")
    private Integer countEcs;

    @ApiModelProperty("是否是默认安全组")
    private Boolean defaulted;

    @ApiModelProperty("安全组名称")
    private String name;

    @ApiModelProperty("安全组创建时间")
    private String createdTime;

    @ApiModelProperty("安全组ID")
    private String id;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("安全组是否为有状态")
    private Boolean stateful;

    @ApiModelProperty("虚拟可用区资源池信息")
    private String vPoolId;
}
