package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum VmTypeEnum {
    /**
     * 内存优化型
     */
    MEMIMPROVE("memImprove"),
    /**
     * 通用型
     */
    COMMON("common"),
    /**
     * GPU型
     */
    GPU("gpu"),
    /**
     * 通用入门型
     */
    COMMONINTRODUCTORY("commonIntroductory"),
    /**
     * 通用网络优化型
     */
    COMMONNETIMPROVE("commonNetImprove"),
    /**
     * 计算型
     */
    COMPUTE("compute"),
    /**
     * 计算网络优化型
     */
    COMPUTENETIMPROVE("computeNetImprove"),
    /**
     * 内存网络优化型
     */
    MEMNETIMPROVE("memNetImprove"),
    /**
     * 大数据型
     */
    LOCALSTORAGE("localStorage"),
    /**
     * 超大内存型
     */
    XLARGEMEMORY("xlargeMemory"),
    /**
     * 超高主频型
     */
    HIGHFREQUENCY("highFrequency"),
    /**
     * vGPU型
     */
    VGPU("vgpu"),
    /**
     * FPGA型
     */
    FPGA("fpga"),
    /**
     * 高IO型
     */
    HIGHIO("highIO"),
    /**
     * 独享型
     */
    EXCLUSIVE("exclusive"),
    /**
     * 通用计算增强型
     */
    NORMALCOMPUTEIMPROVE("normalComputeImprove"),
    /**
     * 通用网络增强型
     */
    NORMALNETENHANCE("normalNetEnhance"),
    /**
     * 存储增强型
     */
    STOREENHANCE("storeEnhance"),
    /**
     * 计算增强型
     */
    COMPUTEENHANCE("computeEnhance"),
    /**
     * NPU型
     */
    NPU("npu");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
