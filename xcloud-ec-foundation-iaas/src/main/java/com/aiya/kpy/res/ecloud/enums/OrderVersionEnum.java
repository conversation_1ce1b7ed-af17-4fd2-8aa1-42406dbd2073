package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum OrderVersionEnum {
    /**
     * 旧版本订单（2020年11月26日之前）
     */
    ORIGIN("origin"),
    /**
     * 新版本订单（带宽限速放大）
     */
    NEWORDER("newOrder");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
