package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmUpdateKeypairBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmUpdateKeypairRequest;
import com.aiya.kpy.res.ecloud.sdk.response.UpdateServerKeypairResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateServerKeypairRequest extends ECloudAcsRequest<UpdateServerKeypairResponse> {
    @ApiModelProperty("密钥对名称")
    @NotNull(message = "密钥对名称不能为空")
    private String keyName;

    @ApiModelProperty("云主机id")
    @NotNull(message = "云主机id不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmUpdateKeypair")
                .uri("/acl/v3/server/keypair")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/keypair")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmUpdateKeypairRequest toRequest() {
        return VmUpdateKeypairRequest.builder()
                .vmUpdateKeypairBody(VmUpdateKeypairBody.builder()
                        .keyName(this.keyName)
                        .serverId(this.serverId)
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return UpdateServerKeypairResponse.class;
    }
}
