package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum DiskPerformanceEnum {
    LOCAL("local"),
    CAPACITY("capacity"),
    /**
     * 高性能型
     */
    HIGHPERFORMANCE("highPerformance"),
    /**
     * 性能优化型
     */
    PERFORMANCEOPTIMIZATION("performanceOptimization"),
    /**
     * 高性能型-云创
     */
    HIGHPERFORMANCEYC("highPerformanceyc"),
    /**
     * 性能优化型-云创
     */
    PERFORMANCEOPTIMIZATIONYC("performanceOptimizationyc");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
