package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmGetRegionResponseBodyContent {
    @SerializedName("id")
    private Integer id;
    @SerializedName("region")
    private String region;
    @SerializedName("name")
    private String name;
    @SerializedName("component")
    private String component;
    @SerializedName("poolid")
    private String poolid;
    @SerializedName("deleted")
    private Boolean deleted;
    @SerializedName("visible")
    private Boolean visible;
}
