package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Getter
@AllArgsConstructor
public enum StateEnum {
    OK("OK"),
    ERROR("ERROR"),
    EXCEPTION("EXCEPTION"),
    ALARM("ALARM"),
    FORBIDDEN("FORBIDDEN");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
