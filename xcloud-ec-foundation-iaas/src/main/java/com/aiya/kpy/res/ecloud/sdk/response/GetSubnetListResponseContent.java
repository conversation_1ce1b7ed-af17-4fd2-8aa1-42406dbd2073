package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
public class GetSubnetListResponseContent {
    @ApiModelProperty("虚拟可用区资源池信息")
    private String vpoolId;

    @ApiModelProperty("子网网关地址")
    private String gatewayIp;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("子网是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("子网是否删除")
    private Boolean deleted;

    @ApiModelProperty("子网IP协议类型：4-IPv4；6-IPv6")
    private VersionsEnum ipVersion;

    @ApiModelProperty("子网名称")
    private String name;

    @ApiModelProperty("子网创建时间")
    private String createdTime;

    @ApiModelProperty("子网网段")
    private String cidr;

    @ApiModelProperty("子网所属的网络ID")
    private String networkId;

    @ApiModelProperty("子网ID")
    private String id;

    @ApiModelProperty("子网所属的网络类型：VM-云主机子网；IRONIC-裸金属子网；INTER_CONNECTION-互联子网； EBM-云主机（同VM合并）；VMWARE-专属云；DBAUDIT-数据库审计")
    private String networkType;

    @ApiModelProperty("子网可用区")
    private String region;
}
