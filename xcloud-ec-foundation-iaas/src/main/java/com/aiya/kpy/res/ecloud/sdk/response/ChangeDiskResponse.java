package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.ProcedureCodeEnum;
import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
public class ChangeDiskResponse implements ECloudResponseBody {
    @ApiModelProperty("支付金额")
    private String payMoney;

    @ApiModelProperty("付费类型")
    private ProcedureCodeEnum procedureCode;

    @ApiModelProperty("订单号")
    private String orderId;

    @ApiModelProperty("退费金额")
    private String fee;

    @ApiModelProperty("错误信息")
    private String errorMessage;

    @ApiModelProperty("预付费链接")
    private String returnUrl;

    @ApiModelProperty("实付金额")
    private String paidFee;

    @ApiModelProperty("已使用金额")
    private String usedFee;

    @ApiModelProperty("订单项集合")
    private List<ChangeDiskResponseProductList> productList;

    @ApiModelProperty("预付费支付信息")
    private Map<String, Object> paymentInfo;
}
