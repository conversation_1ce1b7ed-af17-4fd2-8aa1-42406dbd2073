package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.ChangeTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.RenewInstanceResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmRenewBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmRenewRequest;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewInstanceRequest extends ECloudAcsRequest<RenewInstanceResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @ApiModelProperty("续订时长(月/年)")
    private Integer duration;

    @ApiModelProperty("续订变更要转的计费方式，填入内容：TURN_MONTH：到期转包月；TURN_AMOUNT：到期转按量")
    private ChangeTypeEnum billingType;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmRenew")
                .uri("/acl/v3/server/renew")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/renew")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmRenewRequest toRequest() {
        return VmRenewRequest.builder()
                .vmRenewBody(VmRenewBody.builder()
                        .serverId(this.serverId)
                        .duration(this.duration)
                        .billingType(this.billingType == null ? null : this.billingType.getValue())
                        .build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return RenewInstanceResponse.class;
    }
}
