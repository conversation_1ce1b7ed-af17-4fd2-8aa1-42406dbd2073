package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcPortDetachServerPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcPortDetachServerRequest;
import com.aiya.kpy.res.ecloud.sdk.response.PortDetachServerResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PortDetachServerRequest extends ECloudAcsRequest<PortDetachServerResponse> {
    private String portId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("portDetachServer")
                .uri("/acl/v3/port/{portId}/detachServer")
                .gatewayUri("/api/openapi-vpc/acl/v3/port/{portId}/detachServer")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcPortDetachServerRequest toRequest() {
        VpcPortDetachServerPath path = VpcPortDetachServerPath.builder()
                .portId(this.portId)
                .build();
        return VpcPortDetachServerRequest.builder()
                .portDetachServerPath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return PortDetachServerResponse.class;
    }
}
