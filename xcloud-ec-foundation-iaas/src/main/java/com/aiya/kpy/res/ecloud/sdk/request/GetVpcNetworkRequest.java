package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.NetworkTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetNetworkListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetNetworkListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetVpcNetworkResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetVpcNetworkRequest extends ECloudAcsRequest<GetVpcNetworkResponse> {
    @ApiModelProperty("子网类型,V4/V6")
    private List<VersionsEnum> ipVersions;

    @ApiModelProperty("网络id列表")
    private List<String> rangeInNetworkIds;

    @ApiModelProperty("标签id列表")
    private List<String> tagIds;

    @ApiModelProperty("网络的类型列表, 默认VM和IRONIC")
    private List<NetworkTypeEnum> networkTypeEnum;

    @ApiModelProperty("网络所属的路由器id")
    private String routerId;

    @ApiModelProperty("模糊查询网络名称")
    private String queryWord;

    @ApiModelProperty("是否为共享网络")
    private Boolean shared;

    @ApiModelProperty("分页页数")
    private Integer page;

    @ApiModelProperty("每页大小")
    private Integer pageSize;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("是否可见, 默认只展示可见资源")
    private Boolean visible;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listNetwork")
                .uri("/customer/v3/network/NetworkResps")
                .gatewayUri("/api/openapi-vpc/customer/v3/network/NetworkResps")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetNetworkListRequest toRequest() {
        VpcGetNetworkListQuery query = VpcGetNetworkListQuery.builder()
                .ipVersions(this.ipVersions)
                .rangeInNetworkIds(this.rangeInNetworkIds)
                .tagIds(this.tagIds)
                .networkTypeEnum(this.networkTypeEnum)
                .routerId(this.routerId)
                .queryWord(this.queryWord)
                .shared(this.shared)
                .page(this.page)
                .pageSize(this.pageSize)
                .region(this.region)
                .visible(this.visible)
                .build();
        return VpcGetNetworkListRequest.builder()
                .listNetworkQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetVpcNetworkResponse.class;
    }
}
