package com.aiya.kpy.res.kpyun.entity;


import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 第三方接口调用日志实体类
 * <AUTHOR>
 */
@Document(collection = "RES_THIRD_PARTY_API_REQ_LOG")
@Data
public class ResThirdPartyApiReqLogEntity {
	/**
	 * 主键id
	 */
	@Id
	private String id;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 可用区编号
	 */
	private String zoneCode;

	/**
	 * 请求内容
	 */
	private Object requestJson;
	/**
	 * 应答内容
	 */
	private Object responseJson;
}
