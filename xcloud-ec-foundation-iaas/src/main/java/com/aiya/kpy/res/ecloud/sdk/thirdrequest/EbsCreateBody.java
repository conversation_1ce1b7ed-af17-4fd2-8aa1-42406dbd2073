package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChargePeriodEnum;
import com.aiya.kpy.res.ecloud.enums.DiskTagIdEnum;
import com.aiya.kpy.res.ecloud.enums.ProductTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
@Builder
public class EbsCreateBody extends Body {
    @SerializedName("periodNum")
    private Integer periodNum;
    @SerializedName("backupPolicyId")
    private String backupPolicyId;
    @SerializedName("tagId")
    private DiskTagIdEnum tagId;
    @SerializedName("backupId")
    private String backupId;
    @SerializedName("description")
    private String description;
    @SerializedName("keyId")
    private String keyId;
    @SerializedName("serverId")
    private String serverId;
    @SerializedName("autoEndTime")
    private String autoEndTime;
    @SerializedName("cinderType")
    private String cinderType;
    @SerializedName("autoRenew")
    private Boolean autoRenew;
    @SerializedName("share")
    private Boolean share;
    @SerializedName("returnUrl")
    private String returnUrl;
    @SerializedName("productType")
    private ProductTypeEnum productType;
    @SerializedName("quantity")
    private Integer quantity;
    @SerializedName("snapshotId")
    private String snapshotId;
    @SerializedName("vaz")
    private String vaz;
    @SerializedName("tags")
    private List<RequestTags> tags;
    @SerializedName("serialNo")
    private String serialNo;
    @SerializedName("channelInfoList")
    private List<EbsCreateRequestChannelInfoList> channelInfoList;
    @SerializedName("periodType")
    private ChargePeriodEnum periodType;
    @SerializedName("size")
    private Integer size;
    @SerializedName("name")
    private String name;
    @SerializedName("limitMoney")
    private String limitMoney;
    @SerializedName("region")
    private String region;
    @SerializedName("encryptedParam")
    private String encryptedParam;
    @SerializedName("snapshotPolicyId")
    private String snapshotPolicyId;
}
