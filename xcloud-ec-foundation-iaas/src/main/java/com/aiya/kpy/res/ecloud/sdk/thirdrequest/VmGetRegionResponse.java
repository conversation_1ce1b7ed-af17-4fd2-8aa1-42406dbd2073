package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.StateEnum;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VmGetRegionResponse {
    @SerializedName("requestId")
    private String requestId;
    @SerializedName("errorMessage")
    private String errorMessage;
    @SerializedName("errorCode")
    private String errorCode;
    @SerializedName("state")
    private StateEnum state;
    @SerializedName("body")
    private List<VmGetRegionResponseBodyContent> body;
    @SerializedName("errorParams")
    private List<String> errorParams;
}
