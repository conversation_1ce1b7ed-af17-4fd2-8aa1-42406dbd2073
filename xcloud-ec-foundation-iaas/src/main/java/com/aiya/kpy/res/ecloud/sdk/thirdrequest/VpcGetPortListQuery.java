package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.BindStatusEnum;
import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.ecloud.sdk.position.Query;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
public class VpcGetPortListQuery extends Query {
    @SerializedName("subnetId")
    private String subnetId;
    @SerializedName("havipId")
    private String havipId;
    @SerializedName("resourceId")
    private String resourceId;
    @SerializedName("operationStatus")
    private BindStatusEnum operationStatus;
    @SerializedName("serverName")
    private String serverName;
    @SerializedName("pageSize")
    private Integer pageSize;
    @SerializedName("portRangeInIds")
    private List<String> portRangeInIds;
    @SerializedName("portName")
    private String portName;
    @SerializedName("securityGroupId")
    private String securityGroupId;
    @SerializedName("havipBind")
    private Boolean havipBind;
    @SerializedName("routerId")
    private String routerId;
    @SerializedName("fipBind")
    private Boolean fipBind;
    @SerializedName("networkId")
    private String networkId;
    @SerializedName("isBindable")
    private Boolean isBindable;
    @SerializedName("types")
    private List<PortTypeEnum> types;
    @SerializedName("extranet")
    private Boolean extranet;
    @SerializedName("resourceBind")
    private Boolean resourceBind;
    @SerializedName("queryWordForLbMember")
    private String queryWordForLbMember;
    @SerializedName("macAddress")
    private String macAddress;
    @SerializedName("rangeInServerIds")
    private List<String> rangeInServerIds;
    @SerializedName("poolId")
    private String poolId;
    @SerializedName("ipVersions")
    private List<VersionsEnum> ipVersions;
    @SerializedName("page")
    private Integer page;
    @SerializedName("queryWord")
    private String queryWord;
    @SerializedName("region")
    private String region;
}
