package com.aiya.kpy.res.sdk.request;

import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.kpyun.enums.Version;
import com.aiya.kpy.res.kpyun.parser.KpyParam;
import com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerInstanceResponse;

/**
 * 创建容器实例请求
 *
 * 用于容器云平台创建容器实例的API请求类。支持配置容器的
 * 规格、镜像路径、网络配置、存储卷、SSH密钥等完整的容器
 * 创建参数。
 *
 * <AUTHOR>
 * @since 2024-07-24
 */
public class CreateContainerInstanceRequest extends KpyRpcAcsRequest<CreateContainerInstanceResponse> {
    private String regionId;
    private String signature;
    private String accessKeyId;
    private String clientToken;
    private String containerNetType;
    private String description;
    private String flavorUuid;
    private Integer gpuNum;
    private String hostSn;
    private String imagePath;
    private String keyUuid;
    private String nics;
    private String pswd;
    private String runArgs;
    private String vols;
    private String zoneUuid;

    public CreateContainerInstanceRequest() {
        // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER.name
        super("Container", Version.SDK_VERSION_HEADER_VALUE, "CreateContainerInstance");
    }

    @KpyParam(value = "RegionId", description = "地区ID", required = true)
    public String getRegionId() {
        return regionId;
    }

    public void setRegionId(String regionId) {
        this.regionId = regionId;
    }

    @KpyParam(value = "Signature", description = "签名", required = true)
    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    @KpyParam(value = "AccessKeyId", description = "访问服务所在用的密钥ID", required = true)
    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    @KpyParam(value = "ClientToken", description = "用于保证请求的幂等性，由客户端生成该参数值，要保证在不同请求间唯一")
    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @KpyParam(value = "ContainerNetType", description = "容器网络类型")
    public String getContainerNetType() {
        return containerNetType;
    }

    public void setContainerNetType(String containerNetType) {
        this.containerNetType = containerNetType;
    }

    @KpyParam(value = "Description", description = "描述")
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @KpyParam(value = "FlavorUuid", description = "规格UUID")
    public String getFlavorUuid() {
        return flavorUuid;
    }

    public void setFlavorUuid(String flavorUuid) {
        this.flavorUuid = flavorUuid;
    }

    @KpyParam(value = "GpuNum", description = "gpu数量")
    public Integer getGpuNum() {
        return gpuNum;
    }

    public void setGpuNum(Integer gpuNum) {
        this.gpuNum = gpuNum;
    }

    @KpyParam(value = "HostSn", description = "宿主机SN")
    public String getHostSn() {
        return hostSn;
    }

    public void setHostSn(String hostSn) {
        this.hostSn = hostSn;
    }

    @KpyParam(value = "ImagePath", description = "镜像完整路径")
    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    @KpyParam(value = "KeyUuid", description = "key uuid")
    public String getKeyUuid() {
        return keyUuid;
    }

    public void setKeyUuid(String keyUuid) {
        this.keyUuid = keyUuid;
    }

    @KpyParam(value = "Nics", description = "填写json,格式为[{\"Gateway\": \"xxx\", \"Ip\": \"xxx\", \"NicUuid\": \"xxx\", \"VlanUuid\": \"xxx\"}]")
    public String getNics() {
        return nics;
    }

    public void setNics(String nics) {
        this.nics = nics;
    }

    @KpyParam(value = "Pswd", description = "云主机密码")
    public String getPswd() {
        return pswd;
    }

    public void setPswd(String pswd) {
        this.pswd = pswd;
    }

    @KpyParam(value = "RunArgs", description = "运行参数 json，格式为[\"\",\"\"]")
    public String getRunArgs() {
        return runArgs;
    }

    public void setRunArgs(String runArgs) {
        this.runArgs = runArgs;
    }

    @KpyParam(value = "Vols", description = "填写json,格式为[{\"DstPath\": \"xxx\", \"Size\": 0, \"VolType\": 0, \"VolUuid\":\"xxx\"}]")
    public String getVols() {
        return vols;
    }

    public void setVols(String vols) {
        this.vols = vols;
    }

    @KpyParam(value = "ZoneUuid", description = "计算集群(可用区)ID")
    public String getZoneUuid() {
        return zoneUuid;
    }

    public void setZoneUuid(String zoneUuid) {
        this.zoneUuid = zoneUuid;
    }

    @Override
    public Class<CreateContainerInstanceResponse> getResponseClass() {
        return CreateContainerInstanceResponse.class;
    }
}
