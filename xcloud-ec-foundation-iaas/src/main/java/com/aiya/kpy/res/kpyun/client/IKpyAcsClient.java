/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.auth.Credential;
import com.aiya.kpy.res.kpyun.auth.ISigner;
import com.aiya.kpy.res.kpyun.enums.FormatType;
import com.aiya.kpy.res.kpyun.exception.ClientException;
import com.aiya.kpy.res.kpyun.exception.ServerException;
import com.aiya.kpy.res.kpyun.profile.Endpoint;
import com.aiya.kpy.res.kpyun.profile.IClientProfile;
import com.aiya.kpy.res.kpyun.request.KpyAcsRequest;
import com.aiya.kpy.res.kpyun.response.HttpResponse;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IKpyAcsClient {

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request)
			throws ClientException, ServerException;

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request,
													 boolean autoRetry, int maxRetryCounts) throws ClientException, ServerException;

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, IClientProfile profile)
			throws ClientException, ServerException;

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, String regionId,
													 Credential credential) throws ClientException, ServerException;

	<T extends KpyAcsResponse> T getAcsResponse(KpyAcsRequest<T> request)
			throws ServerException, ClientException;

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request, boolean autoRetry,
													 int maxRetryCounts, IClientProfile profile) throws ClientException, ServerException;

	<T extends KpyAcsResponse> HttpResponse doAction(KpyAcsRequest<T> request,
													 boolean autoRetry, int maxRetryNumber,
													 String regionId, Credential credential,
													 ISigner signer, FormatType format,
													 List<Endpoint> endpoints) throws ClientException, ServerException;
}