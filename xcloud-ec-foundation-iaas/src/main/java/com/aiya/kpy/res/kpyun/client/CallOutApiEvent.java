package com.aiya.kpy.res.kpyun.client;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 *  <AUTHOR>
 */
@Setter
@Getter
public class CallOutApiEvent extends ApplicationEvent {
	private static final long serialVersionUID = 4182673349331248853L;

	private final long startTime;
	private final long endTime;
	private final String zoneCode;
	private final String accessKey;
	private final Object request;
	private final Object response;

	public CallOutApiEvent(Object source, long startTime, long endTime, String zoneCode, String accessKey, Object request, Object response) {
		super(source);
		this.startTime = startTime;
		this.endTime = endTime;
		this.zoneCode = zoneCode;
		this.request = request;
		this.response = response;
		this.accessKey = accessKey;
	}
}
