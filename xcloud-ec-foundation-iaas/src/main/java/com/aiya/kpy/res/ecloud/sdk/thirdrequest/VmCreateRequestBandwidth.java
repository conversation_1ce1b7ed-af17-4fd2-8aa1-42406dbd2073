package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ChargeModeEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Data
public class VmCreateRequestBandwidth {
    @SerializedName("chargeMode")
    private ChargeModeEnum chargeMode;
    @SerializedName("bandwidthSize")
    private Integer bandwidthSize;
    @SerializedName("ipv4Bandwidth")
    private Boolean ipv4Bandwidth;
    @SerializedName("ipv6Bandwidth")
    private Boolean ipv6Bandwidth;
}
