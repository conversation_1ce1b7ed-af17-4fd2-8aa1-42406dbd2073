package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.dao.ResThirdPartyApiReqLogDao;
import com.aiya.kpy.res.kpyun.entity.ResThirdPartyApiReqLogEntity;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Service
@Slf4j
public class CallOutApiEventListener {
	@Autowired
	private ResThirdPartyApiReqLogDao resThirdPartyApiReqLogDao;

	@Async("mySimpleAsync")
	@EventListener
	public void onApplicationEvent(CallOutApiEvent event) {
		final Date endTime = new Date(event.getEndTime());
		final Date startTime = new Date(event.getStartTime());
		final String zoneCode = event.getZoneCode();

		ResThirdPartyApiReqLogEntity entity = new ResThirdPartyApiReqLogEntity();
		entity.setCreateTime(new Date());
		entity.setEndTime(endTime);
		entity.setStartTime(startTime);
		entity.setRequestJson(event.getRequest());
		entity.setResponseJson(event.getResponse());
		entity.setZoneCode(zoneCode);
		resThirdPartyApiReqLogDao.insert(entity);
	}
}
