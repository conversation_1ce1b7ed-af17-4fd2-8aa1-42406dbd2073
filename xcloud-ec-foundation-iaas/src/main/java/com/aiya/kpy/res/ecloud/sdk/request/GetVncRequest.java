package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetVncPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmGetVncRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetVncResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetVncRequest extends ECloudAcsRequest<GetVncResponse> {
    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmGetVnc")
                .uri("/acl/v3/server/{serverId}/vnc")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/{serverId}/vnc")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmGetVncRequest toRequest() {
        return VmGetVncRequest.builder()
                .vmGetVncPath(VmGetVncPath.builder().serverId(this.serverId).build())
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetVncResponse.class;
    }
}
