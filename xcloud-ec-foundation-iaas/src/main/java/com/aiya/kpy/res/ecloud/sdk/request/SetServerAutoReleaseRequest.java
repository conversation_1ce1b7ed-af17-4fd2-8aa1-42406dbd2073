package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmSetServerAutoReleaseBody;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmSetServerAutoReleaseRequest;
import com.aiya.kpy.res.ecloud.sdk.response.SetServerAutoReleaseResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SetServerAutoReleaseRequest extends ECloudAcsRequest<SetServerAutoReleaseResponse> {
    /**
     * 仅按小时付费云主机支持设置/取消自动释放
     * 设置自动释放时间，使用GMT+8的时区时间，格式为yyyy-MM-dd HH:mm:ss；
     * 最短释放时间为当前时间1小时之后；
     * 如果不传入参数autoReleaseTime，表示取消自动释放功能，云主机资源不再自动释放
     */
    @ApiModelProperty("设置自动释放时间")
    private String autoReleaseTime;

    @ApiModelProperty("云主机Id")
    @NotNull(message = "云主机Id不能为空")
    private String serverId;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmSetServerAutoRelease")
                .uri("/acl/v3/server/autoRelease")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/autoRelease")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.PUT)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VmSetServerAutoReleaseRequest toRequest() {
        VmSetServerAutoReleaseBody body = VmSetServerAutoReleaseBody.builder()
                .serverId(this.serverId)
                .autoReleaseTime(this.autoReleaseTime)
                .build();
        return VmSetServerAutoReleaseRequest.builder()
                .vmSetServerAutoReleaseBody(body)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return SetServerAutoReleaseResponse.class;
    }
}
