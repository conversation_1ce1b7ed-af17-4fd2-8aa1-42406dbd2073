package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.EcStatusEnum;
import com.aiya.kpy.res.ecloud.enums.OrderTypeEnum;
import com.aiya.kpy.res.ecloud.enums.ScaleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@Data
public class GetVpcListResponseContent {
    @ApiModelProperty("订单来源")
    private OrderTypeEnum orderType;

    @ApiModelProperty("网络底层状态")
    private EcStatusEnum ecStatus;

    @ApiModelProperty("虚拟可用区资源池信息")
    private String vpoolId;

    @ApiModelProperty("VPC描述")
    private String description;

    @ApiModelProperty("VPC规格")
    private ScaleEnum scale;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("用户ID")
    private String userId;

    @ApiModelProperty("虚拟可用区")
    private String vaz;

    @ApiModelProperty("专属设备")
    private String special;

    @ApiModelProperty("是否为边缘云")
    private Boolean edge;

    @ApiModelProperty("是否删除")
    private Boolean deleted;

    @ApiModelProperty("路由ID")
    private String routerId;

    @ApiModelProperty("VPC名称")
    private String name;

    @ApiModelProperty("VPC创建时间")
    private String createdTime;

    @ApiModelProperty("VPC额外规格")
    private String vpcExtraSpecification;

    @ApiModelProperty("VPC ID")
    private String id;

    @ApiModelProperty("路由器Admin状态")
    private Boolean adminStateUp;

    @ApiModelProperty("子网可用区")
    private String region;
}
