package com.aiya.kpy.res.ecloud.client;

import com.aiya.kpy.res.ecloud.enums.ECloudProductEnum;
import com.aiya.kpy.res.kpyun.dao.ResZoneProviderAccountDao;
import com.aiya.kpy.res.kpyun.entity.ResZoneProviderAccountEntity;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Component
public class ECloudSdkClientCache implements ApplicationContextAware {
    @Resource
    private ResZoneProviderAccountDao resZoneProviderAccountDAO;

    private static final ConcurrentMap<ClientKey, ECloudSdkClient> CLIENT_CACHE_MAP = new ConcurrentHashMap<>();
    private ApplicationContext applicationContext;

    public ECloudSdkClient getCacheECloudSdkClient(String platformRegionCode, ECloudProductEnum product) {
        Assert.notNull(platformRegionCode, "platformRegionCode must not be null");
        ResZoneProviderAccountEntity account = this.getAccountByKpyRegionCode(platformRegionCode);
        ClientKey key = new ClientKey(account.getAccessKeyId(), account.getAccessKeySecret(), account.getRegionCode(), product, account.getReqUrl());
        if (!CLIENT_CACHE_MAP.containsKey(key)) {
            ECloudSdkClient client = applicationContext.getBean(ECloudSdkClient.class, key);
            CLIENT_CACHE_MAP.put(key, client);
        }
        return CLIENT_CACHE_MAP.get(key);
    }

    public ECloudSdkClient getCacheECloudSdkClient(String platformRegionCode, String platformZoneCode, ECloudProductEnum product) {
        Assert.notNull(platformRegionCode, "platformRegionCode must not be null");
        Assert.notNull(platformZoneCode, "platformZoneCode must not be null");
        ResZoneProviderAccountEntity account = this.getAccountByKpyRegionCode(platformRegionCode, platformZoneCode);
        ClientKey key = new ClientKey(account.getAccessKeyId(), account.getAccessKeySecret(), account.getRegionCode(), account.getZone(), product, account.getReqUrl());
        if (!CLIENT_CACHE_MAP.containsKey(key)) {
            ECloudSdkClient client = applicationContext.getBean(ECloudSdkClient.class, key);
            CLIENT_CACHE_MAP.put(key, client);
        }
        return CLIENT_CACHE_MAP.get(key);
    }

    private ResZoneProviderAccountEntity getAccountByKpyRegionCode(String platformRegionCode) {
        ResZoneProviderAccountEntity account = resZoneProviderAccountDAO.selectFirstByKpyRegionCode(platformRegionCode);
        Assert.notNull(account, "account not exist");
        return account;
    }

    private ResZoneProviderAccountEntity getAccountByKpyRegionCode(String platformRegionCode, String platformZoneCode) {
        ResZoneProviderAccountEntity account = resZoneProviderAccountDAO.selectZoneProviderAccount(platformRegionCode, platformZoneCode);
        Assert.notNull(account, "account not exist");
        return account;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
