package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.enums.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
public class GetDiskListResponseContent {
    @ApiModelProperty("回收时间")
    private String recycleTime;

    @ApiModelProperty("硬盘cinder类型")
    private String volumeType;

    @ApiModelProperty("硬盘所在集群的ID")
    private String metadata;

    @ApiModelProperty("当前操作状态")
    private OperationFlagEnum operationFlag;

    @ApiModelProperty("以快照为源创建卷时的快照ID")
    private String backupId;

    @ApiModelProperty("硬盘描述")
    private String description;

    @ApiModelProperty("密钥id")
    private String keyId;

    @ApiModelProperty("硬盘op侧类型")
    private DiskTypeEnum type;

    @ApiModelProperty("az")
    private String availabilityZone;

    @ApiModelProperty("硬盘挂载主机Id列表")
    private List<String> serverIds;

    @ApiModelProperty("是否支持加密功能")
    private Boolean encryption;

    @ApiModelProperty("是否放入回收站")
    private Boolean recycle;

    @ApiModelProperty("克隆时的源卷ID")
    private String sourceVolumeId;

    @ApiModelProperty("创建时间")
    private String createdTime;

    @ApiModelProperty("硬盘id")
    private String id;

    @ApiModelProperty("产品类型")
    private ProductTypeEnum productType;

    @ApiModelProperty("是否被删除")
    private Boolean isDelete;

    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("创建来源")
    private CreateSourceEnum createSource;

    @ApiModelProperty("是否支持ISCSI")
    private Boolean iscsi;

    @ApiModelProperty("计费模式，值为year,month,hour")
    private String measure;

    @ApiModelProperty("硬盘大小")
    private Integer size;

    @ApiModelProperty("是否共享")
    private Boolean isShare;

    @ApiModelProperty("硬盘名称")
    private String name;

    @ApiModelProperty("底层可用区")
    private String region;

    @ApiModelProperty("硬盘状态")
    private DiskStatusEnum status;
}
