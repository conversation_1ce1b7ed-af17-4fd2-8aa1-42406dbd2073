package com.aiya.kpy.res.kpyun.client;

import com.aiya.kpy.res.kpyun.exception.ClientException;
import com.aiya.kpy.res.kpyun.exception.ServerException;
import com.aiya.kpy.res.kpyun.profile.DefaultProfile;
import com.aiya.kpy.res.kpyun.profile.IClientProfile;
import com.aiya.kpy.res.kpyun.request.KpyAcsRequest;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Scope("prototype")
public class DefaultKpyunSdkClient implements KpyunSdkClient {
	private final IKpyAcsClient client;
	private final ClientKey key;
	@Autowired
	private ApplicationEventPublisher mongoDbLogEventPublisher;

	public DefaultKpyunSdkClient(ClientKey key) {
		try {
			DefaultProfile.addEndpoint(key.getDefaultRegion(), key.getDefaultRegion(), key.getProduct().name,key.getDomain());
			IClientProfile profile = DefaultProfile.getProfile(key.getDefaultRegion(), key.getAccessKeyId(),key.getSecret());
			this.client = new DefaultKpyAcsClient(profile);
			this.key = key;
		} catch (ClientException e) {
			throw new RuntimeException(e);
		}
	}

	@Override
	public <T extends KpyAcsResponse> KpySdkResponse<T> execute(KpyAcsRequest<T> request) {
		long startTime = System.currentTimeMillis();
		try {
			KpySdkResponse<T> response = doExecute(request);
			dealwithResult(startTime, request, response);
			return response;
		} catch (RuntimeException ex) {
			dealwithResult(startTime, request, ex);
			throw ex;
		}
	}

	private <T extends KpyAcsResponse> void dealwithResult(long startTime, KpyAcsRequest<T> request,Object response) {
		long endTime = System.currentTimeMillis();
		mongoDbLogEventPublisher.publishEvent(new CallOutApiEvent(this, startTime, endTime, key.getZoneCode(), key.getAccessKeyId(), request.getUrl(), response));
	}


	private <T extends KpyAcsResponse> KpySdkResponse<T> doExecute(KpyAcsRequest<T> request) {
		KpySdkResponse<T> response = new KpySdkResponse<>();
		try {
			T acsResponse = client.getAcsResponse(request);
			response.setSuccess(acsResponse.getCode() == null);
			response.setRequestId(acsResponse.getRequestId());
			if (response.isSuccess()) {
				response.setAcsResponse(acsResponse);
			} else {
				response.setErrMsg(acsResponse.getMessage());
				response.setCode(acsResponse.getCode());
			}
		} catch (ClientException e) {
			response.setSuccess(false);
			response.setCode(e.getErrCode());
			response.setRequestId(e.getRequestId());
			response.setErrMsg(e.getErrMsg());
		} catch (ServerException e) {
			response.setSuccess(false);
			response.setCode(e.getErrCode());
			response.setRequestId(e.getRequestId());
			response.setErrMsg(e.getErrMsg());
		}
		return response;
	}
}
