package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteSecurityGroupPath;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcDeleteSecurityGroupRequest;
import com.aiya.kpy.res.ecloud.sdk.response.DeleteSecurityGroupResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeleteSecurityGroupRequest extends ECloudAcsRequest<DeleteSecurityGroupResponse> {
    @ApiModelProperty("安全组id")
    @NotNull(message = "安全组id不能为空")
    private String securityGroupId;


    @Override
    public Params getParams() {
        return Params.builder()
                .action("deleteSecurityGroup")
                .uri("/customer/v3/SecurityGroup/{securityGroupId}")
                .gatewayUri("/api/openapi-vpc/customer/v3/SecurityGroup/{securityGroupId}")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.DELETE)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcDeleteSecurityGroupRequest toRequest() {
        VpcDeleteSecurityGroupPath path = VpcDeleteSecurityGroupPath.builder()
                .securityGroupId(this.securityGroupId)
                .build();
        return VpcDeleteSecurityGroupRequest.builder()
                .deleteSecurityGroupPath(path)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return DeleteSecurityGroupResponse.class;
    }
}
