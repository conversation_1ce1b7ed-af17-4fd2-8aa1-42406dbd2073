package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetFlavorByRegionResponseContent implements ECloudResponseBody {
    @ApiModelProperty("规格名称")
    private String specsName;
    @ApiModelProperty("可用区")
    private String zoneDesc;
    @ApiModelProperty("可用区名称")
    private String zoneName;
    @ApiModelProperty("售罄 1:售罄 0:未售罄")
    private String soldOut;
}
