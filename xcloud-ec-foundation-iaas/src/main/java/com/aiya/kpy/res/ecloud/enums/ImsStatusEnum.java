package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/5
 */
@Getter
@AllArgsConstructor
public enum ImsStatusEnum {
    UNRECOGNIZED("unrecognized"),
    ACTIVE("active"),
    SAVING("saving"),
    QUEUED("queued"),
    KILLED("killed"),
    PENDING_DELETE("pending_delete"),
    DELETED("deleted"),
    CACHING("caching");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
