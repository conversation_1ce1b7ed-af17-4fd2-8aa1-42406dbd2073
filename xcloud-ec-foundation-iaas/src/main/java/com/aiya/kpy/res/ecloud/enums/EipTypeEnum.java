package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum EipTypeEnum {
    /**
     * 虚拟机
     */
    VM("vm"),
    /**
     * 裸金属
     */
    IRONIC("ironic"),
    /**
     * 弹性负载均衡
     */
    ELB("elb"),
    /**
     * 云数据库
     */
    RDS("rds"),
    /**
     * 高可用网卡
     */
    HAVIP("havip"),
    /**
     * 云堡垒机
     */
    BASTION("bastion"),
    /**
     * SNAT
     */
    SNAT("snat"),
    /**
     * DNAT
     */
    DNAT("dnat"),
    /**
     * 虚拟机
     */
    EBM("ebm"),
    /**
     * 虚拟网卡
     */
    PORT("port");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
