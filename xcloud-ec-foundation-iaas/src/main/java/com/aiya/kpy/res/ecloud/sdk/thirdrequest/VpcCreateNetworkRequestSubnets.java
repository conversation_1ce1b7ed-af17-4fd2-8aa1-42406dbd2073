package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/13
 */
@Builder
@Data
public class VpcCreateNetworkRequestSubnets {
    @SerializedName("ipVersion")
    private VersionsEnum ipVersion;
    @SerializedName("cidr")
    private String cidr;
}
