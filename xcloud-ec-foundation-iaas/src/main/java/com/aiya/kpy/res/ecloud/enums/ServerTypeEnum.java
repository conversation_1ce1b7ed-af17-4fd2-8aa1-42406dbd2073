package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 服务器类型
 *
 * <AUTHOR>
 * @date 2024/2/26
 */
@Getter
@AllArgsConstructor
public enum ServerTypeEnum {
    VM("VM"),
    IRONIC("IRONIC"),
    DECLOUD_SERVER("DECLOUD_SERVER"),
    EBM("EBM");

    private final String value;

    @Override
    public String toString() {
        return this.value;
    }
}
