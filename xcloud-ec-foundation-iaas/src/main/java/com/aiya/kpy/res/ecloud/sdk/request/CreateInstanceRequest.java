package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.BillingTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VmTypeEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.response.CreateInstanceResponse;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.*;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateInstanceRequest extends ECloudAcsRequest<CreateInstanceResponse> {
    @ApiModelProperty("是否自动续订")
    private Boolean autoRenew;

    @ApiModelProperty("可用区")
    @NotNull(message = "可用区不能为空")
    private String region;

    @ApiModelProperty("付费方式")
    @NotNull(message = "付费方式不能为空")
    private BillingTypeEnum billingType;

    @ApiModelProperty("云主机规格类型")
    @NotNull(message = "云主机规格类型不能为空")
    private VmTypeEnum vmType;

    @ApiModelProperty("cpu核数")
    @NotNull(message = "cpu核数不能为空")
    private Integer cpu;

    @ApiModelProperty("内存大小(GB)")
    @NotNull(message = "内存大小(GB)不能为空")
    private Integer ram;

    @ApiModelProperty("规格名称")
    private String specsName;

    @ApiModelProperty("系统盘")
    @NotNull(message = "系统盘不能为空")
    private VmCreateRequestBootVolume bootVolume;

    @ApiModelProperty("数据盘")
    private List<VmCreateRequestDataVolume> dataVolume;

    @ApiModelProperty("镜像名称")
    @NotNull(message = "镜像名称不能为空")
    private String imageName;

    @ApiModelProperty("云主机名称")
    @NotNull(message = "云主机名称不能为空")
    private String name;

    @ApiModelProperty("云主机密码")
    private String password;

    @ApiModelProperty("密钥对名称")
    private String keypairName;

    @ApiModelProperty("订购数量")
    private Integer quantity;

    /**
     * 订购时长(月/年)，按量付费填写0;
     * 包月后付费（政企集团或省集团客户）填写需要订购的时长，取值范围为[0,60]，如果取值为0，会设置成订购一个月并开启自动续订，1-60表示具体订购的月数;
     * 包月预付费（互联网客户）填写需要订购的时长，取值范围为[1,12]，1-12表示具体订购的月数;
     * 包年付费填写需要订购的年数，取值范围为[1,5];
     * 说明：该参数在按量或包月后付费默认为0.
     */
    @ApiModelProperty("订购周期,按量付费填写0")
    private Integer duration;

    @ApiModelProperty("网络")
    @NotNull(message = "网络不能为空")
    private VmCreateRequestNetworks networks;

    @ApiModelProperty("一体化订购公网ip产品")
    private VmCreateRequestIp ip;

    @ApiModelProperty("一体化订购带宽产品")
    private VmCreateRequestBandwidth bandwidth;

    @ApiModelProperty("绑定已有资源信息")
    private VmCreateRequestBind bind;

    @ApiModelProperty("安全组id")
    private List<String> securityGroupIds;

    @ApiModelProperty("自定义参数")
    private String userData;

    @ApiModelProperty("描述")
    private String description;

    @ApiModelProperty("标签")
    private List<RequestTags> tags;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("vmCreate")
                .uri("/acl/v3/server/order")
                .gatewayUri("/api/openapi-ecs/acl/v3/server/order")
                .protocol("http")
                .contentType("application/json")
                .method(MethodType.POST)
                .request(this.toRequest())
                .build();
    }

    public VmCreateRequest toRequest() {
        VmCreateBody vmCreateBody = VmCreateBody.builder()
                .bootVolume(this.bootVolume)
                .specsName(this.specsName)
                .imageName(this.imageName)
                .quantity(this.quantity)
                .userData(this.userData)
                .keypairName(this.keypairName)
                .bandwidth(this.bandwidth)
                .ip(this.ip)
                .cpu(this.cpu)
                .description(this.description)
                .networks(this.networks)
                .duration(this.duration)
                .vmType(this.vmType)
                .password(this.password)
                .bind(this.bind)
                .billingType(this.billingType)
                .dataVolume(this.dataVolume)
                .securityGroupIds(this.securityGroupIds)
                .name(this.name)
                .autoRenew(this.autoRenew)
                .region(this.region)
                .ram(this.ram)
                .tags(this.tags)
                .build();
        VmCreateRequest result = VmCreateRequest.builder()
                .vmCreateBody(vmCreateBody)
                .build();
        return result;
    }

    @Override
    public Class getResponseClass() {
        return CreateInstanceResponse.class;
    }
}
