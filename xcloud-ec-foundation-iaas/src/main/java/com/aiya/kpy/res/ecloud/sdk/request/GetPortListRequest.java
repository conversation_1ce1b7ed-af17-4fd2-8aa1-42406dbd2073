package com.aiya.kpy.res.ecloud.sdk.request;

import com.aiya.kpy.res.ecloud.enums.BindStatusEnum;
import com.aiya.kpy.res.ecloud.enums.PortTypeEnum;
import com.aiya.kpy.res.ecloud.enums.VersionsEnum;
import com.aiya.kpy.res.ecloud.request.ECloudAcsRequest;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetPortListQuery;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcGetPortListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.GetPortListResponse;
import com.ecloud.sdk.http.MethodType;
import com.ecloud.sdk.http.Params;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetPortListRequest extends ECloudAcsRequest<GetPortListResponse> {

    @ApiModelProperty("虚拟网卡类型")
    private List<PortTypeEnum> types;

    @ApiModelProperty("所属的子网IP协议类型")
    private List<VersionsEnum> ipVersions;

    @ApiModelProperty("虚拟网卡ID列表")
    private List<String> portRangeInIds;

    @ApiModelProperty("绑定的服务器ID列表")
    private List<String> rangeInServerIds;

    @ApiModelProperty("虚拟网卡名称")
    private String portName;

    @ApiModelProperty("根据虚拟网卡名称或IP地址搜索")
    private String queryWord;

    @ApiModelProperty("可根据主机名称、内网地址进行模糊查询")
    private String queryWordForLbMember;

    @ApiModelProperty("绑定的资源ID")
    private String resourceId;

    @ApiModelProperty("路由器ID")
    private String routerId;

    @ApiModelProperty("网卡是否已绑定fip")
    private Boolean fipBind;

    @ApiModelProperty("是否可外网访问")
    private Boolean extranet;

    @ApiModelProperty("子网ID")
    private String subnetId;

    @ApiModelProperty("虚拟网卡所属的网络ID")
    private String networkId;

    @ApiModelProperty("虚拟网卡关联的安全组ID")
    private String securityGroupId;

    @ApiModelProperty("是否绑定资源")
    private Boolean resourceBind;

    @ApiModelProperty("是否绑定HA VIP")
    private Boolean havipBind;

    @ApiModelProperty("HA VIP ID，查询未绑定该HA VIP的虚拟网卡")
    private String havipId;

    @ApiModelProperty("虚拟网卡绑定资源操作状态")
    private BindStatusEnum operationStatus;

    @ApiModelProperty("根据虚拟网卡绑定的主机名称精确搜索")
    private String serverName;

    @ApiModelProperty("可用区")
    private String region;

    @ApiModelProperty("分页")
    private Integer page;

    @ApiModelProperty("分页大小")
    private Integer pageSize;

    @ApiModelProperty("虚拟网卡的MAC地址")
    private String macAddress;

    @ApiModelProperty("是否获取可绑定IP的网卡，即包含IPv4地址的网卡")
    private Boolean isBindable;

    @Override
    public Params getParams() {
        return Params.builder()
                .action("listPort")
                .uri("/customer/v3/port")
                .gatewayUri("/api/openapi-vpc/customer/v3/port")
                .protocol("https")
                .contentType("application/json")
                .method(MethodType.GET)
                .request(this.toRequest())
                .build();
    }

    @Override
    public VpcGetPortListRequest toRequest() {
        VpcGetPortListQuery query = VpcGetPortListQuery.builder()
                .types(this.types)
                .ipVersions(this.ipVersions)
                .portRangeInIds(this.portRangeInIds)
                .rangeInServerIds(this.rangeInServerIds)
                .portName(this.portName)
                .queryWord(this.queryWord)
                .queryWordForLbMember(this.queryWordForLbMember)
                .resourceId(this.resourceId)
                .routerId(this.routerId)
                .fipBind(this.fipBind)
                .extranet(this.extranet)
                .subnetId(this.subnetId)
                .networkId(this.networkId)
                .securityGroupId(this.securityGroupId)
                .resourceBind(this.resourceBind)
                .havipBind(this.havipBind)
                .havipId(this.havipId)
                .operationStatus(this.operationStatus)
                .serverName(this.serverName)
                .region(this.region)
                .page(this.page)
                .pageSize(this.pageSize)
                .macAddress(this.macAddress)
                .isBindable(this.isBindable)
                .build();
        return VpcGetPortListRequest.builder()
                .listPortQuery(query)
                .build();
    }

    @Override
    public Class getResponseClass() {
        return GetPortListResponse.class;
    }

    public enum IpVersionEnum {
        IPV4("V4"),
        IPV6("V6");

        private String value;

        IpVersionEnum(String value) {
            this.value = value;
        }

        public String getValue() {
            return this.value;
        }

        @Override
        public String toString() {
            return this.value;
        }
    }
}
