/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */
package com.aiya.kpy.res.kpyun.request;


import com.aiya.kpy.res.kpyun.auth.Credential;
import com.aiya.kpy.res.kpyun.auth.ISignatureComposer;
import com.aiya.kpy.res.kpyun.auth.ISigner;
import com.aiya.kpy.res.kpyun.auth.KpyAcsURLEncoder;
import com.aiya.kpy.res.kpyun.enums.FormatType;
import com.aiya.kpy.res.kpyun.enums.ProtocolType;
import com.aiya.kpy.res.kpyun.profile.ProductDomain;
import com.aiya.kpy.res.kpyun.response.KpyAcsResponse;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 * @param <T>
 */
public abstract class KpyAcsRequest<T extends KpyAcsResponse> extends HttpRequest {

	protected ISignatureComposer composer = null;

	/**
	 * 防止参数冲突加个前缀
	 */
	private String acsVersion = null;
	private String acsProduct = null;
	private String acsActionName = null;
	private String acsRegionId = null;
	private String acsSecurityToken = null;
	private FormatType acsAcceptFormat = null;
	private ProtocolType acsProtocol = ProtocolType.HTTP;
	private final Map<String, String> queryParameters = new HashMap<String, String>();
	private final Map<String, String> domainParameters = new HashMap<String, String>();

	public KpyAcsRequest(String product) {
		super(null);
		this.headers.put("x-sdk-client", "Java/2.0.0");
		this.acsProduct = product;
	}

	public KpyAcsRequest(String product, String version) {
		super(null);
		this.acsProduct = product;
		this.setAcsVersion(version);
	}

	public static String concatQueryString(Map<String, String> parameters)
			throws UnsupportedEncodingException {
		if (null == parameters) {
			return null;
		}
		StringBuilder urlBuilder = new StringBuilder("");
		for (Entry<String, String> entry : parameters.entrySet()) {
			String key = entry.getKey();
			String val = entry.getValue();
			urlBuilder.append(KpyAcsURLEncoder.encode(key));
			if (val != null) {
				urlBuilder.append("=").append(KpyAcsURLEncoder.encode(val));
			}
			urlBuilder.append("&");
		}

		int strIndex = urlBuilder.length();
		if (parameters.size() > 0) {
			urlBuilder.deleteCharAt(strIndex - 1);
		}
		return urlBuilder.toString();
	}

	public String getAcsActionName() {
		return acsActionName;
	}

	public void setAcsActionName(String actionName) {
		this.acsActionName = actionName;
	}

	public String getAcsProduct() {
		return acsProduct;
	}

	public ProtocolType getAcsProtocol() {
		return acsProtocol;
	}

	public void setAcsProtocol(ProtocolType protocol) {
		this.acsProtocol = protocol;
	}

	public Map<String, String> getQueryParameters() {
		return Collections.unmodifiableMap(queryParameters);
	}

	public <K> void putQueryParameter(String name, K value) {
		setParameter(this.queryParameters, name, value);
	}

	protected void putQueryParameter(String name, String value) {
		setParameter(this.queryParameters, name, value);
	}

	public Map<String, String> getDomainParameters() {
		return Collections.unmodifiableMap(domainParameters);
	}

	protected void putDomainParameter(String name, Object value) {
		setParameter(this.domainParameters, name, value);
	}

	protected void putDomainParameter(String name, String value) {
		setParameter(this.domainParameters, name, value);
	}

	protected <K> void setParameter(Map<String, String> map, String name, K value) {
		if (null == map || null == name || null == value) {
			return;
		}
		map.put(name, String.valueOf(value));
	}

	public String getAcsVersion() {
		return acsVersion;
	}

	public void setAcsVersion(String version) {
		this.acsVersion = version;
	}

	public FormatType getAcsAcceptFormat() {
		return acsAcceptFormat;
	}

	public void setAcsAcceptFormat(FormatType acceptFormat) {
		this.acsAcceptFormat = acceptFormat;
		this.putHeaderParameter("Accept",
				FormatType.mapFormatToAccept(acceptFormat));
	}

	public String getAcsRegionId() {
		return acsRegionId;
	}

	public void setAcsRegionId(String regionId) {
		this.acsRegionId = regionId;
	}

	public String getAcsSecurityToken() {
		return acsSecurityToken;
	}

	public void setAcsSecurityToken(String securityToken) {
		this.acsSecurityToken = securityToken;
	}

	public abstract HttpRequest signRequest(ISigner signer, Credential credential,
											FormatType format, ProductDomain domain)
			throws InvalidKeyException, IllegalStateException,
			UnsupportedEncodingException, NoSuchAlgorithmException;

	public abstract String composeUrl(String endpoint, Map<String, String> queries)
			throws UnsupportedEncodingException;

	public abstract Class<T> getResponseClass();
}
