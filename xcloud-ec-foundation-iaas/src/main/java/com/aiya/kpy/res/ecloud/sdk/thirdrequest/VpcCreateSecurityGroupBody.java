package com.aiya.kpy.res.ecloud.sdk.thirdrequest;

import com.aiya.kpy.res.ecloud.enums.ResourceTypeEnum;
import com.ecloud.sdk.position.Body;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/15
 */
@Data
@Builder
public class VpcCreateSecurityGroupBody extends Body {
    @SerializedName("name")
    private String name;
    @SerializedName("description")
    private String description;
    @SerializedName("region")
    private String region;
    @SerializedName("type")
    private ResourceTypeEnum type;
    @SerializedName("stateful")
    private Boolean stateful;
}
