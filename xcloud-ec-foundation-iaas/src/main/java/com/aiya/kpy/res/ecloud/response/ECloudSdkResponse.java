package com.aiya.kpy.res.ecloud.response;

import com.aiya.kpy.ec.foundation.iaas.manager.exception.AbstractIaaSBusinessException;
import com.aiya.kpy.res.ecloud.enums.StateEnum;
import com.google.gson.annotations.SerializedName;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.function.Supplier;

/**
 * <AUTHOR>
 * @date 2024/3/6
 */
@Getter
@Setter
public class ECloudSdkResponse<T extends ECloudResponseBody> {
    @SerializedName("requestId")
    private String requestId;
    @SerializedName("errorMessage")
    private String errorMessage;
    @SerializedName("errorCode")
    private String errorCode;
    @SerializedName("state")
    private StateEnum state;
    @SerializedName("errorParams")
    private List<String> errorParams;
    /**
     * api接口返回不同
     */
    private List<T> list;

    public T getBody() {
        if (CollectionUtils.isEmpty(this.list)) {
            return null;
        }
        return this.list.get(0);
    }

    public <E extends AbstractIaaSBusinessException> void checkResponse(Supplier<E> exceptionSupplier) throws E {
        if (!StateEnum.OK.equals(this.state)) {
            throw exceptionSupplier.get();
        }
    }
}
