package com.aiya.kpy.res.ecloud.sdk.response;

import com.aiya.kpy.res.ecloud.response.ECloudResponseBody;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/26
 */
@Data
public class ChangeEipResponse implements ECloudResponseBody {
    @SerializedName("orderId")
    private String orderId;
    @SerializedName("orderExtResps")
    private List<ChangeEipResponseContent> orderExtResps;
}
