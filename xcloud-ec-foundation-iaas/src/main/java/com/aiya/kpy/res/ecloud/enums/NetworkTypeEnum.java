package com.aiya.kpy.res.ecloud.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@Getter
@AllArgsConstructor
public enum NetworkTypeEnum {
    /**
     * 云主机子网
     */
    VM("VM"),
    /**
     * 物理机子网
     */
    IRONIC("IRONIC"),
    /**
     * 互联子网
     */
    INTER_CONNECTION("INTER_CONNECTION"),
    EBM("EBM"),
    VMWARE("VMWARE"),
    DBAUDIT("DBAUDIT");

    private String value;

    @Override
    public String toString() {
        return this.value;
    }
}
