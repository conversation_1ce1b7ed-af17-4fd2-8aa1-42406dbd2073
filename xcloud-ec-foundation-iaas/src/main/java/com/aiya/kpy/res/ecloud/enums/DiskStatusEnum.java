package com.aiya.kpy.res.ecloud.enums;

import com.aiya.platform.foundation.enums.EnumsValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024/3/24
 */
@Getter
@AllArgsConstructor
public enum DiskStatusEnum implements EnumsValue {
    AVAILABLE("AVAILABLE", "available", "可用"),
    ATTACHING("ATTACHING", "attaching", "挂载中"),
    BACKING_UP("BACKING_UP", "backing-up", "备份中"),
    CREATING("CREATING", "creating", "创建中"),
    DELETING("DELETING", "deleting", "删除中"),
    DOWNLOADING("DOWNLOADING", "downloading", "下载中"),
    UPLOADING("UPLOADING", "uploading", "上传中"),
    ERROR("ERROR", "error", "错误"),
    ERROR_DELETING("ERROR_DELETING", "error-deleting", "删除错误"),
    ERROR_RESTORING("ERROR_RESTORING", "error-restoring", "恢复错误"),
    IN_USE("IN_USE", "in-use", "使用中"),
    RESTORING_BACKUP("RESTORING_BACKUP", "restoring-backup", "恢复备份中"),
    DETACHING("DETACHING", "detaching", "卸载中"),
    UNRECOGNIZED("UNRECOGNIZED", "unrecognized", "未识别"),
    EXTENDING("EXTENDING", "extending", "扩容中"),
    ERROR_EXTENDING("ERROR_EXTENDING", "error-extending", "扩容错误"),
    REINITIALIZING("REINITIALIZING", "reinitializing", "重新初始化中"),
    ERROR_REINITIALIZING("ERROR_REINITIALIZING", "error-reinitializing", "重新初始化错误"),
    DELETED("DELETED", "deleted", "已删除"),
    ERROR_MANAGING("ERROR_MANAGING", "error-managing", "管理错误"),
    MANAGING("MANAGING", "managing", "管理中"),
    MAINTENANCE("MAINTENANCE", "maintenance", "维护中"),
    ERROR_BACKING_UP("ERROR_BACKING_UP", "error-backing-up", "备份错误"),
    RETYPING("RETYPING", "retyping", "重置类型中"),
    REVERTING("REVERTING", "reverting", "回滚中"),;

    private String value;
    private String code;
    private String label;
    @Override
    public String toString() {
        return this.value;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public String getLabel() {
        return this.label;
    }

    public static DiskStatusEnum fromCodeOrValue(String codeOrValue) {
        for (DiskStatusEnum diskStatusEnum : DiskStatusEnum.values()) {
            if (diskStatusEnum.getCode().equals(codeOrValue) || diskStatusEnum.getValue().equals(codeOrValue)) {
                return diskStatusEnum;
            }
        }
        throw new IllegalArgumentException("Unknown DiskStatusEnum code: " + codeOrValue);
    }
}
