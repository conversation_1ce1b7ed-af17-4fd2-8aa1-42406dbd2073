package com.aiya.kpy.res.ecloud.client;

import com.aiya.kpy.res.ecloud.enums.ECloudProductEnum;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/2/28
 */
@Data
@AllArgsConstructor
public class ClientKey {
    /**
     * 访问移动云的ak
     */
    private String accessKey;

    /**
     * 访问移动云的sk
     */
    private String secretKey;

    /**
     * 访问移动云的regionCode
     */
    private String poolId;


    /**
     * 访问移动云的可用区id
     */
    private String regionId;

    /**
     * 访问移动云的产品
     */
    private ECloudProductEnum product;

    private String endpoint;

    public ClientKey(String accessKey, String secretKey, String poolId, ECloudProductEnum product, String endpoint) {
        this.accessKey = accessKey;
        this.secretKey = secretKey;
        this.poolId = poolId;
        this.product = product;
        this.endpoint = endpoint;
    }
}
