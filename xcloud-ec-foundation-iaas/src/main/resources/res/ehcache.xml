<?xml version="1.0" encoding="UTF-8"?>
<ehcache xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://ehcache.org/ehcache.xsd"
         updateCheck="false">

    <!--持久化磁盘路径-->
    <diskStore path="java.io.tmpdir"/>

    <!--默认缓存设置-->
    <defaultCache maxElementsInMemory="1000"
                  eternal="false"
                  timeToIdleSeconds="1000"
                  timeToLiveSeconds="1000"
                  overflowToDisk="false"
                  maxElementsOnDisk="10000"
                  diskPersistent="false"
                  diskExpiryThreadIntervalSeconds="120"
                  memoryStoreEvictionPolicy="FIFO"
    />

    <cache name="resZoneProviderAccount"
           maxEntriesLocalHeap="1000"
           eternal="false"
           timeToIdleSeconds="1000"
           timeToLiveSeconds="1000"
           overflowToDisk="false"
           statistics="true"
           memoryStoreEvictionPolicy="FIFO">
    </cache>
</ehcache>