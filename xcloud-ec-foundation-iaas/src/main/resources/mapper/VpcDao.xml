<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiya.kpy.res.kpyun.dao.ECloudVlanDao">
    <resultMap type="com.aiya.kpy.res.kpyun.entity.ECloudVlanEntity"
               id="resultMap" autoMapping="true"></resultMap>

    <select id="list" resultMap="resultMap">
        SELECT * FROM ECLOUD_VLAN
        WHERE PLATFORM_REGION_CODE = #{platformRegionCode} AND DELETE_TIME = 0
        <if test="platformZoneCode != null">
            AND PLATFORM_ZONE_CODE = #{platformZoneCode}
        </if>
        <if test="vlanStatus != null ">
            AND STATUS = #{vlanStatus}
        </if>
        <if test="vlanUUID != null and vlanUUID.length != 0">
            AND THIRD_PARTY_RESOURCE_UUID IN
            <foreach collection="vlanUUID" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="getLastIp" resultMap="resultMap">
        SELECT *
        FROM ECLOUD_VLAN
                 INNER JOIN (SELECT VPC_ID, maxIp AS minIp
                             FROM (SELECT VPC_ID, INET_NTOA(MAX(INET_ATON(IP))) maxIp
                                   FROM ECLOUD_VLAN
                                   WHERE PLATFORM_REGION_CODE = #{platformRegionCode} AND PLATFORM_ZONE_CODE = #{platformZoneCode} AND DELETE_TIME = 0
                                   GROUP BY VPC_ID) maxIpTable
                             ORDER BY INET_ATON(maxIpTable.maxIp)
                             LIMIT 1) AS maxIpTable
                            ON ECLOUD_VLAN.VPC_ID = maxIpTable.VPC_ID AND ECLOUD_VLAN.IP = maxIpTable.minIp
        WHERE PLATFORM_REGION_CODE = #{platformRegionCode} AND PLATFORM_ZONE_CODE = #{platformZoneCode} AND DELETE_TIME = 0
    </select>
</mapper>