package com.aiyounet.cs.ecs.convert;

import com.aiyounet.cs.ecs.bean.SDConfig;
import java.util.Map;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-07-24T10:40:30+0800",
    comments = "version: 1.2.0.Final, compiler: javac, environment: Java 1.8.0_462 (BellSoft)"
)
public class SDConfigConvertImpl implements SDConfigConvert {

    @Override
    public SDConfig convert(Map map) {
        if ( map == null ) {
            return null;
        }

        SDConfig sDConfig = new SDConfig();

        return sDConfig;
    }
}
