package com.aiya.kpy.container.task.model;

import com.aiya.kpy.ec.foundation.model.DefaultAbstractTaskModel;
import lombok.Getter;
import lombok.Setter;

/**
 * 容器异步任务模型
 * 用于容器云平台各种操作的异步任务处理
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Getter
@Setter
public class ContainerTaskModel extends DefaultAbstractTaskModel {

    /**
     * 地域代码
     */
    private String regionCode;

    /**
     * 可用区代码
     */
    private String zoneCode;

    /**
     * 容器UUID
     */
    private String containerUuid;

    /**
     * 容器规格UUID
     */
    private String flavorUuid;

    /**
     * 镜像UUID
     */
    private String imageUuid;

    /**
     * 网络UUID
     */
    private String networkUuid;

    /**
     * 容器名称
     */
    private String containerName;

    /**
     * 容器描述
     */
    private String description;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 域ID
     */
    private Long domainId;

    /**
     * 网卡UUID（用于网卡相关操作）
     */
    private String nicUuid;

    /**
     * VLAN UUID（用于VLAN相关操作）
     */
    private String vlanUuid;

    /**
     * VLAN名称
     */
    private String vlanName;

    /**
     * 是否内部VLAN
     */
    private Boolean isInternal;

    /**
     * SSH密钥UUID（用于SSH密钥相关操作）
     */
    private String sshkeyUuid;

    /**
     * SSH密钥名称
     */
    private String sshkeyName;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 任务UUID（底层返回的任务ID）
     */
    private String taskUuid;

    /**
     * 请求ID
     */
    private String requestId;

    /**
     * 主机ID
     */
    private String hostId;

    /**
     * 容器状态
     */
    private String containerState;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 网关地址
     */
    private String gateway;

    /**
     * 子网掩码
     */
    private String netmask;

    /**
     * VLAN ID
     */
    private Integer vlanId;

    /**
     * SSH公钥内容
     */
    private String publicKey;

    /**
     * SSH私钥内容
     */
    private String privateKey;

    /**
     * 容器网络类型
     */
    private String containerNetType;

    /**
     * GPU数量
     */
    private Integer gpuNum;

    /**
     * 宿主机SN
     */
    private String hostSn;

    /**
     * SSH密钥UUID
     */
    private String keyUuid;

    /**
     * 容器密码
     */
    private String pswd;

    /**
     * 运行参数JSON
     */
    private String runArgs;

    /**
     * 存储卷配置JSON
     */
    private String vols;

    /**
     * 网卡配置JSON
     */
    private String nics;

}
