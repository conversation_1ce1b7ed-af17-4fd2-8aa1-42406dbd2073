package com.aiya.kpy.container.task.state;

import com.aiya.platform.foundation.enums.EnumsValue;

/**
 * 创建容器实例异步任务状态枚举
 * 定义创建容器实例操作的状态流转
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public enum CreateContainerInstanceTaskState implements EnumsValue {

    /**
     * 初始化状态
     */
    INIT,

    /**
     * 创建容器实例
     */
    CREATE_CONTAINER_INSTANCE,

    /**
     * 查询操作状态
     */
    QUERY_OPERATION_STATUS,

    /**
     * 保存操作结果
     */
    SAVE_OPERATION_RESULT;

    @Override
    public String getCode() {
        return name();
    }

    @Override
    public String getLabel() {
        return name();
    }
}
