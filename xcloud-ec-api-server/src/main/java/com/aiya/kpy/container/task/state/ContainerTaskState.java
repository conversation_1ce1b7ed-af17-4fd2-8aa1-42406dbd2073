package com.aiya.kpy.container.task.state;

import com.aiya.platform.foundation.enums.EnumsValue;

/**
 * 容器异步任务状态枚举
 * 定义容器云平台各种操作的异步任务状态流转
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public enum ContainerTaskState implements EnumsValue {

    /**
     * 初始化状态
     */
    INIT,

    /**
     * 创建容器实例
     */
    CREATE_CONTAINER_INSTANCE,

    /**
     * 启动容器实例
     */
    START_CONTAINER_INSTANCE,

    /**
     * 停止容器实例
     */
    STOP_CONTAINER_INSTANCE,

    /**
     * 删除容器实例
     */
    DELETE_CONTAINER_INSTANCE,

    /**
     * 创建容器网卡
     */
    CREATE_CONTAINER_NIC,

    /**
     * 创建容器VLAN
     */
    CREATE_CONTAINER_VLAN,

    /**
     * 创建容器SSH密钥
     */
    CREATE_CONTAINER_SSHKEY,

    /**
     * 查询操作状态
     */
    QUERY_OPERATION_STATUS,

    /**
     * 保存操作结果
     */
    SAVE_OPERATION_RESULT,

    /**
     * 任务完成
     */
    TASK_COMPLETED;

    @Override
    public String getCode() {
        return name();
    }

    @Override
    public String getLabel() {
        return name();
    }
}
