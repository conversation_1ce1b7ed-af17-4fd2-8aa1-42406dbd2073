package com.aiya.kpy.container.task.listener;

import com.aiya.kpy.container.foundation.service.ContainerService;
import com.aiya.kpy.container.task.model.ContainerTaskModel;
import com.aiya.kpy.container.task.state.StopContainerInstanceTaskState;
import com.aiya.kpy.ec.task.listener.AbstractAsyncTaskListener;
import com.aiya.kpy.res.sdk.request.StopContainerInstanceRequest;
import com.aiya.kpy.res.sdk.response.StopContainerInstanceResponse;
import com.aiya.kpy.async.foundation.model.AsyncTaskGroups;
import com.aiya.kpy.async.foundation.model.AsyncTaskName;
import com.aiya.platform.asynctask.AsyncTaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 停止容器实例异步任务监听器
 * 处理容器实例停止的异步任务流程
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@Service
public class StopContainerInstanceTaskListener extends AbstractAsyncTaskListener<ContainerTaskModel, StopContainerInstanceTaskState> {

    @Autowired
    private ContainerService containerService;

    @Override
    public AsyncTaskResult process(ContainerTaskModel taskModel, StopContainerInstanceTaskState state) {
        log.info("处理停止容器实例异步任务，状态：{}，容器UUID：{}", state, taskModel.getContainerUuid());
        
        switch (state) {
            case INIT:
                return dealWithInit(taskModel);
            case STOP_CONTAINER_INSTANCE:
                return dealWithStopContainerInstance(taskModel);
            case QUERY_OPERATION_STATUS:
                return dealWithQueryOperationStatus(taskModel);
            case SAVE_OPERATION_RESULT:
                return dealWithSaveOperationResult(taskModel);
            default:
                throw new IllegalStateException("不支持的任务状态：" + state);
        }
    }

    /**
     * 处理初始化状态
     */
    private AsyncTaskResult dealWithInit(ContainerTaskModel taskModel) {
        log.info("开始初始化停止容器实例任务，容器UUID：{}", taskModel.getContainerUuid());
        
        // 初始化重试次数
        taskModel.initRetryTime();
        
        // 转到停止容器实例状态
        return AsyncTaskResult.next(StopContainerInstanceTaskState.STOP_CONTAINER_INSTANCE, taskModel);
    }

    /**
     * 处理停止容器实例
     */
    private AsyncTaskResult dealWithStopContainerInstance(ContainerTaskModel taskModel) {
        log.info("开始停止容器实例，容器UUID：{}", taskModel.getContainerUuid());
        
        try {
            // 构建Foundation层请求
            StopContainerInstanceRequest request = new StopContainerInstanceRequest();
            request.setRegionId(taskModel.getRegionCode());
            request.setUuid(taskModel.getContainerUuid());
            
            // 调用Foundation层服务
            StopContainerInstanceResponse response = containerService.stopContainerInstance(request);
            
            // 保存返回的任务信息
            taskModel.setTaskUuid(response.getTaskUuid());
            taskModel.setRequestId(response.getRequestId());
            taskModel.setHostId(response.getHostId());
            
            log.info("停止容器实例请求成功，任务UUID：{}", response.getTaskUuid());
            
            // 转到查询操作状态
            return AsyncTaskResult.next(StopContainerInstanceTaskState.QUERY_OPERATION_STATUS, taskModel);
            
        } catch (Exception e) {
            log.error("停止容器实例失败，容器UUID：{}，错误信息：{}", taskModel.getContainerUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("StopContainerInstanceFailed", "停止容器实例失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理查询操作状态
     */
    private AsyncTaskResult dealWithQueryOperationStatus(ContainerTaskModel taskModel) {
        log.info("查询容器实例停止状态，任务UUID：{}", taskModel.getTaskUuid());
        
        try {
            // 这里可以根据需要查询底层任务状态
            // 暂时直接转到保存结果状态
            return AsyncTaskResult.next(StopContainerInstanceTaskState.SAVE_OPERATION_RESULT, taskModel);
            
        } catch (Exception e) {
            log.error("查询容器实例停止状态失败，任务UUID：{}，错误信息：{}", taskModel.getTaskUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("QueryOperationStatusFailed", "查询操作状态失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_STATE_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理保存操作结果
     */
    private AsyncTaskResult dealWithSaveOperationResult(ContainerTaskModel taskModel) {
        log.info("保存容器实例停止结果，容器UUID：{}", taskModel.getContainerUuid());
        
        try {
            // 这里可以保存停止结果到数据库或进行其他后续处理
            log.info("容器实例停止任务完成，容器UUID：{}，任务UUID：{}", 
                    taskModel.getContainerUuid(), taskModel.getTaskUuid());
            
            return AsyncTaskResult.success();
            
        } catch (Exception e) {
            log.error("保存容器实例停止结果失败，容器UUID：{}，错误信息：{}", 
                    taskModel.getContainerUuid(), e.getMessage(), e);
            return fail("SaveOperationResultFailed", "保存操作结果失败：" + e.getMessage());
        }
    }

    @Override
    public void processWhenFail(ContainerTaskModel taskModel) {
        log.error("停止容器实例任务失败，容器UUID：{}", taskModel.getContainerUuid());
        // 这里可以进行失败后的清理工作
    }

    @Override
    public String taskGroupName() {
        return AsyncTaskGroups.CONTAINER_TASK_GROUP;
    }

    @Override
    public String taskName() {
        return AsyncTaskName.STOP_CONTAINER_INSTANCE.getCode();
    }
}
