package com.aiya.kpy.container.task.listener;

import com.aiya.kpy.container.foundation.service.ContainerService;
import com.aiya.kpy.container.task.model.ContainerTaskModel;
import com.aiya.kpy.container.task.state.CreateContainerVlanTaskState;
import com.aiya.kpy.ec.task.listener.AbstractAsyncTaskListener;
import com.aiya.kpy.res.sdk.request.CreateContainerVlanRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerVlanResponse;
import com.aiya.kpy.async.foundation.model.AsyncTaskGroups;
import com.aiya.kpy.async.foundation.model.AsyncTaskName;
import com.aiya.platform.asynctask.AsyncTaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 创建容器VLAN异步任务监听器
 * 处理容器VLAN创建的异步任务流程
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@Service
public class CreateContainerVlanTaskListener extends AbstractAsyncTaskListener<ContainerTaskModel, CreateContainerVlanTaskState> {

    @Autowired
    private ContainerService containerService;

    @Override
    public AsyncTaskResult process(ContainerTaskModel taskModel, CreateContainerVlanTaskState state) {
        log.info("处理创建容器VLAN异步任务，状态：{}，VLAN UUID：{}", state, taskModel.getVlanUuid());
        
        switch (state) {
            case INIT:
                return dealWithInit(taskModel);
            case CREATE_CONTAINER_VLAN:
                return dealWithCreateContainerVlan(taskModel);
            case QUERY_OPERATION_STATUS:
                return dealWithQueryOperationStatus(taskModel);
            case SAVE_OPERATION_RESULT:
                return dealWithSaveOperationResult(taskModel);
            default:
                throw new IllegalStateException("不支持的任务状态：" + state);
        }
    }

    /**
     * 处理初始化状态
     */
    private AsyncTaskResult dealWithInit(ContainerTaskModel taskModel) {
        log.info("开始初始化创建容器VLAN任务，VLAN UUID：{}", taskModel.getVlanUuid());
        
        // 初始化重试次数
        taskModel.initRetryTime();
        
        // 转到创建容器VLAN状态
        return AsyncTaskResult.next(CreateContainerVlanTaskState.CREATE_CONTAINER_VLAN, taskModel);
    }

    /**
     * 处理创建容器VLAN
     */
    private AsyncTaskResult dealWithCreateContainerVlan(ContainerTaskModel taskModel) {
        log.info("开始创建容器VLAN，VLAN名称：{}，VLAN UUID：{}", 
                taskModel.getVlanName(), taskModel.getVlanUuid());
        
        try {
            // 构建Foundation层请求
            CreateContainerVlanRequest request = new CreateContainerVlanRequest();
            request.setRegionId(taskModel.getRegionCode());
            request.setDescription(taskModel.getDescription());
            request.setIsInternal(taskModel.getIsInternal());
            request.setVni(taskModel.getVlanId());
            request.setZoneUuid(taskModel.getZoneCode());
            
            // 调用Foundation层服务
            CreateContainerVlanResponse response = containerService.createContainerVlan(request);
            
            // 保存返回的任务信息
            taskModel.setRequestId(response.getRequestId());
            taskModel.setHostId(response.getHostId());

            // 保存VLAN信息
            if (response.getInfo() != null) {
                taskModel.setVlanUuid(response.getInfo().getUuid());
            }

            log.info("创建容器VLAN请求成功，请求ID：{}", response.getRequestId());
            
            // 转到查询操作状态
            return AsyncTaskResult.next(CreateContainerVlanTaskState.QUERY_OPERATION_STATUS, taskModel);
            
        } catch (Exception e) {
            log.error("创建容器VLAN失败，VLAN名称：{}，VLAN UUID：{}，错误信息：{}", 
                    taskModel.getVlanName(), taskModel.getVlanUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("CreateContainerVlanFailed", "创建容器VLAN失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理查询操作状态
     */
    private AsyncTaskResult dealWithQueryOperationStatus(ContainerTaskModel taskModel) {
        log.info("查询容器VLAN创建状态，VLAN UUID：{}", taskModel.getVlanUuid());
        
        try {
            // 这里可以根据需要查询底层任务状态
            // 暂时直接转到保存结果状态
            return AsyncTaskResult.next(CreateContainerVlanTaskState.SAVE_OPERATION_RESULT, taskModel);
            
        } catch (Exception e) {
            log.error("查询容器VLAN创建状态失败，VLAN UUID：{}，错误信息：{}", taskModel.getVlanUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("QueryOperationStatusFailed", "查询操作状态失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_STATE_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理保存操作结果
     */
    private AsyncTaskResult dealWithSaveOperationResult(ContainerTaskModel taskModel) {
        log.info("保存容器VLAN创建结果，VLAN UUID：{}", taskModel.getVlanUuid());
        
        try {
            // 这里可以保存创建结果到数据库或进行其他后续处理
            log.info("容器VLAN创建任务完成，VLAN名称：{}，VLAN UUID：{}",
                    taskModel.getVlanName(), taskModel.getVlanUuid());
            
            return AsyncTaskResult.success();
            
        } catch (Exception e) {
            log.error("保存容器VLAN创建结果失败，VLAN UUID：{}，错误信息：{}", 
                    taskModel.getVlanUuid(), e.getMessage(), e);
            return fail("SaveOperationResultFailed", "保存操作结果失败：" + e.getMessage());
        }
    }

    @Override
    public void processWhenFail(ContainerTaskModel taskModel) {
        log.error("创建容器VLAN任务失败，VLAN名称：{}，VLAN UUID：{}",
                taskModel.getVlanName(), taskModel.getVlanUuid());
        // 这里可以进行失败后的清理工作
    }

    @Override
    public String taskGroupName() {
        return AsyncTaskGroups.CONTAINER_TASK_GROUP;
    }

    @Override
    public String taskName() {
        return AsyncTaskName.CREATE_CONTAINER_VLAN.getCode();
    }
}
