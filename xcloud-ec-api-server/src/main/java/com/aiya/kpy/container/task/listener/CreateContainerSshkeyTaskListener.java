package com.aiya.kpy.container.task.listener;

import com.aiya.kpy.container.foundation.service.ContainerService;
import com.aiya.kpy.container.task.model.ContainerTaskModel;
import com.aiya.kpy.container.task.state.CreateContainerSshkeyTaskState;
import com.aiya.kpy.ec.task.listener.AbstractAsyncTaskListener;
import com.aiya.kpy.res.sdk.request.CreateContainerSshkeyRequest;
import com.aiya.kpy.res.sdk.response.CreateContainerSshkeyResponse;
import com.aiya.kpy.async.foundation.model.AsyncTaskGroups;
import com.aiya.kpy.async.foundation.model.AsyncTaskName;
import com.aiya.platform.asynctask.AsyncTaskResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 创建容器SSH密钥异步任务监听器
 * 处理容器SSH密钥创建的异步任务流程
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@Service
public class CreateContainerSshkeyTaskListener extends AbstractAsyncTaskListener<ContainerTaskModel, CreateContainerSshkeyTaskState> {

    @Autowired
    private ContainerService containerService;

    @Override
    public AsyncTaskResult process(ContainerTaskModel taskModel, CreateContainerSshkeyTaskState state) {
        log.info("处理创建容器SSH密钥异步任务，状态：{}，SSH密钥UUID：{}", state, taskModel.getSshkeyUuid());
        
        switch (state) {
            case INIT:
                return dealWithInit(taskModel);
            case CREATE_CONTAINER_SSHKEY:
                return dealWithCreateContainerSshkey(taskModel);
            case QUERY_OPERATION_STATUS:
                return dealWithQueryOperationStatus(taskModel);
            case SAVE_OPERATION_RESULT:
                return dealWithSaveOperationResult(taskModel);
            default:
                throw new IllegalStateException("不支持的任务状态：" + state);
        }
    }

    /**
     * 处理初始化状态
     */
    private AsyncTaskResult dealWithInit(ContainerTaskModel taskModel) {
        log.info("开始初始化创建容器SSH密钥任务，SSH密钥UUID：{}", taskModel.getSshkeyUuid());
        
        // 初始化重试次数
        taskModel.initRetryTime();
        
        // 转到创建容器SSH密钥状态
        return AsyncTaskResult.next(CreateContainerSshkeyTaskState.CREATE_CONTAINER_SSHKEY, taskModel);
    }

    /**
     * 处理创建容器SSH密钥
     */
    private AsyncTaskResult dealWithCreateContainerSshkey(ContainerTaskModel taskModel) {
        log.info("开始创建容器SSH密钥，SSH密钥名称：{}，SSH密钥UUID：{}", 
                taskModel.getSshkeyName(), taskModel.getSshkeyUuid());
        
        try {
            // 构建Foundation层请求
            CreateContainerSshkeyRequest request = new CreateContainerSshkeyRequest();
            request.setRegionId(taskModel.getRegionCode());
            request.setDescription(taskModel.getDescription());
            
            // 调用Foundation层服务
            CreateContainerSshkeyResponse response = containerService.createContainerSshkey(request);
            
            // 保存返回的任务信息
            taskModel.setRequestId(response.getRequestId());
            taskModel.setHostId(response.getHostId());

            // 保存SSH密钥信息
            if (response.getInfo() != null) {
                taskModel.setSshkeyUuid(response.getInfo().getUuid());
            }
            
            log.info("创建容器SSH密钥请求成功，请求ID：{}", response.getRequestId());
            
            // 转到查询操作状态
            return AsyncTaskResult.next(CreateContainerSshkeyTaskState.QUERY_OPERATION_STATUS, taskModel);
            
        } catch (Exception e) {
            log.error("创建容器SSH密钥失败，SSH密钥名称：{}，SSH密钥UUID：{}，错误信息：{}", 
                    taskModel.getSshkeyName(), taskModel.getSshkeyUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("CreateContainerSshkeyFailed", "创建容器SSH密钥失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理查询操作状态
     */
    private AsyncTaskResult dealWithQueryOperationStatus(ContainerTaskModel taskModel) {
        log.info("查询容器SSH密钥创建状态，任务UUID：{}", taskModel.getTaskUuid());
        
        try {
            // 这里可以根据需要查询底层任务状态
            // 暂时直接转到保存结果状态
            return AsyncTaskResult.next(CreateContainerSshkeyTaskState.SAVE_OPERATION_RESULT, taskModel);
            
        } catch (Exception e) {
            log.error("查询容器SSH密钥创建状态失败，SSH密钥UUID：{}，错误信息：{}", taskModel.getSshkeyUuid(), e.getMessage(), e);
            
            if (taskModel.isOverMaxRetryTime()) {
                return fail("QueryOperationStatusFailed", "查询操作状态失败，已达到最大重试次数：" + e.getMessage());
            }
            
            taskModel.plusRetryTime();
            return AsyncTaskResult.retry(RETRY_WAIT_STATE_SECONDS, e.getMessage());
        }
    }

    /**
     * 处理保存操作结果
     */
    private AsyncTaskResult dealWithSaveOperationResult(ContainerTaskModel taskModel) {
        log.info("保存容器SSH密钥创建结果，SSH密钥UUID：{}", taskModel.getSshkeyUuid());
        
        try {
            // 这里可以保存创建结果到数据库或进行其他后续处理
            log.info("容器SSH密钥创建任务完成，SSH密钥名称：{}，SSH密钥UUID：{}",
                    taskModel.getSshkeyName(), taskModel.getSshkeyUuid());
            
            return AsyncTaskResult.success();
            
        } catch (Exception e) {
            log.error("保存容器SSH密钥创建结果失败，SSH密钥UUID：{}，错误信息：{}", 
                    taskModel.getSshkeyUuid(), e.getMessage(), e);
            return fail("SaveOperationResultFailed", "保存操作结果失败：" + e.getMessage());
        }
    }

    @Override
    public void processWhenFail(ContainerTaskModel taskModel) {
        log.error("创建容器SSH密钥任务失败，SSH密钥名称：{}，SSH密钥UUID：{}",
                taskModel.getSshkeyName(), taskModel.getSshkeyUuid());
        // 这里可以进行失败后的清理工作
    }

    @Override
    public String taskGroupName() {
        return AsyncTaskGroups.CONTAINER_TASK_GROUP;
    }

    @Override
    public String taskName() {
        return AsyncTaskName.CREATE_CONTAINER_SSHKEY.getCode();
    }
}
