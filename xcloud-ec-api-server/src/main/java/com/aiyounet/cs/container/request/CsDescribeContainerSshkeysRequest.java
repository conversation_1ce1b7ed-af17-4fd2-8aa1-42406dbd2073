package com.aiyounet.cs.container.request;

import com.aiyounet.cs.common.request.CsPageQueryBeanRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 查询容器SSH密钥列表请求
 * 用于容器云平台查询容器SSH密钥列表的Web API请求类，支持分页和条件过滤
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询容器SSH密钥列表请求")
public class CsDescribeContainerSshkeysRequest extends CsPageQueryBeanRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "SSH密钥UUID", example = "sshkey-uuid-123")
    private String uuid;
    
    @ApiModelProperty(value = "SSH密钥名称", example = "my-container-key")
    private String name;
}
