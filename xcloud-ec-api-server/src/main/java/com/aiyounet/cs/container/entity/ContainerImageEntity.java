package com.aiyounet.cs.container.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 容器镜像实体类
 * 对应数据库表 CONTAINER_IMAGE，存储容器镜像的基本信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Entity
@Table(name = "CONTAINER_IMAGE")
@Data
public class ContainerImageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Long id;

    /**
     * 镜像的唯一标识符（UUID格式）
     */
    @Column(name = "IMAGE_UUID", length = 36, nullable = false, unique = true)
    private String imageUuid;

    /**
     * 镜像名称
     */
    @Column(name = "IMAGE_NAME", length = 255, nullable = false)
    private String imageName;

    /**
     * 创建者名称/昵称
     */
    @Column(name = "AUTHOR_NAME", length = 100, nullable = false)
    private String authorName;

    /**
     * 创建者用户ID（用于私有镜像查询）
     */
    @Column(name = "AUTHOR_USER_ID")
    private Long authorUserId;

    /**
     * 镜像封面图片的URL地址
     */
    @Column(name = "COVER_URL", length = 500, nullable = false)
    private String coverUrl;

    /**
     * 镜像的详细描述信息
     */
    @Column(name = "INTRODUCTION", length = 1000, nullable = false)
    private String introduction;

    /**
     * 镜像创建时间
     */
    @Column(name = "CREATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 镜像更新时间
     */
    @Column(name = "UPDATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updateTime;

    /**
     * 镜像大小，单位字节
     */
    @Column(name = "IMAGE_SIZE")
    private Long imageSize;

    /**
     * 镜像价格（放大一亿倍存储，避免浮点数运算）
     */
    @Column(name = "PRICE", nullable = false)
    private Integer price = 0;

    /**
     * 创建者/镜像是否经过认证
     */
    @Column(name = "CERTIFIED_CREATOR", nullable = false)
    private Boolean certifiedCreator = false;

    /**
     * 镜像类型：base-基础镜像，community-社区镜像
     */
    @Column(name = "IMAGE_TYPE", length = 20, nullable = false)
    private String imageType = "community";

    /**
     * 镜像状态：active-可用，inactive-不可用，deleted-已删除
     */
    @Column(name = "IMAGE_STATUS", length = 20, nullable = false)
    private String imageStatus = "active";

    /**
     * 是否为私有镜像
     */
    @Column(name = "IS_PRIVATE", nullable = false)
    private Boolean isPrivate = false;

    /**
     * 镜像的详细说明文档，使用Markdown格式
     */
    @Column(name = "MARKDOWN_DOC", columnDefinition = "TEXT")
    private String markdownDoc;

    /**
     * 镜像下载次数
     */
    @Column(name = "DOWNLOAD_COUNT", nullable = false)
    private Integer downloadCount = 0;

    /**
     * 镜像评分（1-5分）
     */
    @Column(name = "RATING")
    private Integer rating;

    /**
     * 镜像版本号
     */
    @Column(name = "VERSION", length = 50)
    private String version;

    /**
     * 镜像仓库地址
     */
    @Column(name = "REPOSITORY_URL", length = 500)
    private String repositoryUrl;

    /**
     * 删除时间（软删除）
     */
    @Column(name = "DELETE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date deleteTime;
}
