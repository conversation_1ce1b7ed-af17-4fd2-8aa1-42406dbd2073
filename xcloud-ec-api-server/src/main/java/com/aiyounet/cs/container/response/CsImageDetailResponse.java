package com.aiyounet.cs.container.response;

import com.aiya.platform.web.WebResponse;
import com.aiyounet.cs.container.model.ImageInfoDetail;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 镜像详情响应
 * 用于返回容器镜像详情查询结果的Web API响应类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("镜像详情响应")
public class CsImageDetailResponse extends WebResponse {

    @ApiModelProperty(value = "镜像详情数据", required = true)
    private ImageInfoDetail data;

    @ApiModelProperty(value = "请求是否成功", required = true, example = "true")
    private Boolean success;

    public CsImageDetailResponse() {
        this.success = true;
    }

    public CsImageDetailResponse(ImageInfoDetail data) {
        this();
        this.data = data;
        super.setData(data);
    }
}
