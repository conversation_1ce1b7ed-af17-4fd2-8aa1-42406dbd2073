package com.aiyounet.cs.container.model;

import lombok.Data;
import java.util.Date;

/**
 * 容器网卡信息
 * 用于容器云平台Web API的网卡数据传输，包含网卡的网络配置信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class CsContainerNicInfo {
    
    /**
     * 网卡ID
     */
    private Integer id;
    
    /**
     * 网卡UUID
     */
    private String uuid;
    
    /**
     * 网卡名称
     */
    private String name;
    
    /**
     * 网卡描述
     */
    private String description;
    
    /**
     * IP地址
     */
    private String ip;
    
    /**
     * MAC地址
     */
    private String mac;
    
    /**
     * 网关地址
     */
    private String gateway;
    
    /**
     * DNS地址
     */
    private String dns;
    
    /**
     * 设备编号
     */
    private String deviceNo;
    
    /**
     * 容器UUID
     */
    private String dockerUuid;
    
    /**
     * VLAN UUID
     */
    private String vlanUuid;
    
    /**
     * VNI标识
     */
    private Integer vni;
    
    /**
     * 可用区UUID
     */
    private String zoneUuid;
    
    /**
     * 地区ID
     */
    private Integer regionId;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 网卡状态
     */
    private Integer state;
    
    /**
     * 是否内部网络
     */
    private Boolean isInternal;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 创建者
     */
    private String creator;
}
