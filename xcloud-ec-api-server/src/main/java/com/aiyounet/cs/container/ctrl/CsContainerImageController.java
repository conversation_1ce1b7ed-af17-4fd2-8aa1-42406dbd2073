package com.aiyounet.cs.container.ctrl;

import com.aiyounet.cs.container.model.ImageInfoDetail;
import com.aiyounet.cs.container.model.ImageListData;
import com.aiyounet.cs.container.model.ImageType;
import com.aiyounet.cs.container.response.CsImageDetailResponse;
import com.aiyounet.cs.container.response.CsImageListResponse;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Pattern;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 容器镜像管理控制器
 * 提供容器镜像管理的Web API接口
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@RestController
@RequestMapping("/container")
@Api(tags = "容器镜像管理", description = "容器镜像的管理和搜索功能")
public class CsContainerImageController {

    /**
     * 获取镜像列表
     */
    @GetMapping("/images")
    @ApiOperation(value = "获取镜像列表",
                  notes = "获取可用的容器镜像列表，包含镜像的详细信息、价格、标签等",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功", response = CsImageListResponse.class),
        @ApiResponse(code = 422, message = "参数验证错误")
    })
    public CsImageListResponse getImageList(
            @ApiParam(value = "搜索关键词，支持模糊匹配镜像名称或描述", required = false)
            @RequestParam(value = "keyword", required = false, defaultValue = "") String keyword,
            @ApiParam(value = "镜像类型", required = false)
            @RequestParam(value = "type", required = false) ImageType type) {
        
        log.info("接收获取镜像列表请求：keyword={}, type={}", keyword, type);
        
        // 创建示例数据
        ImageInfoDetail image1 = new ImageInfoDetail();
        image1.setId("4e3b50de-7ec2-4c1e-b989-8023a77fa962");
        image1.setImageName("ComfyUI_v0.3.43");
        image1.setAuthorName("搬运工");
        image1.setCoverUrl("https://static.xiangongyun.com/container_image_cover/comfyui.png");
        image1.setIntroduction("ComfyUI：强大的AI图像生成工具，支持节点式编程");
        image1.setCreateTime("2025-01-24T10:30:00Z");
        image1.setSize(22265696436L);
        image1.setPrice(20000000);
        image1.setCertifiedCreator(true);
        image1.setTags(Arrays.asList("ComfyUI", "AI生成", "图像处理"));
        image1.setMarkdown("# ComfyUI 镜像使用说明\n\n## 功能特性\n- 已安装常用节点\n- 支持AI绘画\n\n## 使用方法\n1. 启动容器\n2. 访问Web界面\n3. 开始创作");

        ImageInfoDetail image2 = new ImageInfoDetail();
        image2.setId("5f4c61ef-8fd3-5d2f-c09a-9134b88gb073");
        image2.setImageName("JoyVASA数字人说话");
        image2.setAuthorName("仙宫科哥");
        image2.setCoverUrl("https://static.xiangongyun.com/container_image_cover/joyvasa.png");
        image2.setIntroduction("JoyVASA：基于扩散的音频驱动面部动力学和头部运动生成的肖像和动物图像动画");
        image2.setCreateTime("2025-01-23T15:20:00Z");
        image2.setSize(18456789123L);
        image2.setPrice(0);
        image2.setCertifiedCreator(true);
        image2.setTags(Arrays.asList("AI", "数字人", "语音合成"));
        image2.setMarkdown("# JoyVASA 数字人说话镜像\n\n## 简介\n基于最新的扩散模型技术，实现高质量的数字人语音驱动动画。");

        List<ImageInfoDetail> imageList = new ArrayList<>();
        imageList.add(image1);
        imageList.add(image2);

        ImageListData imageListData = new ImageListData();
        imageListData.setList(imageList);
        imageListData.setTags(Arrays.asList("AI", "ComfyUI", "图像处理", "数字人", "语音合成", "AI生成"));

        return new CsImageListResponse(imageListData);
    }

    /**
     * 获取镜像详情
     */
    @GetMapping("/images/{imageId}")
    @ApiOperation(value = "获取镜像详情",
                  notes = "根据镜像ID获取镜像的详细信息，包含完整的markdown格式说明文档",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功", response = CsImageDetailResponse.class),
        @ApiResponse(code = 422, message = "参数验证错误")
    })
    public CsImageDetailResponse getImageDetail(
            @ApiParam(value = "镜像ID", required = true)
            @PathVariable("imageId") 
            @Pattern(regexp = "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$", 
                     message = "镜像ID必须是有效的UUID格式") 
            String imageId) {
        
        log.info("接收获取镜像详情请求：imageId={}", imageId);
        
        // 创建示例数据
        ImageInfoDetail imageDetail = new ImageInfoDetail();
        imageDetail.setId(imageId);
        imageDetail.setImageName("ComfyUI_v0.3.43");
        imageDetail.setAuthorName("搬运工");
        imageDetail.setCoverUrl("https://static.xiangongyun.com/container_image_cover/comfyui.png");
        imageDetail.setIntroduction("ComfyUI：强大的AI图像生成工具，支持节点式编程，提供丰富的AI模型和插件生态");
        imageDetail.setCreateTime("2025-01-24T10:30:00Z");
        imageDetail.setSize(22265696436L);
        imageDetail.setPrice(20000000);
        imageDetail.setCertifiedCreator(true);
        imageDetail.setTags(Arrays.asList("ComfyUI", "AI生成", "图像处理", "Stable Diffusion"));
        imageDetail.setMarkdown("# ComfyUI 镜像使用说明\n\n## 功能特性\n- 已安装常用节点和模型\n- 支持Stable Diffusion、ControlNet等\n- 预配置常用工作流\n\n## 使用方法\n1. 启动容器实例\n2. 访问Web界面 http://localhost:8188\n3. 导入工作流开始创作\n\n## 注意事项\n- 建议使用GPU加速\n- 首次运行需要下载模型文件");

        return new CsImageDetailResponse(imageDetail);
    }

    /**
     * 获取私有镜像列表
     */
    @GetMapping("/own/images")
    @ApiOperation(value = "获取私有镜像列表",
                  notes = "获取用户私有的容器镜像列表，包含镜像的详细信息、价格、标签等，需要传入任意cookie",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功", response = CsImageListResponse.class)
    })
    public CsImageListResponse getPrivateImageList() {
        
        log.info("接收获取私有镜像列表请求");
        
        // 创建示例私有镜像数据
        ImageInfoDetail privateImage = new ImageInfoDetail();
        privateImage.setId("6a5d72f0-9ge4-6e3g-d10b-0245c99hc184");
        privateImage.setImageName("我的自定义ComfyUI");
        privateImage.setAuthorName("当前用户");
        privateImage.setCoverUrl("https://static.xiangongyun.com/container_image_cover/custom.png");
        privateImage.setIntroduction("基于ComfyUI定制的私有镜像，包含个人工作流和模型");
        privateImage.setCreateTime("2025-01-20T14:15:00Z");
        privateImage.setSize(25678901234L);
        privateImage.setPrice(0);
        privateImage.setCertifiedCreator(false);
        privateImage.setTags(Arrays.asList("私有", "ComfyUI", "自定义"));
        privateImage.setMarkdown("# 我的自定义ComfyUI镜像\n\n## 特色功能\n- 预装个人常用模型\n- 自定义工作流模板\n- 优化的性能配置");

        List<ImageInfoDetail> privateImageList = new ArrayList<>();
        privateImageList.add(privateImage);

        ImageListData imageListData = new ImageListData();
        imageListData.setList(privateImageList);
        imageListData.setTags(Arrays.asList("私有", "ComfyUI", "自定义", "个人"));

        return new CsImageListResponse(imageListData);
    }
}
