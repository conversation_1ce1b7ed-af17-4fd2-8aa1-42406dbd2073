package com.aiyounet.cs.container.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 镜像详情信息模型
 * 包含镜像的完整信息，包括基本信息、价格、标签和详细说明文档
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@ApiModel("镜像详情信息模型")
public class ImageInfoDetail {

    @ApiModelProperty(value = "镜像的唯一标识符", required = true, example = "4e3b50de-7ec2-4c1e-b989-8023a77fa962")
    private String id;

    @ApiModelProperty(value = "镜像名称", required = true, example = "ComfyUI_v0.3.43")
    private String imageName;

    @ApiModelProperty(value = "创建者名称/昵称", required = true, example = "搬运工")
    private String authorName;

    @ApiModelProperty(value = "镜像封面图片的 URL 地址", required = true, example = "https://static.xiangongyun.com/container_image_cover/example.png")
    private String coverUrl;

    @ApiModelProperty(value = "镜像的详细描述信息，说明镜像的功能和用途", required = true, example = "ComfyUI：强大的AI图像生成工具")
    private String introduction;

    @ApiModelProperty(value = "镜像创建时间（ISO 8601 格式）", example = "2025-01-24T10:30:00Z")
    private String createTime;

    @ApiModelProperty(value = "镜像大小，单位字节", example = "22265696436")
    private Long size;

    @ApiModelProperty(value = "镜像价格。注意：实际存储时放大一亿倍避免浮点数运算", example = "20000000")
    private Integer price = 0;

    @ApiModelProperty(value = "创建者/镜像是否经过认证", example = "true")
    private Boolean certifiedCreator = false;

    @ApiModelProperty(value = "镜像标签列表，用于分类和搜索", example = "[\"ComfyUI\", \"AI生成\", \"图像处理\"]")
    private List<String> tags;

    @ApiModelProperty(value = "镜像的详细说明文档，使用Markdown格式", example = "# ComfyUI 镜像使用说明\\n\\n## 功能特性\\n- 已安装常用节点\\n- 支持AI绘画")
    private String markdown;
}
