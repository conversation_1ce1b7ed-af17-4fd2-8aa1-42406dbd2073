package com.aiyounet.cs.container.model;

import lombok.Data;
import java.util.Date;

/**
 * 容器存储卷信息
 * 用于容器云平台Web API的存储卷数据传输，包含存储卷的配置信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class CsContainerVolInfo {
    
    /**
     * 存储卷ID
     */
    private Integer id;
    
    /**
     * 存储卷UUID
     */
    private String uuid;
    
    /**
     * 存储卷描述
     */
    private String description;
    
    /**
     * 挂载路径
     */
    private String dstPath;
    
    /**
     * 宿主机路径
     */
    private String host;
    
    /**
     * 地区ID
     */
    private Integer regionId;
    
    /**
     * 存储卷大小
     */
    private Integer size;
    
    /**
     * 存储卷状态
     */
    private Integer state;
    
    /**
     * 存储卷类型
     */
    private Integer volType;
    
    /**
     * 容器UUID
     */
    private String dockerUuid;
    
    /**
     * 设备编号
     */
    private String deviceNo;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 创建者
     */
    private String creator;
    
    /**
     * 用户ID
     */
    private Integer userId;
}
