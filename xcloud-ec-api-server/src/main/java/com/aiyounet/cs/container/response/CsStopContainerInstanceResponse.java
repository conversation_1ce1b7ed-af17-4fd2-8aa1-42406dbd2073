package com.aiyounet.cs.container.response;

import com.aiya.platform.web.WebResponse;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 停止容器实例响应
 * 用于容器云平台停止容器实例的Web API响应类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("停止容器实例响应")
public class CsStopContainerInstanceResponse extends WebResponse {
    
    @ApiModelProperty(value = "请求ID", example = "req-123456")
    private String requestId;
    
    @ApiModelProperty(value = "主机ID", example = "host-123456")
    private String hostId;
    
    @ApiModelProperty(value = "任务UUID", example = "task-uuid-123")
    private String taskUuid;
}
