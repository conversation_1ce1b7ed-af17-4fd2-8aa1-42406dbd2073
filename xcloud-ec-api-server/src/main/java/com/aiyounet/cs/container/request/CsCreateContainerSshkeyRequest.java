package com.aiyounet.cs.container.request;

import com.aiya.platform.web.AbstractPostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 创建容器SSH密钥请求
 * 用于容器云平台创建容器SSH密钥的Web API请求类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("创建容器SSH密钥请求")
public class CsCreateContainerSshkeyRequest extends AbstractPostRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "SSH密钥描述", example = "容器SSH密钥")
    private String description;
    
    @ApiModelProperty(value = "SSH密钥名称", required = true, example = "my-container-key")
    @NotBlank(message = "SSH密钥名称不能为空")
    private String name;
}
