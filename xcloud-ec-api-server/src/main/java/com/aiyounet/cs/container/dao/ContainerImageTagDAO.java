package com.aiyounet.cs.container.dao;

import com.aiyounet.cs.container.entity.ContainerImageTagEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 容器镜像标签数据访问接口
 * 提供容器镜像标签的数据库操作方法
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public interface ContainerImageTagDAO {

    /**
     * 根据镜像ID查询标签列表
     *
     * @param imageId 镜像ID
     * @return 标签列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE_TAG WHERE IMAGE_ID = #{imageId} ORDER BY TAG_WEIGHT DESC, TAG_NAME ASC")
    List<ContainerImageTagEntity> selectTagsByImageId(@Param("imageId") Long imageId);

    /**
     * 根据镜像ID列表批量查询标签
     *
     * @param imageIds 镜像ID列表
     * @return 标签列表
     */
    List<ContainerImageTagEntity> selectTagsByImageIds(@Param("imageIds") List<Long> imageIds);

    /**
     * 查询所有可用标签
     *
     * @return 标签名称列表
     */
    @Select("SELECT DISTINCT TAG_NAME FROM CONTAINER_IMAGE_TAG " +
            "WHERE IMAGE_ID IN (SELECT ID FROM CONTAINER_IMAGE WHERE IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL) " +
            "ORDER BY TAG_NAME ASC")
    List<String> selectAllAvailableTags();

    /**
     * 查询热门标签
     *
     * @param limit 限制数量
     * @return 标签名称列表
     */
    @Select("SELECT TAG_NAME, COUNT(*) as tag_count FROM CONTAINER_IMAGE_TAG " +
            "WHERE IMAGE_ID IN (SELECT ID FROM CONTAINER_IMAGE WHERE IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL) " +
            "GROUP BY TAG_NAME ORDER BY tag_count DESC, TAG_NAME ASC LIMIT #{limit}")
    List<String> selectPopularTags(@Param("limit") Integer limit);

    /**
     * 根据标签名称查询关联的镜像ID列表
     *
     * @param tagName 标签名称
     * @return 镜像ID列表
     */
    @Select("SELECT DISTINCT IMAGE_ID FROM CONTAINER_IMAGE_TAG WHERE TAG_NAME = #{tagName}")
    List<Long> selectImageIdsByTagName(@Param("tagName") String tagName);

    /**
     * 根据标签名称列表查询关联的镜像ID列表
     *
     * @param tagNames 标签名称列表
     * @return 镜像ID列表
     */
    List<Long> selectImageIdsByTagNames(@Param("tagNames") List<String> tagNames);

    /**
     * 插入镜像标签关联
     *
     * @param imageTag 镜像标签实体
     * @return 影响行数
     */
    Integer insertImageTag(ContainerImageTagEntity imageTag);

    /**
     * 批量插入镜像标签关联
     *
     * @param imageTags 镜像标签实体列表
     * @return 影响行数
     */
    Integer batchInsertImageTags(@Param("imageTags") List<ContainerImageTagEntity> imageTags);

    /**
     * 删除镜像的所有标签
     *
     * @param imageId 镜像ID
     * @return 影响行数
     */
    @Select("DELETE FROM CONTAINER_IMAGE_TAG WHERE IMAGE_ID = #{imageId}")
    Integer deleteTagsByImageId(@Param("imageId") Long imageId);

    /**
     * 删除指定镜像的指定标签
     *
     * @param imageId 镜像ID
     * @param tagName 标签名称
     * @return 影响行数
     */
    @Select("DELETE FROM CONTAINER_IMAGE_TAG WHERE IMAGE_ID = #{imageId} AND TAG_NAME = #{tagName}")
    Integer deleteImageTag(@Param("imageId") Long imageId, @Param("tagName") String tagName);

    /**
     * 统计标签使用次数
     *
     * @param tagName 标签名称
     * @return 使用次数
     */
    @Select("SELECT COUNT(*) FROM CONTAINER_IMAGE_TAG WHERE TAG_NAME = #{tagName}")
    Integer countTagUsage(@Param("tagName") String tagName);

    /**
     * 查询系统标签列表
     *
     * @return 系统标签列表
     */
    @Select("SELECT DISTINCT TAG_NAME FROM CONTAINER_IMAGE_TAG WHERE TAG_TYPE = 'system' ORDER BY TAG_NAME ASC")
    List<String> selectSystemTags();

    /**
     * 查询用户标签列表
     *
     * @return 用户标签列表
     */
    @Select("SELECT DISTINCT TAG_NAME FROM CONTAINER_IMAGE_TAG WHERE TAG_TYPE = 'user' ORDER BY TAG_NAME ASC")
    List<String> selectUserTags();
}
