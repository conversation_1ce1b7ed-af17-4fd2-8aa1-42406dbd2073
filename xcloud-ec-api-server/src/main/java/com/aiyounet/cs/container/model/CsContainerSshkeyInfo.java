package com.aiyounet.cs.container.model;

import lombok.Data;
import java.util.Date;

/**
 * 容器SSH密钥信息
 * 用于容器云平台Web API的SSH密钥数据传输，包含密钥的基本信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class CsContainerSshkeyInfo {
    
    /**
     * SSH密钥UUID
     */
    private String uuid;
    
    /**
     * 密钥名称
     */
    private String name;
    
    /**
     * 密钥描述
     */
    private String description;
    
    /**
     * 密钥内容
     */
    private String content;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 创建者
     */
    private String creator;
}
