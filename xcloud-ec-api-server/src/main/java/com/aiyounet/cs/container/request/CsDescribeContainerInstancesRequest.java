package com.aiyounet.cs.container.request;

import com.aiyounet.cs.common.request.CsPageQueryBeanRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 查询容器实例列表请求
 * 用于容器云平台查询容器实例列表的Web API请求类，支持分页和条件过滤
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("查询容器实例列表请求")
public class CsDescribeContainerInstancesRequest extends CsPageQueryBeanRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "规格UUID", example = "flavor-uuid-123")
    private String flavorUuid;
    
    @ApiModelProperty(value = "容器UUID", example = "container-uuid-123")
    private String uuid;
    
    @ApiModelProperty(value = "容器状态", example = "1", notes = "0-处理中, 1-正常, 2-关机")
    private Integer state;
    
    @ApiModelProperty(value = "IP地址", example = "*************")
    private String ip;
    
    @ApiModelProperty(value = "用户ID", example = "12345")
    private Integer userId;
}
