package com.aiyounet.cs.container.response;

import com.aiya.platform.web.WebResponse;
import com.aiyounet.cs.container.model.ImageListData;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 镜像列表响应
 * 用于返回容器镜像列表查询结果的Web API响应类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("镜像列表响应")
public class CsImageListResponse extends WebResponse {

    @ApiModelProperty(value = "镜像列表数据", required = true)
    private ImageListData data;

    @ApiModelProperty(value = "请求是否成功", required = true, example = "true")
    private Boolean success;

    public CsImageListResponse() {
        this.success = true;
    }

    public CsImageListResponse(ImageListData data) {
        this();
        this.data = data;
        super.setData(data);
    }
}
