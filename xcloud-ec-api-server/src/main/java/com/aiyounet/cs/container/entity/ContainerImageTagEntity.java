package com.aiyounet.cs.container.entity;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

/**
 * 容器镜像标签实体类
 * 对应数据库表 CONTAINER_IMAGE_TAG，存储镜像与标签的关联关系
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Entity
@Table(name = "CONTAINER_IMAGE_TAG")
@Data
public class ContainerImageTagEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Long id;

    /**
     * 镜像ID
     */
    @Column(name = "IMAGE_ID", nullable = false)
    private Long imageId;

    /**
     * 标签名称
     */
    @Column(name = "TAG_NAME", length = 50, nullable = false)
    private String tagName;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    private Date createTime;

    /**
     * 标签类型：system-系统标签，user-用户标签
     */
    @Column(name = "TAG_TYPE", length = 20, nullable = false)
    private String tagType = "user";

    /**
     * 标签权重（用于排序）
     */
    @Column(name = "TAG_WEIGHT", nullable = false)
    private Integer tagWeight = 0;
}
