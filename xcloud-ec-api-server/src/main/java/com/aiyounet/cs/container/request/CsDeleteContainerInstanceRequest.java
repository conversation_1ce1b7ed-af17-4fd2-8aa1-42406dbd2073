package com.aiyounet.cs.container.request;

import com.aiya.platform.web.AbstractPostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 删除容器实例请求
 * 用于容器云平台删除容器实例的Web API请求类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("删除容器实例请求")
public class CsDeleteContainerInstanceRequest extends AbstractPostRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "容器UUID", required = true, example = "container-uuid-123")
    @NotBlank(message = "容器UUID不能为空")
    private String uuid;
}
