package com.aiyounet.cs.container.ctrl;

import com.aiyounet.cs.common.response.CsPageDataResponse;
import com.aiyounet.cs.container.service.CsContainerService;
import com.aiyounet.cs.container.request.*;
import com.aiyounet.cs.container.response.*;
import com.aiyounet.cs.container.model.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 容器云管理控制器
 * 提供容器云平台的Web API接口，包括容器实例、网卡、VLAN、SSH密钥等管理功能
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@RestController
@RequestMapping("/container")
@Api(tags = "容器云管理", description = "提供容器云平台的完整管理功能，包括容器实例、网络、存储、密钥等操作")
public class CsContainerController {

    @Autowired
    private CsContainerService csContainerService;

    /**
     * 创建容器实例
     */
    @PostMapping("/machines")
    @ApiOperation(value = "创建容器实例",
                  notes = "根据指定的配置参数创建新的容器实例，包括规格、镜像、网络、存储等配置",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功", response = CsCreateContainerInstanceResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsCreateContainerInstanceResponse createContainerInstance(
            @Valid @RequestBody CsCreateContainerInstanceRequest request) {
        log.info("接收创建容器实例请求：{}", request);
        return csContainerService.createContainerInstance(request);
    }

    /**
     * 查询容器实例列表
     */
    @GetMapping("/machines")
    @ApiOperation(value = "查询容器实例列表",
                  notes = "支持分页查询和多种条件过滤，返回容器实例的详细信息列表",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsPageDataResponse<CsContainerDetailInfo> describeContainerInstances(
            @Valid @RequestBody CsDescribeContainerInstancesRequest request) {
        log.info("接收查询容器实例列表请求：{}", request);
        return csContainerService.describeContainerInstances(request);
    }

    /**
     * 删除容器实例
     */
    @DeleteMapping("/machines")
    @ApiOperation(value = "删除容器实例",
                  notes = "根据容器UUID删除指定的容器实例，操作不可逆",
                  httpMethod = "DELETE")
    @ApiResponses({
        @ApiResponse(code = 200, message = "删除成功", response = CsDeleteContainerInstanceResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 404, message = "容器实例不存在"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsDeleteContainerInstanceResponse deleteContainerInstance(
            @Valid @RequestBody CsDeleteContainerInstanceRequest request) {
        log.info("接收删除容器实例请求：{}", request);
        return csContainerService.deleteContainerInstance(request);
    }

    /**
     * 启动容器实例
     */
    @PostMapping("/machines/start")
    @ApiOperation(value = "启动容器实例",
                  notes = "启动指定的容器实例，使其进入运行状态",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "启动成功", response = CsStartContainerInstanceResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 404, message = "容器实例不存在"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsStartContainerInstanceResponse startContainerInstance(
            @Valid @RequestBody CsStartContainerInstanceRequest request) {
        log.info("接收启动容器实例请求：{}", request);
        return csContainerService.startContainerInstance(request);
    }

    /**
     * 停止容器实例
     */
    @PostMapping("/machines/stop")
    @ApiOperation(value = "停止容器实例",
                  notes = "停止指定的容器实例，使其进入停止状态",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "停止成功", response = CsStopContainerInstanceResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 404, message = "容器实例不存在"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsStopContainerInstanceResponse stopContainerInstance(
            @Valid @RequestBody CsStopContainerInstanceRequest request) {
        log.info("接收停止容器实例请求：{}", request);
        return csContainerService.stopContainerInstance(request);
    }

    /**
     * 创建容器网卡
     */
    @PostMapping("/nics")
    @ApiOperation(value = "创建容器网卡",
                  notes = "为容器创建新的网络接口，配置IP地址、网关等网络参数",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功", response = CsCreateContainerNicResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsCreateContainerNicResponse createContainerNic(
            @Valid @RequestBody CsCreateContainerNicRequest request) {
        log.info("接收创建容器网卡请求：{}", request);
        return csContainerService.createContainerNic(request);
    }

    /**
     * 创建容器VLAN
     */
    @PostMapping("/vlans")
    @ApiOperation(value = "创建容器VLAN",
                  notes = "创建新的虚拟局域网，用于容器网络隔离和管理",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功", response = CsCreateContainerVlanResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsCreateContainerVlanResponse createContainerVlan(
            @Valid @RequestBody CsCreateContainerVlanRequest request) {
        log.info("接收创建容器VLAN请求：{}", request);
        return csContainerService.createContainerVlan(request);
    }

    /**
     * 查询容器VLAN列表
     */
    @GetMapping("/vlans/list")
    @ApiOperation(value = "查询容器VLAN列表",
                  notes = "支持分页查询和条件过滤，返回VLAN配置信息列表",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsPageDataResponse<CsContainerVlanInfo> describeContainerVlans(
            @Valid @RequestBody CsDescribeContainerVlansRequest request) {
        log.info("接收查询容器VLAN列表请求：{}", request);
        return csContainerService.describeContainerVlans(request);
    }

    /**
     * 创建容器SSH密钥
     */
    @PostMapping("/sshkeys")
    @ApiOperation(value = "创建容器SSH密钥",
                  notes = "创建新的SSH密钥对，用于容器的安全登录认证",
                  httpMethod = "POST")
    @ApiResponses({
        @ApiResponse(code = 200, message = "创建成功", response = CsCreateContainerSshkeyResponse.class),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsCreateContainerSshkeyResponse createContainerSshkey(
            @Valid @RequestBody CsCreateContainerSshkeyRequest request) {
        log.info("接收创建容器SSH密钥请求：{}", request);
        return csContainerService.createContainerSshkey(request);
    }

    /**
     * 查询容器SSH密钥列表
     */
    @GetMapping("/sshkeys/list")
    @ApiOperation(value = "查询容器SSH密钥列表",
                  notes = "支持分页查询和条件过滤，返回SSH密钥配置信息列表",
                  httpMethod = "GET")
    @ApiResponses({
        @ApiResponse(code = 200, message = "查询成功"),
        @ApiResponse(code = 400, message = "请求参数错误"),
        @ApiResponse(code = 500, message = "服务器内部错误")
    })
    public CsPageDataResponse<CsContainerSshkeyInfo> describeContainerSshkeys(
            @Valid @RequestBody CsDescribeContainerSshkeysRequest request) {
        log.info("接收查询容器SSH密钥列表请求：{}", request);
        return csContainerService.describeContainerSshkeys(request);
    }
}
