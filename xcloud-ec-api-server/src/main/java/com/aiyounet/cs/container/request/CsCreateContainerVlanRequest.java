package com.aiyounet.cs.container.request;

import com.aiya.platform.web.AbstractPostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建容器VLAN请求
 * 用于容器云平台创建容器VLAN的Web API请求类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("创建容器VLAN请求")
public class CsCreateContainerVlanRequest extends AbstractPostRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "CIDR网络配置", example = "***********/24")
    private String cidr;
    
    @ApiModelProperty(value = "VLAN描述", example = "容器VLAN网络")
    private String description;
    
    @ApiModelProperty(value = "DNS服务器地址", example = "*******")
    private String dns;
    
    @ApiModelProperty(value = "排除地址范围", example = "***********-***********0")
    private String exclude;
    
    @ApiModelProperty(value = "网关地址", example = "***********")
    private String gateway;
    
    @ApiModelProperty(value = "是否内部网络", required = true, example = "true", notes = "true=内部网络, false=公网")
    @NotNull(message = "是否内部网络不能为空")
    private Boolean isInternal;
    
    @ApiModelProperty(value = "网络标识VNI", example = "1000")
    private Integer vni;
    
    @ApiModelProperty(value = "可用区UUID", example = "zone-uuid-123")
    private String zoneUuid;
}
