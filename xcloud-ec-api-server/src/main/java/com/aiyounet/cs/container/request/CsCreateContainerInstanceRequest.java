package com.aiyounet.cs.container.request;

import com.aiya.platform.web.AbstractPostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建容器实例请求
 * 用于容器云平台创建容器实例的Web API请求类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("创建容器实例请求")
public class CsCreateContainerInstanceRequest extends AbstractPostRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "容器网络类型", example = "bridge")
    private String containerNetType;
    
    @ApiModelProperty(value = "容器描述", example = "测试容器实例")
    private String description;
    
    @ApiModelProperty(value = "规格UUID", required = true, example = "flavor-uuid-123")
    @NotBlank(message = "规格UUID不能为空")
    private String flavorUuid;
    
    @ApiModelProperty(value = "GPU数量", example = "1")
    private Integer gpuNum;
    
    @ApiModelProperty(value = "宿主机SN", example = "host-sn-123")
    private String hostSn;
    
    @ApiModelProperty(value = "镜像完整路径", required = true, example = "/path/to/image")
    @NotBlank(message = "镜像路径不能为空")
    private String imagePath;
    
    @ApiModelProperty(value = "SSH密钥UUID", example = "key-uuid-123")
    private String keyUuid;
    
    @ApiModelProperty(value = "网卡配置JSON", example = "[{\"Gateway\": \"***********\", \"Ip\": \"*************\", \"VlanUuid\": \"vlan-uuid\"}]")
    private String nics;
    
    @ApiModelProperty(value = "容器密码", example = "password123")
    private String pswd;
    
    @ApiModelProperty(value = "运行参数JSON", example = "[\"-p\", \"8080:80\"]")
    private String runArgs;
    
    @ApiModelProperty(value = "存储卷配置JSON", example = "[{\"DstPath\": \"/data\", \"Size\": 100, \"VolType\": 1}]")
    private String vols;
    
    @ApiModelProperty(value = "可用区UUID", required = true, example = "zone-uuid-123")
    @NotBlank(message = "可用区UUID不能为空")
    private String zoneUuid;
}
