package com.aiyounet.cs.container.request;

import com.aiya.platform.web.AbstractPostRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import org.hibernate.validator.constraints.NotBlank;

/**
 * 创建容器网卡请求
 * 用于容器云平台创建容器网卡的Web API请求类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("创建容器网卡请求")
public class CsCreateContainerNicRequest extends AbstractPostRequest {
    
    @ApiModelProperty(value = "地区代码", required = true, example = "cn-beijing")
    @NotBlank(message = "地区代码不能为空")
    private String regionCode;
    
    @ApiModelProperty(value = "网卡描述", example = "容器网卡")
    private String description;
    
    @ApiModelProperty(value = "DNS地址", example = "*******")
    private String dns;
    
    @ApiModelProperty(value = "挂载容器UUID", example = "container-uuid-123")
    private String dockerUuid;
    
    @ApiModelProperty(value = "网关地址", example = "***********")
    private String gateway;
    
    @ApiModelProperty(value = "IP地址", example = "*************")
    private String ip;
    
    @ApiModelProperty(value = "VLAN UUID", example = "vlan-uuid-123")
    private String vlanUuid;
    
    @ApiModelProperty(value = "可用区UUID", example = "zone-uuid-123")
    private String zoneUuid;
}
