package com.aiyounet.cs.container.model;

import lombok.Data;
import java.util.Date;

/**
 * 容器VLAN信息
 * 用于容器云平台Web API的VLAN数据传输，包含VLAN的网络配置信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class CsContainerVlanInfo {
    
    /**
     * VLAN ID
     */
    private Integer id;
    
    /**
     * VLAN UUID
     */
    private String uuid;
    
    /**
     * VLAN名称
     */
    private String name;
    
    /**
     * CIDR网络配置
     */
    private String cidr;
    
    /**
     * VLAN描述
     */
    private String description;
    
    /**
     * DNS服务器地址
     */
    private String dns;
    
    /**
     * 排除地址范围
     */
    private String exclude;
    
    /**
     * 网关地址
     */
    private String gateway;
    
    /**
     * 是否内部网络
     */
    private Boolean isInternal;
    
    /**
     * 网络标识
     */
    private Integer nid;
    
    /**
     * 地区ID
     */
    private Integer regionId;
    
    /**
     * VLAN状态
     */
    private Integer state;
    
    /**
     * 用户ID
     */
    private Integer userId;
    
    /**
     * VNI标识
     */
    private Integer vni;
    
    /**
     * 可用区UUID
     */
    private String zoneUuid;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 创建者
     */
    private String creator;
}
