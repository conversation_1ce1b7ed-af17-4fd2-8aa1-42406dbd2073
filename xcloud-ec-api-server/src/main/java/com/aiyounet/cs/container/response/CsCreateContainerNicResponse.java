package com.aiyounet.cs.container.response;

import com.aiya.platform.web.WebResponse;
import com.aiyounet.cs.container.model.CsContainerNicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建容器网卡响应
 * 用于容器云平台创建容器网卡的Web API响应类
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("创建容器网卡响应")
public class CsCreateContainerNicResponse extends WebResponse {
    
    @ApiModelProperty(value = "请求ID", example = "req-123456")
    private String requestId;
    
    @ApiModelProperty(value = "主机ID", example = "host-123456")
    private String hostId;

    @ApiModelProperty(value = "任务UUID", example = "task-uuid-123")
    private String taskUuid;

    @ApiModelProperty(value = "网卡信息")
    private CsContainerNicInfo info;
}
