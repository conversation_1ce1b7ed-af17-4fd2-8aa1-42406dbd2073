package com.aiyounet.cs.container.model;

import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * 容器实例详细信息
 * 用于容器云平台Web API的容器实例数据传输，包含容器的完整配置信息
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
public class CsContainerDetailInfo {
    
    /**
     * 容器UUID
     */
    private String uuid;
    
    /**
     * 容器描述
     */
    private String description;
    
    /**
     * 规格UUID
     */
    private String flavorUuid;
    
    /**
     * CPU核数
     */
    private Integer cpus;
    
    /**
     * 内存大小(MB)
     */
    private Integer mem;
    
    /**
     * GPU数量
     */
    private Integer gpuNum;
    
    /**
     * 宿主机SN
     */
    private String hostSn;
    
    /**
     * SSH密钥UUID
     */
    private String keyUuid;
    
    /**
     * 密码
     */
    private String pswd;
    
    /**
     * 地区ID
     */
    private Integer regionId;
    
    /**
     * 根磁盘大小
     */
    private Integer rootSize;
    
    /**
     * 运行参数
     */
    private List<String> runArgs;
    
    /**
     * 容器状态
     */
    private Integer state;
    
    /**
     * 可用区UUID
     */
    private String zoneUuid;
    
    /**
     * 容器网络类型
     */
    private String containerNetType;
    
    /**
     * 镜像路径
     */
    private String imagePath;
    
    /**
     * 创建时间
     */
    private Date createdAt;
    
    /**
     * 更新时间
     */
    private Date updatedAt;
    
    /**
     * 网卡列表
     */
    private List<CsContainerNicInfo> nics;
    
    /**
     * 存储卷列表
     */
    private List<CsContainerVolInfo> vols;
}
