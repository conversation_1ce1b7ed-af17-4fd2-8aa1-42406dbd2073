package com.aiyounet.cs.container.dao;

import com.aiyounet.cs.container.entity.ContainerImageEntity;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

/**
 * 容器镜像数据访问接口
 * 提供容器镜像的数据库操作方法
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public interface ContainerImageDAO {

    /**
     * 根据UUID查询镜像详情
     *
     * @param imageUuid 镜像UUID
     * @return 镜像实体
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE IMAGE_UUID = #{imageUuid} AND IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL")
    ContainerImageEntity selectByImageUuid(@Param("imageUuid") String imageUuid);

    /**
     * 分页查询镜像列表
     *
     * @param keyword   搜索关键词
     * @param imageType 镜像类型
     * @param isPrivate 是否私有镜像
     * @param authorUserId 作者用户ID（私有镜像查询时使用）
     * @param rowBounds 分页参数
     * @return 镜像列表
     */
    List<ContainerImageEntity> selectImageListByPage(
            @Param("keyword") String keyword,
            @Param("imageType") String imageType,
            @Param("isPrivate") Boolean isPrivate,
            @Param("authorUserId") Long authorUserId,
            RowBounds rowBounds);

    /**
     * 统计镜像数量
     *
     * @param keyword   搜索关键词
     * @param imageType 镜像类型
     * @param isPrivate 是否私有镜像
     * @param authorUserId 作者用户ID（私有镜像查询时使用）
     * @return 镜像总数
     */
    Integer countImageList(
            @Param("keyword") String keyword,
            @Param("imageType") String imageType,
            @Param("isPrivate") Boolean isPrivate,
            @Param("authorUserId") Long authorUserId);

    /**
     * 查询热门镜像列表
     *
     * @param limit 限制数量
     * @return 热门镜像列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE IMAGE_STATUS = 'active' AND IS_PRIVATE = 0 AND DELETE_TIME IS NULL " +
            "ORDER BY DOWNLOAD_COUNT DESC, RATING DESC, CREATE_TIME DESC LIMIT #{limit}")
    List<ContainerImageEntity> selectPopularImages(@Param("limit") Integer limit);

    /**
     * 查询最新镜像列表
     *
     * @param limit 限制数量
     * @return 最新镜像列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE IMAGE_STATUS = 'active' AND IS_PRIVATE = 0 AND DELETE_TIME IS NULL " +
            "ORDER BY CREATE_TIME DESC LIMIT #{limit}")
    List<ContainerImageEntity> selectLatestImages(@Param("limit") Integer limit);

    /**
     * 根据作者用户ID查询私有镜像列表
     *
     * @param authorUserId 作者用户ID
     * @param rowBounds    分页参数
     * @return 私有镜像列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE AUTHOR_USER_ID = #{authorUserId} AND IS_PRIVATE = 1 " +
            "AND IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL ORDER BY CREATE_TIME DESC")
    List<ContainerImageEntity> selectPrivateImagesByUserId(@Param("authorUserId") Long authorUserId, RowBounds rowBounds);

    /**
     * 统计用户私有镜像数量
     *
     * @param authorUserId 作者用户ID
     * @return 私有镜像数量
     */
    @Select("SELECT COUNT(*) FROM CONTAINER_IMAGE WHERE AUTHOR_USER_ID = #{authorUserId} AND IS_PRIVATE = 1 " +
            "AND IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL")
    Integer countPrivateImagesByUserId(@Param("authorUserId") Long authorUserId);

    /**
     * 根据标签查询镜像列表
     *
     * @param tagNames  标签名称列表
     * @param rowBounds 分页参数
     * @return 镜像列表
     */
    List<ContainerImageEntity> selectImagesByTags(@Param("tagNames") List<String> tagNames, RowBounds rowBounds);

    /**
     * 更新镜像下载次数
     *
     * @param imageId 镜像ID
     * @return 影响行数
     */
    @Select("UPDATE CONTAINER_IMAGE SET DOWNLOAD_COUNT = DOWNLOAD_COUNT + 1, UPDATE_TIME = NOW() WHERE ID = #{imageId}")
    Integer updateDownloadCount(@Param("imageId") Long imageId);

    /**
     * 根据镜像名称模糊查询
     *
     * @param imageName 镜像名称
     * @param rowBounds 分页参数
     * @return 镜像列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE IMAGE_NAME LIKE CONCAT('%', #{imageName}, '%') " +
            "AND IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL ORDER BY DOWNLOAD_COUNT DESC")
    List<ContainerImageEntity> selectImagesByNameLike(@Param("imageName") String imageName, RowBounds rowBounds);

    /**
     * 根据作者名称查询镜像列表
     *
     * @param authorName 作者名称
     * @param rowBounds  分页参数
     * @return 镜像列表
     */
    @Select("SELECT * FROM CONTAINER_IMAGE WHERE AUTHOR_NAME LIKE CONCAT('%', #{authorName}, '%') " +
            "AND IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL ORDER BY CREATE_TIME DESC")
    List<ContainerImageEntity> selectImagesByAuthorName(@Param("authorName") String authorName, RowBounds rowBounds);

    /**
     * 插入镜像记录
     *
     * @param image 镜像实体
     * @return 影响行数
     */
    Integer insertImage(ContainerImageEntity image);

    /**
     * 更新镜像信息
     *
     * @param image 镜像实体
     * @return 影响行数
     */
    Integer updateImage(ContainerImageEntity image);

    /**
     * 软删除镜像
     *
     * @param imageId 镜像ID
     * @return 影响行数
     */
    @Select("UPDATE CONTAINER_IMAGE SET IMAGE_STATUS = 'deleted', DELETE_TIME = NOW() WHERE ID = #{imageId}")
    Integer softDeleteImage(@Param("imageId") Long imageId);
}
