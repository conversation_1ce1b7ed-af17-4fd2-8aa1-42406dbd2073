package com.aiyounet.cs.container.service;

import com.aiyounet.cs.common.response.CsPageDataResponse;
import com.aiyounet.cs.container.request.*;
import com.aiyounet.cs.container.response.*;
import com.aiyounet.cs.container.model.*;

/**
 * 容器云业务服务接口
 * 用于容器云平台的Web API业务逻辑处理，提供容器实例、网卡、VLAN、SSH密钥等管理功能。
 * 处理业务逻辑转换和数据格式转换。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public interface CsContainerService {

    /**
     * 创建容器实例
     * 根据用户请求创建新的容器实例，包括规格、镜像、网络等配置
     *
     * @param request 创建容器实例请求参数
     * @return 创建容器实例响应结果
     */
    CsCreateContainerInstanceResponse createContainerInstance(CsCreateContainerInstanceRequest request);

    /**
     * 查询容器实例列表
     * 支持分页查询和多种条件过滤，返回容器实例的详细信息
     *
     * @param request 查询容器实例列表请求参数
     * @return 容器实例分页列表响应结果
     */
    CsPageDataResponse<CsContainerDetailInfo> describeContainerInstances(CsDescribeContainerInstancesRequest request);

    /**
     * 删除容器实例
     * 根据容器UUID删除指定的容器实例
     *
     * @param request 删除容器实例请求参数
     * @return 删除容器实例响应结果
     */
    CsDeleteContainerInstanceResponse deleteContainerInstance(CsDeleteContainerInstanceRequest request);

    /**
     * 启动容器实例
     * 启动指定的容器实例，使其进入运行状态
     *
     * @param request 启动容器实例请求参数
     * @return 启动容器实例响应结果
     */
    CsStartContainerInstanceResponse startContainerInstance(CsStartContainerInstanceRequest request);

    /**
     * 停止容器实例
     * 停止指定的容器实例，使其进入停止状态
     *
     * @param request 停止容器实例请求参数
     * @return 停止容器实例响应结果
     */
    CsStopContainerInstanceResponse stopContainerInstance(CsStopContainerInstanceRequest request);

    /**
     * 创建容器网卡
     * 为容器创建新的网络接口，配置IP地址、网关等网络参数
     *
     * @param request 创建容器网卡请求参数
     * @return 创建容器网卡响应结果
     */
    CsCreateContainerNicResponse createContainerNic(CsCreateContainerNicRequest request);

    /**
     * 创建容器VLAN
     * 创建新的虚拟局域网，用于容器网络隔离和管理
     *
     * @param request 创建容器VLAN请求参数
     * @return 创建容器VLAN响应结果
     */
    CsCreateContainerVlanResponse createContainerVlan(CsCreateContainerVlanRequest request);

    /**
     * 查询容器VLAN列表
     * 支持分页查询和条件过滤，返回VLAN配置信息
     *
     * @param request 查询容器VLAN列表请求参数
     * @return 容器VLAN分页列表响应结果
     */
    CsPageDataResponse<CsContainerVlanInfo> describeContainerVlans(CsDescribeContainerVlansRequest request);

    /**
     * 创建容器SSH密钥
     * 创建新的SSH密钥对，用于容器的安全登录认证
     *
     * @param request 创建容器SSH密钥请求参数
     * @return 创建容器SSH密钥响应结果
     */
    CsCreateContainerSshkeyResponse createContainerSshkey(CsCreateContainerSshkeyRequest request);

    /**
     * 查询容器SSH密钥列表
     * 支持分页查询和条件过滤，返回SSH密钥信息
     *
     * @param request 查询容器SSH密钥列表请求参数
     * @return 容器SSH密钥分页列表响应结果
     */
    CsPageDataResponse<CsContainerSshkeyInfo> describeContainerSshkeys(CsDescribeContainerSshkeysRequest request);
}
