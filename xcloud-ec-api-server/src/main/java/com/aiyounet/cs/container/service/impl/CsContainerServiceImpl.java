package com.aiyounet.cs.container.service.impl;

import com.aiya.kpy.container.foundation.service.ContainerService;
import com.aiya.kpy.res.sdk.request.*;
import com.aiya.kpy.res.sdk.response.*;
import com.aiya.kpy.res.sdk.model.*;
import com.aiyounet.cs.common.response.CsPageDataResponse;
import com.aiyounet.cs.container.service.CsContainerService;
import com.aiyounet.cs.container.request.*;
import com.aiyounet.cs.container.response.*;
import com.aiyounet.cs.container.model.*;
import com.aiya.kpy.container.task.model.ContainerTaskModel;
import com.aiya.kpy.container.task.state.*;
import com.aiya.kpy.container.task.state.CreateContainerInstanceTaskState;
import com.aiya.kpy.container.task.state.StartContainerInstanceTaskState;
import com.aiya.kpy.container.task.state.StopContainerInstanceTaskState;
import com.aiya.kpy.container.task.state.DeleteContainerInstanceTaskState;
import com.aiya.kpy.container.task.state.CreateContainerNicTaskState;
import com.aiya.kpy.container.task.state.CreateContainerVlanTaskState;
import com.aiya.kpy.container.task.state.CreateContainerSshkeyTaskState;
import com.aiya.kpy.async.foundation.model.AsyncTaskGroups;
import com.aiya.kpy.async.foundation.model.AsyncTaskName;
import com.aiya.platform.asynctask.AsyncTaskSender;
import com.aiya.platform.foundation.utils.UUIDGenernator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 容器云业务服务实现类
 * 处理容器云平台Web API的业务逻辑，包括数据转换和Foundation层服务调用
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@Service
public class CsContainerServiceImpl implements CsContainerService {

    @Autowired
    private ContainerService containerService;

    @Autowired
    private AsyncTaskSender asyncTaskSender;

    @Override
    public CsCreateContainerInstanceResponse createContainerInstance(CsCreateContainerInstanceRequest request) {
        log.info("开始创建容器实例，请求参数：{}", request);

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setZoneCode(request.getZoneUuid());
        taskModel.setFlavorUuid(request.getFlavorUuid());
        taskModel.setImageUuid(request.getImagePath());
        taskModel.setNics(request.getNics());
        taskModel.setContainerName(request.getDescription());
        taskModel.setDescription(request.getDescription());
        taskModel.setOperationType("CREATE_CONTAINER_INSTANCE");

        // 设置其他创建容器实例的参数
        taskModel.setContainerNetType(request.getContainerNetType());
        taskModel.setGpuNum(request.getGpuNum());
        taskModel.setHostSn(request.getHostSn());
        taskModel.setKeyUuid(request.getKeyUuid());
        taskModel.setPswd(request.getPswd());
        taskModel.setRunArgs(request.getRunArgs());
        taskModel.setVols(request.getVols());

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();
        taskModel.setContainerUuid(taskUuid);

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.CREATE_CONTAINER_INSTANCE,
                taskUuid,
                taskModel,
                CreateContainerInstanceTaskState.INIT
        );

        // 构建响应
        CsCreateContainerInstanceResponse response = new CsCreateContainerInstanceResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("创建容器实例异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsPageDataResponse<CsContainerDetailInfo> describeContainerInstances(CsDescribeContainerInstancesRequest request) {
        log.info("开始查询容器实例列表，请求参数：{}", request);
        
        // 构建Foundation层请求
        DescribeContainerInstancesRequest foundationRequest = new DescribeContainerInstancesRequest();
        foundationRequest.setRegionId(request.getRegionCode());
        foundationRequest.setPageNumber(request.getPage());
        foundationRequest.setPageSize(request.getPageSize());
        foundationRequest.setFlavorUuid(request.getFlavorUuid());
        foundationRequest.setUuid(request.getUuid());
        foundationRequest.setState(request.getState());
        foundationRequest.setIp(request.getIp());
        foundationRequest.setUserId(request.getUserId());
        
        // 调用Foundation层服务
        DescribeContainerInstancesResponse foundationResponse = containerService.describeContainerInstances(foundationRequest);
        
        // 转换数据
        List<CsContainerDetailInfo> containerList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(foundationResponse.getInfo())) {
            containerList = foundationResponse.getInfo().stream()
                .map(this::convertToContainerDetailInfo)
                .collect(Collectors.toList());
        }

        // 构建响应
        CsPageDataResponse<CsContainerDetailInfo> response = new CsPageDataResponse<>(
            containerList,
            foundationResponse.getTotalCount() != null ? foundationResponse.getTotalCount() : 0
        );
        
        log.info("查询容器实例列表成功，返回{}条记录", containerList.size());
        return response;
    }

    @Override
    public CsDeleteContainerInstanceResponse deleteContainerInstance(CsDeleteContainerInstanceRequest request) {
        log.info("开始删除容器实例，容器UUID：{}", request.getUuid());

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setContainerUuid(request.getUuid());
        taskModel.setOperationType("DELETE_CONTAINER_INSTANCE");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.DELETE_CONTAINER_INSTANCE,
                taskUuid,
                taskModel,
                DeleteContainerInstanceTaskState.INIT
        );

        // 构建响应
        CsDeleteContainerInstanceResponse response = new CsDeleteContainerInstanceResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("删除容器实例异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsStartContainerInstanceResponse startContainerInstance(CsStartContainerInstanceRequest request) {
        log.info("开始启动容器实例，容器UUID：{}", request.getUuid());

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setContainerUuid(request.getUuid());
        taskModel.setOperationType("START_CONTAINER_INSTANCE");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.START_CONTAINER_INSTANCE,
                taskUuid,
                taskModel,
                StartContainerInstanceTaskState.INIT
        );

        // 构建响应
        CsStartContainerInstanceResponse response = new CsStartContainerInstanceResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("启动容器实例异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsStopContainerInstanceResponse stopContainerInstance(CsStopContainerInstanceRequest request) {
        log.info("开始停止容器实例，容器UUID：{}", request.getUuid());

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setContainerUuid(request.getUuid());
        taskModel.setOperationType("STOP_CONTAINER_INSTANCE");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.STOP_CONTAINER_INSTANCE,
                taskUuid,
                taskModel,
                StopContainerInstanceTaskState.INIT
        );

        // 构建响应
        CsStopContainerInstanceResponse response = new CsStopContainerInstanceResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("停止容器实例异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsCreateContainerNicResponse createContainerNic(CsCreateContainerNicRequest request) {
        log.info("开始创建容器网卡，请求参数：{}", request);

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setZoneCode(request.getZoneUuid());
        taskModel.setContainerUuid(request.getDockerUuid());
        taskModel.setNetworkUuid(request.getVlanUuid());
        taskModel.setIpAddress(request.getIp());
        taskModel.setGateway(request.getGateway());
        taskModel.setDescription(request.getDescription());
        taskModel.setOperationType("CREATE_CONTAINER_NIC");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();
        taskModel.setNicUuid(taskUuid);

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.CREATE_CONTAINER_NIC,
                taskUuid,
                taskModel,
                CreateContainerNicTaskState.INIT
        );

        // 构建响应
        CsCreateContainerNicResponse response = new CsCreateContainerNicResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("创建容器网卡异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsCreateContainerVlanResponse createContainerVlan(CsCreateContainerVlanRequest request) {
        log.info("开始创建容器VLAN，请求参数：{}", request);

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setZoneCode(request.getZoneUuid());
        taskModel.setVlanName(request.getDescription());
        taskModel.setDescription(request.getDescription());
        taskModel.setIsInternal(request.getIsInternal());
        taskModel.setGateway(request.getGateway());
        taskModel.setVlanId(request.getVni());
        taskModel.setOperationType("CREATE_CONTAINER_VLAN");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();
        taskModel.setVlanUuid(taskUuid);

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.CREATE_CONTAINER_VLAN,
                taskUuid,
                taskModel,
                CreateContainerVlanTaskState.INIT
        );

        // 构建响应
        CsCreateContainerVlanResponse response = new CsCreateContainerVlanResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("创建容器VLAN异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsPageDataResponse<CsContainerVlanInfo> describeContainerVlans(CsDescribeContainerVlansRequest request) {
        log.info("开始查询容器VLAN列表，请求参数：{}", request);
        
        // 构建Foundation层请求
        DescribeContainerVlansRequest foundationRequest = new DescribeContainerVlansRequest();
        foundationRequest.setRegionId(request.getRegionCode());
        foundationRequest.setPageNumber(request.getPage());
        foundationRequest.setPageSize(request.getPageSize());
        foundationRequest.setUuid(request.getUuid());
        foundationRequest.setName(request.getName());
        foundationRequest.setIsInternal(request.getIsInternal());
        foundationRequest.setState(request.getState());
        foundationRequest.setZoneUuid(request.getZoneUuid());
        
        // 调用Foundation层服务
        DescribeContainerVlansResponse foundationResponse = containerService.describeContainerVlans(foundationRequest);
        
        // 转换数据
        List<CsContainerVlanInfo> vlanList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(foundationResponse.getInfo())) {
            vlanList = foundationResponse.getInfo().stream()
                .map(vlan -> {
                    CsContainerVlanInfo vlanInfo = new CsContainerVlanInfo();
                    BeanUtils.copyProperties(vlan, vlanInfo);
                    return vlanInfo;
                })
                .collect(Collectors.toList());
        }

        // 构建响应
        CsPageDataResponse<CsContainerVlanInfo> response = new CsPageDataResponse<>(
            vlanList,
            foundationResponse.getTotalCount() != null ? foundationResponse.getTotalCount() : 0
        );
        
        log.info("查询容器VLAN列表成功，返回{}条记录", vlanList.size());
        return response;
    }

    @Override
    public CsCreateContainerSshkeyResponse createContainerSshkey(CsCreateContainerSshkeyRequest request) {
        log.info("开始创建容器SSH密钥，请求参数：{}", request);

        // 构建容器异步任务模型
        ContainerTaskModel taskModel = new ContainerTaskModel();
        taskModel.setRegionCode(request.getRegionCode());
        taskModel.setSshkeyName(request.getName());
        taskModel.setDescription(request.getDescription());
        taskModel.setOperationType("CREATE_CONTAINER_SSHKEY");

        // 生成任务UUID
        String taskUuid = UUIDGenernator.nextUUID();
        taskModel.setSshkeyUuid(taskUuid);

        // 发送异步任务
        asyncTaskSender.sendTask(
                AsyncTaskGroups.CONTAINER_TASK_GROUP,
                AsyncTaskName.CREATE_CONTAINER_SSHKEY,
                taskUuid,
                taskModel,
                CreateContainerSshkeyTaskState.INIT
        );

        // 构建响应
        CsCreateContainerSshkeyResponse response = new CsCreateContainerSshkeyResponse();
        response.setTaskUuid(taskUuid);
        response.setRequestId(taskUuid);
        response.setHostId("async-task");

        log.info("创建容器SSH密钥异步任务已发送，任务UUID：{}", taskUuid);
        return response;
    }

    @Override
    public CsPageDataResponse<CsContainerSshkeyInfo> describeContainerSshkeys(CsDescribeContainerSshkeysRequest request) {
        log.info("开始查询容器SSH密钥列表，请求参数：{}", request);
        
        // 构建Foundation层请求
        DescribeContainerSshkeysRequest foundationRequest = new DescribeContainerSshkeysRequest();
        foundationRequest.setRegionId(request.getRegionCode());
        foundationRequest.setPageNumber(request.getPage());
        foundationRequest.setPageSize(request.getPageSize());
        foundationRequest.setUuid(request.getUuid());
        foundationRequest.setName(request.getName());
        
        // 调用Foundation层服务
        DescribeContainerSshkeysResponse foundationResponse = containerService.describeContainerSshkeys(foundationRequest);
        
        // 转换数据
        List<CsContainerSshkeyInfo> sshkeyList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(foundationResponse.getInfo())) {
            sshkeyList = foundationResponse.getInfo().stream()
                .map(sshkey -> {
                    CsContainerSshkeyInfo sshkeyInfo = new CsContainerSshkeyInfo();
                    BeanUtils.copyProperties(sshkey, sshkeyInfo);
                    return sshkeyInfo;
                })
                .collect(Collectors.toList());
        }

        // 构建响应
        CsPageDataResponse<CsContainerSshkeyInfo> response = new CsPageDataResponse<>(
            sshkeyList,
            foundationResponse.getTotalCount() != null ? foundationResponse.getTotalCount() : 0
        );
        
        log.info("查询容器SSH密钥列表成功，返回{}条记录", sshkeyList.size());
        return response;
    }

    /**
     * 转换容器详细信息
     */
    private CsContainerDetailInfo convertToContainerDetailInfo(Container container) {
        CsContainerDetailInfo info = new CsContainerDetailInfo();
        BeanUtils.copyProperties(container, info);
        
        // 转换网卡信息
        if (!CollectionUtils.isEmpty(container.getNics())) {
            List<CsContainerNicInfo> nicList = container.getNics().stream()
                .map(nic -> {
                    CsContainerNicInfo nicInfo = new CsContainerNicInfo();
                    BeanUtils.copyProperties(nic, nicInfo);
                    return nicInfo;
                })
                .collect(Collectors.toList());
            info.setNics(nicList);
        }
        
        // 转换存储卷信息
        if (!CollectionUtils.isEmpty(container.getVols())) {
            List<CsContainerVolInfo> volList = container.getVols().stream()
                .map(vol -> {
                    CsContainerVolInfo volInfo = new CsContainerVolInfo();
                    BeanUtils.copyProperties(vol, volInfo);
                    return volInfo;
                })
                .collect(Collectors.toList());
            info.setVols(volList);
        }
        
        return info;
    }
}
