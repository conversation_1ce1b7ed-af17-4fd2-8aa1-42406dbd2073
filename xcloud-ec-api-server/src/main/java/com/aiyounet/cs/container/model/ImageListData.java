package com.aiyounet.cs.container.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 镜像列表数据模型
 * 包含镜像列表和可用标签列表
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Data
@ApiModel("镜像列表数据模型")
public class ImageListData {

    @ApiModelProperty(value = "镜像列表", required = true)
    private List<ImageInfoDetail> list;

    @ApiModelProperty(value = "所有可用标签列表", required = true, example = "[\"AI\", \"ComfyUI\", \"图像处理\", \"数字人\"]")
    private List<String> tags;
}
