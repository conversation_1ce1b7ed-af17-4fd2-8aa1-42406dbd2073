package com.aiyounet.supervisor.publicip.model;

import com.aiya.kpy.common.enums.*;
import com.aiya.kpy.ec.api.enums.BindStatus;
import com.aiya.kpy.ec.api.enums.OwnBindType;
import com.aiya.kpy.publicip.api.enums.PublicIpBindLevel;
import com.aiya.kpy.publicip.api.enums.PublicIpPendingStatus;
import com.aiya.kpy.publicip.api.enums.PublicIpStatus;
import com.aiya.platform.api.enums.UserClassifyEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> by hxy
 * @date 2018/8/9
 */
@Getter
@Setter
public class PublicIpInfo implements Serializable {
	private static final long serialVersionUID = 1206725892962093551L;
	/**
	 * 弹性IP标识
	 */
	private String publicIpId;
	/**
	 * 公网IP名称
	 */
	private String publicIpName;
	/**
	 * 公网IP地址
	 */
	private String ipAddress;
	/**
	 * 带宽
	 */
	private Integer bandwidth;
	/**
	 * 地域编码
	 */
	private String regionCode;
	/**
	 * 地域名称
	 */
	private String regionName;
	/**
	 * 绑定状态
	 */
	private BindStatus bindStatus;
	/**
	 * 网络计费
	 */
	private InternetChargeType internetChargeType;
	/**
	 * 实例ID
	 */
	private String instanceId;
	/**
	 * 实例名称
	 */
	private String instanceName;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 到期时间
	 */
	private Date expireTime;
	/**
	 * 自动续费状态
	 */
	private AutoRenewStatus renewStatus;
	/**
	 * 弹性IP状态
	 */
	private PublicIpStatus publicIpStatus;
	/**
	 * 是否为主IP
	 */
	private PublicIpBindLevel ipBindLevel;
	/**
	 * 绑定方式
	 */
	private OwnBindType bindType;
	/**
	 * 线路类型
	 */
	private BandwidthType bandwidthType;
	/**
	 * 资源所属用户
	 */
	private Long ownerId;
	/**
	 * 是否在试用中
	 */
	private Boolean inTryOut;
	/**
	 * 是否来自试用产品
	 */
	private Boolean fromTryOut;
	/**
	 * 最多还能增加多少天试用期
	 */
	private Integer maxLeftTryDay;
	/**
	 * 是否欠费
	 */
	private BooleanEnum inArrearage;
	/**
	 * 业务流量欠费金额
	 */
	private BigDecimal flowArrearageCharge;
	/**
	 * 操作状态
	 */
	private PublicIpPendingStatus pendingStatus;
	/**
	 * 限制业务流量后的带宽
	 */
	private BigDecimal limitBandwidthValues;
	/**
	 * 绑定实在所在的可用区编码
	 */
	private String zoneCode;
	/**
	 * 绑定实在所在可用区名称
	 */
	private String zoneName;
	/**
	 * 付费方式
	 */
	private InstanceChargeType chargeType;
	/**
	 * 加速地域编码
	 */
	private String accelRegionCode;
	/*** 加速地域名称 */
	private String accelRegionName;
	/**
	 * 用户名
	 */
	private String userName;
	/**
	 * 邮箱
	 */
	private String email;
	/* 用户类别 */
	private UserClassifyEnum userClassify;
	/**
	 * 弹性带宽
	 */
	private Integer elasticBandwidth;
	/**
	 * 默认网关
	 */
	private String gateway;
	/**
	 * 默认子网掩码
	 */
	private String subnetMask;
	/**
	 * IP是否被禁用
	 */
	private Boolean ipDisabled;

	public PublicIpInfo() {
	}
}
