package com.aiyounet.supervisor.cloudmonitor.service;

import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorRuleInfo;
import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorWarningInfo;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorRuleListRequest;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorWarningListRequest;
import com.aiyounet.supervisor.cloudmonitor.response.SuperCloudMonitorPageDataResponse;
import com.aiyounet.supervisor.cloudmonitor.response.SuperGetCloudMonitorRuleDetailResponse;

import java.util.Date;

/**
 * @Author: chenqiang
 * @Date: 2022/6/22
 */
public interface SuperCloudMonitorService {

    SuperCloudMonitorPageDataResponse<CloudMonitorRuleInfo> getCloudMonitorRuleList(SuperGetCloudMonitorRuleListRequest.PageFilter filterRequest, String domainName);

    SuperGetCloudMonitorRuleDetailResponse getCloudMonitorRuleDetail(String ruleUUID);

    SuperCloudMonitorPageDataResponse<CloudMonitorWarningInfo> getCloudMonitorWarningList(SuperGetCloudMonitorWarningListRequest.PageFilter filterRequest, Date startTime, Date endTime, String domainName);
}
