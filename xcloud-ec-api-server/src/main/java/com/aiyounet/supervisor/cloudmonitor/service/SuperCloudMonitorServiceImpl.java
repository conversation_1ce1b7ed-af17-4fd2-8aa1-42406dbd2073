package com.aiyounet.supervisor.cloudmonitor.service;

import com.aiya.kpy.cloudmonitor.api.CloudMonitorAPI;
import com.aiya.kpy.cloudmonitor.api.enums.AlertType;
import com.aiya.kpy.cloudmonitor.api.enums.CloudMonitorRuleStatus;
import com.aiya.kpy.cloudmonitor.api.enums.MonitorWarningStatus;
import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorRuleInfo;
import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorWarningInfo;
import com.aiya.kpy.cloudmonitor.api.request.DescribeCloudMonitorRuleDetailRequest;
import com.aiya.kpy.cloudmonitor.api.request.DescribeCloudMonitorRuleListRequest;
import com.aiya.kpy.cloudmonitor.api.request.DescribeCloudMonitorWarningListRequest;
import com.aiya.kpy.cloudmonitor.api.response.DescribeCloudMonitorRuleDetailResponse;
import com.aiya.kpy.cloudmonitor.api.response.DescribeCloudMonitorRuleListResponse;
import com.aiya.kpy.cloudmonitor.api.response.DescribeCloudMonitorWarningListResponse;
import com.aiya.kpy.common.util.AssertUtil;
import com.aiya.kpy.uic.api.UserMgmtAPI;
import com.aiya.kpy.uic.api.model.DomainNameInfo;
import com.aiya.kpy.uic.api.model.UserInfo;
import com.aiya.kpy.uic.api.request.DescribeUserByUserIdRequest;
import com.aiya.kpy.uic.api.request.GetDomainNameInfoByDomainNameRequest;
import com.aiya.kpy.uic.api.response.DescribeUserByUserIdResponse;
import com.aiya.kpy.uic.api.response.GetDomainNameInfoByDomainNameResponse;
import com.aiya.platform.api.enums.UserClassifyEnum;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorRuleListRequest;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorWarningListRequest;
import com.aiyounet.supervisor.cloudmonitor.response.SuperCloudMonitorPageDataResponse;
import com.aiyounet.supervisor.cloudmonitor.response.SuperGetCloudMonitorRuleDetailResponse;
import com.aiyounet.supervisor.normaluser.service.UserIdFactory;
import com.aiyounet.supervisor.util.SuperRequestConvertUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @Author: chenqiang
 * @Date: 2022/6/22
 */
@Service
public class SuperCloudMonitorServiceImpl implements SuperCloudMonitorService {
    @Autowired
    private CloudMonitorAPI cloudMonitorAPI;
    @Autowired
    private UserIdFactory userIdFactory;
    @Autowired
    private UserMgmtAPI userMgmtAPI;

    @Override
    public SuperCloudMonitorPageDataResponse<CloudMonitorRuleInfo> getCloudMonitorRuleList(SuperGetCloudMonitorRuleListRequest.PageFilter pageFilter, String domainName) {
        DescribeCloudMonitorRuleListRequest request = new DescribeCloudMonitorRuleListRequest();
        CloudMonitorRuleStatus status = pageFilter.getStatus();
        UserClassifyEnum userClassify = pageFilter.getUserClassify();
        //关键字
        String field = pageFilter.getField();
        String keyword = pageFilter.getKeyword();
        // 运维通过用户名模糊查询列表
        if (userIdFactory.support(field, keyword)) {
            List<Long> userIds = userIdFactory.getUserIdsByKeywordFuzzy(field, keyword, domainName);
            if (CollectionUtils.isEmpty(userIds)) {
                return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
            }
            request.setQueryUserIds(userIds);
        } else if (StringUtils.isNotEmpty(keyword) && StringUtils.isNotEmpty(field)) {
            request.setKeyword(keyword);
            request.setField(field);
        }
        //域名校验
        if (StringUtils.isNotBlank(domainName)) {
            GetDomainNameInfoByDomainNameRequest domainRequest = new GetDomainNameInfoByDomainNameRequest();
            domainRequest.setDomainNames(Arrays.asList(domainName));
            final GetDomainNameInfoByDomainNameResponse domainResponse = userMgmtAPI.getDomainNameInfoByDomainName(domainRequest);
            AssertUtil.checkAPIResponse(domainResponse);
            final List<DomainNameInfo> domainNameInfos = domainResponse.getDomainNameInfos();
            if (CollectionUtils.isEmpty(domainNameInfos)) {
                return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
            }
            request.setQueryDomainId(domainNameInfos.get(0).getId());
        }
        request.setStatus(status);
        request.setPageNumber(pageFilter.getPage());
        request.setPageSize(pageFilter.getPageSize());
        request.setUserClassify(userClassify);
        //增加超管权限参数
        SuperRequestConvertUtils.converSuperRequest(request);
        final DescribeCloudMonitorRuleListResponse response = cloudMonitorAPI.describeCloudMonitorRuleList(request);
        AssertUtil.checkAPIResponse(response);
        final List<CloudMonitorRuleInfo> ruleInfoList = response.getMonitorRuleInfos();
        if (CollectionUtils.isEmpty(ruleInfoList)) {
            return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
        }

        for (CloudMonitorRuleInfo ruleInfo : ruleInfoList) {
            DescribeUserByUserIdRequest userRequest = new DescribeUserByUserIdRequest();
            userRequest.setUserId(ruleInfo.getOwnerId());
            DescribeUserByUserIdResponse userResponse = userMgmtAPI.describeUserByUserId(userRequest);
            AssertUtil.checkAPIResponse(userResponse);
            UserInfo userInfo = userResponse.getUserInfo();
            if (Objects.nonNull(userInfo)) {
                ruleInfo.setUserName(userInfo.getUserName());
                ruleInfo.setEmail(userInfo.getEmail());
                ruleInfo.setUserClassify(userInfo.getUserClassify());
            }
        }
        return new SuperCloudMonitorPageDataResponse(ruleInfoList, response.getAmount(), response.getAmount());
    }

    @Override
    public SuperGetCloudMonitorRuleDetailResponse getCloudMonitorRuleDetail(String ruleUUID) {
        DescribeCloudMonitorRuleDetailRequest request = new DescribeCloudMonitorRuleDetailRequest();
        request.setRuleUUID(ruleUUID);
        //增加超管权限参数
        SuperRequestConvertUtils.converSuperRequest(request);
        final DescribeCloudMonitorRuleDetailResponse response = cloudMonitorAPI.describeCloudMonitorRuleDetail(request);
        AssertUtil.checkAPIResponse(response);
        CloudMonitorRuleInfo ruleInfo = response.getRuleInfo();
        if (Objects.isNull(ruleInfo)) {
            return new SuperGetCloudMonitorRuleDetailResponse();
        }
        DescribeUserByUserIdRequest userRequest = new DescribeUserByUserIdRequest();
        userRequest.setUserId(ruleInfo.getOwnerId());
        DescribeUserByUserIdResponse userResponse = userMgmtAPI.describeUserByUserId(userRequest);
        AssertUtil.checkAPIResponse(userResponse);
        UserInfo userInfo = userResponse.getUserInfo();
        if (Objects.nonNull(userInfo)) {
            ruleInfo.setUserName(userInfo.getUserName());
            ruleInfo.setEmail(userInfo.getEmail());
            ruleInfo.setUserClassify(userInfo.getUserClassify());
        }
        return new SuperGetCloudMonitorRuleDetailResponse(ruleInfo);
    }

    @Override
    public SuperCloudMonitorPageDataResponse<CloudMonitorWarningInfo> getCloudMonitorWarningList(SuperGetCloudMonitorWarningListRequest.PageFilter pageFilter, Date startTime, Date endTime, String domainName) {
        DescribeCloudMonitorWarningListRequest request = new DescribeCloudMonitorWarningListRequest();
        AlertType alertType = pageFilter.getAlertType();
        MonitorWarningStatus status = pageFilter.getStatus();
        //关键字
        String field = pageFilter.getField();
        String keyword = pageFilter.getKeyword();
        // 运维通过用户名模糊查询列表
        if (userIdFactory.support(field, keyword)) {
            List<Long> userIds = userIdFactory.getUserIdsByKeywordFuzzy(field, keyword, domainName);
            if (CollectionUtils.isEmpty(userIds)) {
                return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
            }
            request.setQueryUserIds(userIds);
        } else if (StringUtils.isNotEmpty(keyword) && StringUtils.isNotEmpty(field)) {
            request.setKeyword(keyword);
            request.setField(field);
        }
        //域名校验
        if (StringUtils.isNotBlank(domainName)) {
            GetDomainNameInfoByDomainNameRequest domainRequest = new GetDomainNameInfoByDomainNameRequest();
            domainRequest.setDomainNames(Arrays.asList(domainName));
            final GetDomainNameInfoByDomainNameResponse domainResponse = userMgmtAPI.getDomainNameInfoByDomainName(domainRequest);
            AssertUtil.checkAPIResponse(domainResponse);
            final List<DomainNameInfo> domainNameInfos = domainResponse.getDomainNameInfos();
            if (CollectionUtils.isEmpty(domainNameInfos)) {
                return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
            }
            request.setQueryDomainId(domainNameInfos.get(0).getId());
        }
        request.setAlertType(alertType);
        request.setStatus(status);
        request.setPageNumber(pageFilter.getPage());
        request.setPageSize(pageFilter.getPageSize());
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        //增加超管权限参数
        SuperRequestConvertUtils.converSuperRequest(request);
        final DescribeCloudMonitorWarningListResponse response = cloudMonitorAPI.describeCloudMonitorWarningList(request);
        AssertUtil.checkAPIResponse(response);
        final List<CloudMonitorWarningInfo> warningInfoList = response.getMonitorWarningInfos();
        if (CollectionUtils.isEmpty(warningInfoList)) {
            return new SuperCloudMonitorPageDataResponse(Lists.newArrayList(), 0, 0);
        }

        for (CloudMonitorWarningInfo warningInfo : warningInfoList) {
            DescribeUserByUserIdRequest userRequest = new DescribeUserByUserIdRequest();
            userRequest.setUserId(warningInfo.getOwnerId());
            DescribeUserByUserIdResponse userResponse = userMgmtAPI.describeUserByUserId(userRequest);
            AssertUtil.checkAPIResponse(userResponse);
            UserInfo userInfo = userResponse.getUserInfo();
            if (Objects.nonNull(userInfo)) {
                warningInfo.setUserName(userInfo.getUserName());
                warningInfo.setEmail(userInfo.getEmail());
                warningInfo.setUserClassify(userInfo.getUserClassify());
            }
        }
        return new SuperCloudMonitorPageDataResponse(warningInfoList, response.getAmount(), response.getAmount());
    }
}
