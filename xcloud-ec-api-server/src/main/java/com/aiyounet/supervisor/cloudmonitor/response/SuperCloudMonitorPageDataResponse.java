package com.aiyounet.supervisor.cloudmonitor.response;

import com.aiya.platform.web.WebResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SuperCloudMonitorPageDataResponse<T> extends WebResponse {
	private static final long serialVersionUID = 54132022063063862L;

	private int totalCount;
	private int total;

	public SuperCloudMonitorPageDataResponse() {

	}

	public SuperCloudMonitorPageDataResponse(List<T> data) {
		super.setData(data);
	}

	public SuperCloudMonitorPageDataResponse(List<T> data, int totalCount) {
		super.setData(data);
		this.totalCount = totalCount;
	}

	public SuperCloudMonitorPageDataResponse(List<T> data, int totalCount, int total) {
		super.setData(data);
		this.totalCount = totalCount;
		this.total = total;
	}
}
