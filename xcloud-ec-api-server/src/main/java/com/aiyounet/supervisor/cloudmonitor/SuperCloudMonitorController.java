package com.aiyounet.supervisor.cloudmonitor;

import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorRuleInfo;
import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorWarningInfo;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorRuleDetailRequest;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorRuleListRequest;
import com.aiyounet.supervisor.cloudmonitor.request.SuperGetCloudMonitorWarningListRequest;
import com.aiyounet.supervisor.cloudmonitor.response.SuperCloudMonitorPageDataResponse;
import com.aiyounet.supervisor.cloudmonitor.response.SuperGetCloudMonitorRuleDetailResponse;
import com.aiyounet.supervisor.cloudmonitor.service.SuperCloudMonitorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * @Author: chenqiang
 * @Date: 2022/6/22
 */
@Controller
@RequestMapping("admin/cloudmonitor")
public class SuperCloudMonitorController {

    @Autowired
    private SuperCloudMonitorService superCloudMonitorService;

    @RequestMapping("getCloudMonitorRuleList")
    public SuperCloudMonitorPageDataResponse<CloudMonitorRuleInfo> getCloudMonitorRuleList(SuperGetCloudMonitorRuleListRequest request) {
        return superCloudMonitorService.getCloudMonitorRuleList(request.getFilterRequest(), request.getDomainName());
    }

    @RequestMapping("getCloudMonitorRuleDetail")
    public SuperGetCloudMonitorRuleDetailResponse getCloudMonitorRuleDetail(SuperGetCloudMonitorRuleDetailRequest request) {
        return superCloudMonitorService.getCloudMonitorRuleDetail(request.getRuleUUID());
    }

    @RequestMapping("getCloudMonitorWarningList")
    public SuperCloudMonitorPageDataResponse<CloudMonitorWarningInfo> getCloudMonitorWarningList(SuperGetCloudMonitorWarningListRequest request) {
        return superCloudMonitorService.getCloudMonitorWarningList(request.getFilterRequest(), request.getStartTime(), request.getEndTime(), request.getDomainName());
    }
}
