package com.aiyounet.supervisor.cloudmonitor.response;

import com.aiya.kpy.cloudmonitor.api.model.CloudMonitorRuleInfo;
import com.aiya.platform.web.WebResponse;
import lombok.Getter;
import lombok.Setter;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2022/6/22
 */
@Getter
@Setter
public class SuperGetCloudMonitorRuleDetailResponse extends WebResponse {

    private CloudMonitorRuleInfo cloudMonitorRuleInfo;

    public SuperGetCloudMonitorRuleDetailResponse() {
    }

    public SuperGetCloudMonitorRuleDetailResponse(CloudMonitorRuleInfo cloudMonitorRuleInfo) {
        this.cloudMonitorRuleInfo = cloudMonitorRuleInfo;
    }
}
