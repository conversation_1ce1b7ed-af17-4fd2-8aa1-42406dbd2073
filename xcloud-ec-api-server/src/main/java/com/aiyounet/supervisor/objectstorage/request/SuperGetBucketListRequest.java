package com.aiyounet.supervisor.objectstorage.request;



import com.aiya.platform.api.enums.UserClassifyEnum;
import com.aiyounet.supervisor.common.PageFilterRequest;
import com.aiyounet.supervisor.common.request.PageBeanRequest;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;



@Setter
@Getter
public class SuperGetBucketListRequest extends PageBeanRequest<SuperGetBucketListRequest.PageFilter> {
    private static final long serialVersionUID = -8092786643139474920L;
    /**
     * 开始日期
     */
    private Date startTime;

    /**
     * 结束日期
     */
    private Date endTime;

    /**
     * 地域编码
     */
    private String regionCode;

    @Override
    public Class<PageFilter> filterClass() {
        return SuperGetBucketListRequest.PageFilter.class;
    }

    public SuperGetBucketListRequest() {
    }

    @Setter
    @Getter
    public static final class PageFilter extends PageFilterRequest {
        public static final String USER_NAME_KEY = "userName";

        private UserClassifyEnum userClassify;

        public PageFilter() {
        }
    }
}
