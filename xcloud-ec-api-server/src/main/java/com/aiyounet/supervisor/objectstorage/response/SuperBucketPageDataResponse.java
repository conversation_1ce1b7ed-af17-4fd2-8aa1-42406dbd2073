package com.aiyounet.supervisor.objectstorage.response;

import com.aiya.platform.web.WebResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SuperBucketPageDataResponse<T> extends WebResponse {

	private static final long serialVersionUID = 4942717225428020198L;


	public SuperBucketPageDataResponse() {

	}

	public SuperBucketPageDataResponse(List<T> data) {
		super.setData(data);
	}

	public SuperBucketPageDataResponse(List<T> data, int rowCount) {
		super.setData(data);
		super.setRowCount(rowCount);
	}

}
