package com.aiyounet.supervisor.objectstorage.response;

import com.aiya.platform.web.WebResponse;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class SuperObjectPageDataResponse<T> extends WebResponse {

	private static final long serialVersionUID = -5393160510097547287L;


	public SuperObjectPageDataResponse() {

	}

	public SuperObjectPageDataResponse(List<T> data) {
		super.setData(data);
	}

	public SuperObjectPageDataResponse(List<T> data, int rowCount) {
		super.setData(data);
		super.setRowCount(rowCount);
	}

}
