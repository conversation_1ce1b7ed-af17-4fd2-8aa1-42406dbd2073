package com.aiyounet.supervisor.objectstorage.request;




import com.aiyounet.supervisor.common.PageFilterRequest;
import com.aiyounet.supervisor.common.request.PageBeanRequest;
import lombok.Getter;
import lombok.Setter;




@Setter
@Getter
public class SuperGetObjectListRequest extends PageBeanRequest<SuperGetObjectListRequest.PageFilter> {
    private static final long serialVersionUID = -8093781643139474920L;

    /**
     * 桶uuid
     */
    private String bucketUUID;

    @Override
    public Class<PageFilter> filterClass() {
        return SuperGetObjectListRequest.PageFilter.class;
    }

    public SuperGetObjectListRequest() {
    }

    @Setter
    @Getter
    public static final class PageFilter extends PageFilterRequest {
        public static final String USER_NAME_KEY = "userName";


        public PageFilter() {
        }
    }
}
