package com.aiyounet.supervisor.objectstorage.service;


import com.aiya.kpy.objectstorage.api.model.BucketInfo;
import com.aiya.kpy.objectstorage.api.model.ObjectInfo;
import com.aiyounet.supervisor.objectstorage.request.SuperGetBucketListRequest;
import com.aiyounet.supervisor.objectstorage.request.SuperGetObjectListRequest;
import com.aiyounet.supervisor.objectstorage.response.SuperBucketPageDataResponse;
import com.aiyounet.supervisor.objectstorage.response.SuperObjectPageDataResponse;

import java.util.Date;

public interface SuperObjectStorageService {
    /**
     * 获取桶列表
     */
    SuperBucketPageDataResponse<BucketInfo> getBucketList(SuperGetBucketListRequest.PageFilter filterRequest, Date startTime, Date endTime, String regionCode);

    /**
     * 获取对象列表
     */
    SuperObjectPageDataResponse<ObjectInfo> getObjectList(SuperGetObjectListRequest.PageFilter filterRequest, String bucketUUID);
}
