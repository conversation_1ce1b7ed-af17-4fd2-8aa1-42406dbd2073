package com.aiyounet.supervisor.objectstorage.service.impl;

import com.aiya.kpy.api.uic.UicApiWrapper;
import com.aiya.kpy.common.util.AssertUtil;
import com.aiya.kpy.ec.api.constant.ErrorCodes;
import com.aiya.kpy.ec.foundation.entity.KpyRegionEntity;
import com.aiya.kpy.ec.foundation.service.RegionZoneService;
import com.aiya.kpy.objectstorage.api.ObjectStorageAPI;
import com.aiya.kpy.objectstorage.api.model.BucketInfo;
import com.aiya.kpy.objectstorage.api.model.ObjectInfo;
import com.aiya.kpy.objectstorage.api.request.DescribeBucketListRequest;
import com.aiya.kpy.objectstorage.api.request.DescribeObjectListRequest;
import com.aiya.kpy.objectstorage.api.response.DescribeBucketListResponse;
import com.aiya.kpy.objectstorage.api.response.DescribeObjectListResponse;
import com.aiya.kpy.uic.api.model.FindConsoleUserModel;
import com.aiya.kpy.uic.api.model.UserInfo;
import com.aiya.kpy.uic.api.request.FindConsoleUserRequest;
import com.aiya.kpy.uic.api.response.FindConsoleUserResponse;
import com.aiya.platform.api.core.ErrorCode;
import com.aiya.platform.api.enums.UserClassifyEnum;
import com.aiya.platform.foundation.exception.BusinessException;
import com.aiyounet.common.bean.PageData;
import com.aiyounet.supervisor.objectstorage.request.SuperGetBucketListRequest;
import com.aiyounet.supervisor.objectstorage.request.SuperGetObjectListRequest;
import com.aiyounet.supervisor.objectstorage.response.SuperBucketPageDataResponse;
import com.aiyounet.supervisor.objectstorage.response.SuperObjectPageDataResponse;
import com.aiyounet.supervisor.objectstorage.service.SuperObjectStorageService;
import com.aiyounet.supervisor.util.SuperRequestConvertUtils;
import com.kaopuyun.combine.common.request.component.AbstractContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class SuperObjectStorageServiceImpl implements SuperObjectStorageService {
    @Autowired
    private UicApiWrapper uicApiWrapper;
    @Autowired
    private ObjectStorageAPI objectStorageAPI;
    @Autowired
    private RegionZoneService regionZoneService;

    @Override
    public SuperBucketPageDataResponse<BucketInfo> getBucketList(SuperGetBucketListRequest.PageFilter filter, Date startTime, Date endTime, String regionCode) {
        DescribeBucketListRequest request = new DescribeBucketListRequest();
        String field = filter.getField();
        String keyword = filter.getKeyword();
        request.setUserClassifyEnum(filter.getUserClassify());

        if (StringUtils.isNotBlank(field) && StringUtils.isNotBlank(keyword)) {
            if ("ownAccount".equals(field)) {
                //根据所属账户查询ids列表
                FindConsoleUserRequest userRequest = new FindConsoleUserRequest();
                Set<Long> domainIds = AbstractContext.getUser().getDomainIds();
                Long userId = AbstractContext.getUser().getUserId();
                userRequest.setOprUserId(userId);
                userRequest.setDomainIds(domainIds);
                FindConsoleUserModel buildModel = FindConsoleUserModel.builder().emailFuzzy(keyword).build();
                userRequest.setFindConsoleUserModel(buildModel);
                FindConsoleUserResponse consoleUser = uicApiWrapper.findConsoleUser(userRequest);
                List<UserInfo> userInfos = consoleUser.getUserInfos();
                if (CollectionUtils.isEmpty(userInfos)) {
                    return new SuperBucketPageDataResponse<>(new ArrayList<>(), 0);
                }
                request.setQueryUserIds(userInfos.stream().map(item -> item.getUserId()).collect(Collectors.toList()));
            } else {
                request.setField(field);
                request.setKeyword(keyword);
            }
        }

        if (StringUtils.isNotBlank(regionCode)) {
            KpyRegionEntity regionInfo = regionZoneService.getRegionInfo(regionCode);
            request.setRegionId(Objects.isNull(regionInfo) ? null : regionInfo.getId());
        }

        //增加超管权限参数
        SuperRequestConvertUtils.converSuperRequest(request);
        final DescribeBucketListResponse response = objectStorageAPI.describeBucketList(request);
        AssertUtil.checkAPIResponse(response);
        List<BucketInfo> bucketInfos = response.getBucketInfos();
        if (CollectionUtils.isEmpty(bucketInfos)) {
            return new SuperBucketPageDataResponse<>(new ArrayList<>(), 0);
        }
        return new SuperBucketPageDataResponse<>(response.getBucketInfos(), response.getAmount());
    }

    /**
     * 获取对象列表
     */
    @Override
    public SuperObjectPageDataResponse<ObjectInfo> getObjectList(SuperGetObjectListRequest.PageFilter filterRequest, String bucketUUID) {
        DescribeObjectListRequest request = new DescribeObjectListRequest();
        request.setBucketUUID(bucketUUID);
        request.setOrderBy(filterRequest.getOrderBy());
        request.setOrderType(filterRequest.getOrderType());
        //增加超管权限参数
        SuperRequestConvertUtils.converSuperRequest(request);
        final DescribeObjectListResponse response = objectStorageAPI.describeObjectList(request);
        AssertUtil.checkAPIResponse(response);
        List<ObjectInfo> objectInfos = response.getObjectInfos();

        if (CollectionUtils.isEmpty(objectInfos)) {
            return new SuperObjectPageDataResponse<>(new ArrayList<>(), 0);
        }
        return new SuperObjectPageDataResponse<>(response.getObjectInfos(), response.getAmount());
    }
}
