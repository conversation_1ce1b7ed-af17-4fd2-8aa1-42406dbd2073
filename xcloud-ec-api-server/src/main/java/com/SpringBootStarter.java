package com;

import com.aiya.kpy.ec.task.mq.EcReceiveTopic;
import com.aiya.kpy.ec.task.mq.EcSendTopic;
import com.aiya.platform.errcode.ErrorCodeTranslator;
import com.aiya.platform.errcode.support.DBSupportErrorCodeTranslator;
import com.aiya.platform.foundation.spring.AbstractSpringBootStarter;
import com.aiya.platform.redis.RedisClient;
import com.aiya.platform.web.session.holder.RedisSessionHolder;
import com.alibaba.fastjson.parser.ParserConfig;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.logging.LoggingSystem;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.feign.EnableFeignClients;
import org.springframework.cloud.stream.annotation.EnableBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.Order;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

import javax.sql.DataSource;

/**
 * <AUTHOR> by ChenXing on 2017/10/17 0017.
 */
@EnableBinding({EcReceiveTopic.class, EcSendTopic.class})
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
        MongoAutoConfiguration.class,
        MongoDataAutoConfiguration.class})
@EnableDiscoveryClient
@EnableFeignClients
@EnableAspectJAutoProxy
@EnableAsync
public class SpringBootStarter extends AbstractSpringBootStarter {
    static {

        System.clearProperty(LoggingSystem.SYSTEM_PROPERTY);
        ParserConfig.getGlobalInstance().addDeny("org.openqa.selenium");
    }

    public static void main(String[] args) {
        new SpringApplicationBuilder().sources(SpringBootStarter.class).build().run(args);
    }

    @Bean
    protected FilterRegistrationBean registerCharacterFilter() {
        CharacterEncodingFilter filter = new CharacterEncodingFilter();
        FilterRegistrationBean filterRegistrationBean = new FilterRegistrationBean(filter);
        filterRegistrationBean.addUrlPatterns("/*");
        filterRegistrationBean.setAsyncSupported(true);
        filterRegistrationBean.addInitParameter("encoding", "UTF-8");
        filterRegistrationBean.addInitParameter("forceEncoding", "true");
        return filterRegistrationBean;
    }

    @Bean(name = "multipartResolver")
    public CommonsMultipartResolver multipartResolver() {
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
        return multipartResolver;
    }

    @Bean
    @Primary
    @Order(0)
    public DataSourceTransactionManager primaryDataSourceTransactionManager(@Qualifier(value = "imd_ec_datasource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public ErrorCodeTranslator errorCodeTranslator() {
        return new DBSupportErrorCodeTranslator();
    }

    @Bean(name = "superVisorSessionHolder")
    public RedisSessionHolder redisSessionHolder(@Qualifier("supervisorWebSessionRedisClient") RedisClient redisClient) {
        RedisSessionHolder sessionHolder = new RedisSessionHolder("supervisor_user_session_");
        sessionHolder.setCookieKeyId("kpy_supervisor-sessionid");
        sessionHolder.setRedisClient(redisClient);
        return sessionHolder;
    }
}
