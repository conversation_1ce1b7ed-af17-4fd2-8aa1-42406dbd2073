<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:plat-web="http://www.aiya.com/schema/plat-web"
       xmlns="http://www.springframework.org/schema/beans" xmlns:redis="http://www.aiya.com/schema/redis"
       xmlns:plat-dubbo="http://www.aiya.com/schema/plat-dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                          http://www.aiya.com/schema/plat-web http://www.aiya.com/schema/plat-web/plat-web.xsd
                          http://www.aiya.com/schema/redis http://www.aiya.com/schema/redis/redis.xsd
                          http://www.aiya.com/schema/plat-dubbo http://www.aiya.com/schema/plat-dubbo/plat-dubbo.xsd">

    <!-- redis客户端 -->
    <redis:client id="sessionRedisClient"
                  host="${redis.kpyweb.ip}"
                  password="${redis.kpyweb.password}"
                  port="${redis.kpyweb.port}"
                  db-index="${redis.kpyweb.dbIndex}" />

    <plat-web:session session-scope="distribute"
                      redis-client="sessionRedisClient" session-key="kpy_web_foundation_session_">
        <plat-web:session-entry-names constant-class-name="com.aiyounet.cs.api.service.CsSessionApi" />
    </plat-web:session>
    <plat-web:auth-check/>

</beans>
