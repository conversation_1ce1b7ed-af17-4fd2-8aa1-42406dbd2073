<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
       http://www.springframework.org/schema/context
       http://www.springframework.org/schema/context/spring-context.xsd
       http://www.springframework.org/schema/mvc
       http://www.springframework.org/schema/mvc/spring-mvc.xsd">
    <!--web 类工程公共的配置文件-->

    <!-- spring的组件扫描器:扫描Controller -->
    <context:component-scan base-package="com.aiyounet">
        <context:include-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <!--配置 PermissionInterceptor-->
    <bean class="org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping">
        <property name="interceptors">
            <bean class="com.aiyounet.supervisor.auth.PermissionInterceptor"/>
        </property>
        <property name="order" value="-1"/>
    </bean>
    <mvc:interceptors>
        <mvc:interceptor>
            <mvc:mapping path="/**" />
            <bean id="commonInterceptor" class="com.kaopuyun.combine.common.request.component.interceptor.UserInfoInterceptor"></bean>
        </mvc:interceptor>
    </mvc:interceptors>
</beans>
