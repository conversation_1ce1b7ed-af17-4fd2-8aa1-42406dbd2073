<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyounet.cs.container.dao.ContainerImageDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.aiyounet.cs.container.entity.ContainerImageEntity">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="IMAGE_UUID" property="imageUuid" jdbcType="VARCHAR"/>
        <result column="IMAGE_NAME" property="imageName" jdbcType="VARCHAR"/>
        <result column="AUTHOR_NAME" property="authorName" jdbcType="VARCHAR"/>
        <result column="AUTHOR_USER_ID" property="authorUserId" jdbcType="BIGINT"/>
        <result column="COVER_URL" property="coverUrl" jdbcType="VARCHAR"/>
        <result column="INTRODUCTION" property="introduction" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="IMAGE_SIZE" property="imageSize" jdbcType="BIGINT"/>
        <result column="PRICE" property="price" jdbcType="INTEGER"/>
        <result column="CERTIFIED_CREATOR" property="certifiedCreator" jdbcType="BOOLEAN"/>
        <result column="IMAGE_TYPE" property="imageType" jdbcType="VARCHAR"/>
        <result column="IMAGE_STATUS" property="imageStatus" jdbcType="VARCHAR"/>
        <result column="IS_PRIVATE" property="isPrivate" jdbcType="BOOLEAN"/>
        <result column="MARKDOWN_DOC" property="markdownDoc" jdbcType="LONGVARCHAR"/>
        <result column="DOWNLOAD_COUNT" property="downloadCount" jdbcType="INTEGER"/>
        <result column="RATING" property="rating" jdbcType="INTEGER"/>
        <result column="VERSION" property="version" jdbcType="VARCHAR"/>
        <result column="REPOSITORY_URL" property="repositoryUrl" jdbcType="VARCHAR"/>
        <result column="DELETE_TIME" property="deleteTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, IMAGE_UUID, IMAGE_NAME, AUTHOR_NAME, AUTHOR_USER_ID, COVER_URL, INTRODUCTION,
        CREATE_TIME, UPDATE_TIME, IMAGE_SIZE, PRICE, CERTIFIED_CREATOR, IMAGE_TYPE,
        IMAGE_STATUS, IS_PRIVATE, MARKDOWN_DOC, DOWNLOAD_COUNT, RATING, VERSION,
        REPOSITORY_URL, DELETE_TIME
    </sql>

    <!-- 分页查询镜像列表 -->
    <select id="selectImageListByPage" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CONTAINER_IMAGE
        <where>
            IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL
            <if test="isPrivate != null">
                AND IS_PRIVATE = #{isPrivate}
            </if>
            <if test="authorUserId != null">
                AND AUTHOR_USER_ID = #{authorUserId}
            </if>
            <if test="imageType != null and imageType != ''">
                AND IMAGE_TYPE = #{imageType}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                IMAGE_NAME LIKE CONCAT('%', #{keyword}, '%')
                OR AUTHOR_NAME LIKE CONCAT('%', #{keyword}, '%')
                OR INTRODUCTION LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
        ORDER BY DOWNLOAD_COUNT DESC, RATING DESC, CREATE_TIME DESC
    </select>

    <!-- 统计镜像数量 -->
    <select id="countImageList" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM CONTAINER_IMAGE
        <where>
            IMAGE_STATUS = 'active' AND DELETE_TIME IS NULL
            <if test="isPrivate != null">
                AND IS_PRIVATE = #{isPrivate}
            </if>
            <if test="authorUserId != null">
                AND AUTHOR_USER_ID = #{authorUserId}
            </if>
            <if test="imageType != null and imageType != ''">
                AND IMAGE_TYPE = #{imageType}
            </if>
            <if test="keyword != null and keyword != ''">
                AND (
                IMAGE_NAME LIKE CONCAT('%', #{keyword}, '%')
                OR AUTHOR_NAME LIKE CONCAT('%', #{keyword}, '%')
                OR INTRODUCTION LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
        </where>
    </select>

    <!-- 根据标签查询镜像列表 -->
    <select id="selectImagesByTags" resultMap="BaseResultMap">
        SELECT DISTINCT ci.*
        FROM CONTAINER_IMAGE ci
        INNER JOIN CONTAINER_IMAGE_TAG cit ON ci.ID = cit.IMAGE_ID
        <where>
            ci.IMAGE_STATUS = 'active' AND ci.DELETE_TIME IS NULL
            <if test="tagNames != null and tagNames.size() > 0">
                AND cit.TAG_NAME IN
                <foreach collection="tagNames" item="tagName" open="(" separator="," close=")">
                    #{tagName}
                </foreach>
            </if>
        </where>
        ORDER BY ci.DOWNLOAD_COUNT DESC, ci.RATING DESC, ci.CREATE_TIME DESC
    </select>

    <!-- 插入镜像记录 -->
    <insert id="insertImage" parameterType="com.aiyounet.cs.container.entity.ContainerImageEntity"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CONTAINER_IMAGE (
        IMAGE_UUID, IMAGE_NAME, AUTHOR_NAME, AUTHOR_USER_ID, COVER_URL, INTRODUCTION,
        CREATE_TIME, UPDATE_TIME, IMAGE_SIZE, PRICE, CERTIFIED_CREATOR, IMAGE_TYPE,
        IMAGE_STATUS, IS_PRIVATE, MARKDOWN_DOC, DOWNLOAD_COUNT, RATING, VERSION,
        REPOSITORY_URL
        ) VALUES (
        #{imageUuid}, #{imageName}, #{authorName}, #{authorUserId}, #{coverUrl}, #{introduction},
        NOW(), NOW(), #{imageSize}, #{price}, #{certifiedCreator}, #{imageType},
        #{imageStatus}, #{isPrivate}, #{markdownDoc}, #{downloadCount}, #{rating}, #{version},
        #{repositoryUrl}
        )
    </insert>

    <!-- 更新镜像信息 -->
    <update id="updateImage" parameterType="com.aiyounet.cs.container.entity.ContainerImageEntity">
        UPDATE CONTAINER_IMAGE
        <set>
            <if test="imageName != null">IMAGE_NAME = #{imageName},</if>
            <if test="authorName != null">AUTHOR_NAME = #{authorName},</if>
            <if test="coverUrl != null">COVER_URL = #{coverUrl},</if>
            <if test="introduction != null">INTRODUCTION = #{introduction},</if>
            <if test="imageSize != null">IMAGE_SIZE = #{imageSize},</if>
            <if test="price != null">PRICE = #{price},</if>
            <if test="certifiedCreator != null">CERTIFIED_CREATOR = #{certifiedCreator},</if>
            <if test="imageType != null">IMAGE_TYPE = #{imageType},</if>
            <if test="imageStatus != null">IMAGE_STATUS = #{imageStatus},</if>
            <if test="isPrivate != null">IS_PRIVATE = #{isPrivate},</if>
            <if test="markdownDoc != null">MARKDOWN_DOC = #{markdownDoc},</if>
            <if test="rating != null">RATING = #{rating},</if>
            <if test="version != null">VERSION = #{version},</if>
            <if test="repositoryUrl != null">REPOSITORY_URL = #{repositoryUrl},</if>
            UPDATE_TIME = NOW()
        </set>
        WHERE ID = #{id}
    </update>

</mapper>
