<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aiyounet.cs.container.dao.ContainerImageTagDAO">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.aiyounet.cs.container.entity.ContainerImageTagEntity">
        <id column="ID" property="id" jdbcType="BIGINT"/>
        <result column="IMAGE_ID" property="imageId" jdbcType="BIGINT"/>
        <result column="TAG_NAME" property="tagName" jdbcType="VARCHAR"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="TAG_TYPE" property="tagType" jdbcType="VARCHAR"/>
        <result column="TAG_WEIGHT" property="tagWeight" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        ID, IMAGE_ID, TAG_NAME, CREATE_TIME, TAG_TYPE, TAG_WEIGHT
    </sql>

    <!-- 根据镜像ID列表批量查询标签 -->
    <select id="selectTagsByImageIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM CONTAINER_IMAGE_TAG
        WHERE IMAGE_ID IN
        <foreach collection="imageIds" item="imageId" open="(" separator="," close=")">
            #{imageId}
        </foreach>
        ORDER BY IMAGE_ID ASC, TAG_WEIGHT DESC, TAG_NAME ASC
    </select>

    <!-- 根据标签名称列表查询关联的镜像ID列表 -->
    <select id="selectImageIdsByTagNames" resultType="java.lang.Long">
        SELECT DISTINCT IMAGE_ID
        FROM CONTAINER_IMAGE_TAG
        WHERE TAG_NAME IN
        <foreach collection="tagNames" item="tagName" open="(" separator="," close=")">
            #{tagName}
        </foreach>
    </select>

    <!-- 批量插入镜像标签关联 -->
    <insert id="batchInsertImageTags" parameterType="java.util.List">
        INSERT INTO CONTAINER_IMAGE_TAG (IMAGE_ID, TAG_NAME, CREATE_TIME, TAG_TYPE, TAG_WEIGHT)
        VALUES
        <foreach collection="imageTags" item="imageTag" separator=",">
            (#{imageTag.imageId}, #{imageTag.tagName}, NOW(), #{imageTag.tagType}, #{imageTag.tagWeight})
        </foreach>
    </insert>

    <!-- 插入镜像标签关联 -->
    <insert id="insertImageTag" parameterType="com.aiyounet.cs.container.entity.ContainerImageTagEntity"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO CONTAINER_IMAGE_TAG (IMAGE_ID, TAG_NAME, CREATE_TIME, TAG_TYPE, TAG_WEIGHT)
        VALUES (#{imageId}, #{tagName}, NOW(), #{tagType}, #{tagWeight})
    </insert>

</mapper>
