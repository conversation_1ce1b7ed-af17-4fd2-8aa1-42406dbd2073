<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!--<include resource="org/springframework/boot/logging/logback/defaults.xml"/>-->

    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="logstashHost" source="logging.logstash.host"
                    defaultValue="************:4560"/> <!-- 必须设置默认值 否则启动不成功 -->

    <property name="CONSOLE_LOG_PATTERN"
              value="%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%mdc{trace_id},%mdc{span_id}] %logger{36} - %msg%n"/>
    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type
             ch.qos.logback.classic.encoder.PatternLayoutEncoder by default -->
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 集中化日志输出 -->
    <appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <!-- 替换为实际的接入地址 -->
        <!-- 不同环境接入地址不一样，应该采用环境变量引入 -->
        <destination>${logstashHost}</destination>

        <!-- encoder is required -->
        <encoder class="net.logstash.logback.encoder.LogstashEncoder">
            <customFields>{"app_name":"${appName}"}</customFields>
            <includeContext>false</includeContext>
        </encoder>
    </appender>

    <!-- 日志生成位置 -->


<!--    <appender name="file" class="ch.qos.logback.core.rolling.RollingFileAppender">-->
<!--        <File>./opt/logs/tomcat/xcloud-ec-api-server/log.log</File>-->
<!--        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">-->
<!--            &lt;!&ndash; daily rollover 保存历史记录到这个文件夹一日起为后缀 &ndash;&gt;-->
<!--            <FileNamePattern>./opt/logs/tomcat/xcloud-ec-api-server/%d{yyyyMMdd}xcloud-ec-api-server.log</FileNamePattern>-->
<!--            &lt;!&ndash; keep 30 days' worth of history &ndash;&gt;-->
<!--            &lt;!&ndash;<maxHistory>30</maxHistory>&ndash;&gt;-->
<!--        </rollingPolicy>-->
<!--        <encoder>-->
<!--            <Pattern>${CONSOLE_LOG_PATTERN}</Pattern>-->
<!--            <charset>UTF-8</charset> &lt;!&ndash; 此处设置字符集 &ndash;&gt;-->
<!--        </encoder>-->
<!--    </appender>-->

    <!-- 如有需要可以额外配置文件输出 -->

    <!-- 日志级别配置 -->
    <root level="info">
<!--        <appender-ref ref="file"/>-->
        <appender-ref ref="console"/>
        <appender-ref ref="logstash"/>
    </root>
</configuration>
