<configuration>

    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <!-- encoders are assigned the type ch.qos.logback.classic.encoder.PatternLayoutEncoder
            by default -->
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!--
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>z:/aaa.log</file>

        <encoder>
          <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
      </appender>

    <logger name="org.springframework" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.apache.zookeeper" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.I0Itec" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="com.alibaba.dubbo" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="com.alibaba.druid" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.apache.ibatis" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.mybatis.spring" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="org.jboss.netty" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <logger name="com.aiya" level="DEBUG">
        <appender-ref ref="STDOUT" />
    </logger>

    <root level="debug">
        <appender-ref ref="STDOUT" />
    </root>
     -->
</configuration>