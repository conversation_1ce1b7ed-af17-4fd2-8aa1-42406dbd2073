#config for server container
#see org.springframework.boot.autoconfigure.web.ServerProperties.java
server.port=10088
dubbo.protocol.telnet=exit
dubbo.protocol.name=dubbo
DESENSITIZATION=false
#xxl.job.accessToken =
#xxl.job.executor.appname = ${spring.cloud.config.name}
#xxl.job.executor.ip =
#xxl.job.executor.port = 0
#xxl.job.executor.logpath = /data/applogs/xxl-job/jobhandler
#xxl.job.executor.logretentiondays = -1
#
## xxl-·þÎñÆ÷µØÖ·£¬¸÷»·¾³²»Ò»Ñù
#xxl.job.admin.addresses = http://***********:15673/xxl-job-admin/