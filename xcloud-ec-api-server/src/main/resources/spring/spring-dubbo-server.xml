<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:plat-dubbo="http://www.aiya.com/schema/plat-dubbo"
       xmlns:plat-api="http://www.aiya.com/schema/plat-api"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
    http://www.springframework.org/schema/beans/spring-beans.xsd
    http://www.aiya.com/schema/plat-dubbo
    http://www.aiya.com/schema/plat-dubbo/plat-dubbo.xsd
    http://www.aiya.com/schema/plat-api
    http://www.aiya.com/schema/plat-api/plat-api.xsd">


    <plat-dubbo:global dubbo-cache-file="kpy-ec-api-server"/>

    <plat-api:service application-name="kpy-ec-api-server">
        <plat-api:error-code-prefix constant-class-name="com.aiya.kpy.ec.api.constant.ErrorCodes" constant-class-field="PREFIX"/>
        <!--<plat-api:method-invoker type="after" class="com.aiya.kpy.rmc.support.ec.ResourceAuthorityCheckMethodInvoker" order="20000"/>-->
        <!--<plat-api:method-invoker type="after" class="com.aiya.kpy.api.zonesupport.ZoneAPISupportCheckMethodInvoker" order="20001"/>-->
        <!--<plat-api:method-invoker type="after" class="com.aiya.kpy.rmc.support.ec.ResourceInjectCheckMethodInvoker" order="20002"/>-->
    </plat-api:service>
</beans>