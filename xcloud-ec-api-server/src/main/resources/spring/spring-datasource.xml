<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:database="http://www.aiya.com/schema/database"
       xmlns:mybatis-orm="http://www.aiya.com/schema/mybatis-orm"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                        http://www.aiya.com/schema/database
						http://www.aiya.com/schema/database/database.xsd
						http://www.aiya.com/schema/mybatis-orm
						http://www.aiya.com/schema/mybatis-orm/mybatis-orm.xsd">

    <!--<database:mysql-data-source id="imd_log_datasource"
                                db-ip="${mysql.log.ip}"
                                db-port="${mysql.log.port}"
                                db-name="${mysql.log.dbname}"
                                db-username="${mysql.log.username}"
                                db-password="${mysql.log.password}"
                                pool-max="100"
                                pool-min="1"
                                force-rollback-active-connection="true"/>-->

    <database:mysql-data-source id="imd_ec_datasource"
                                db-ip="${mysql.ec.ip}"
                                db-port="${mysql.ec.port}"
                                db-name="${mysql.ec.dbname}"
                                db-username="${mysql.ec.username}"
                                db-password="${mysql.ec.password}"
                                pool-max="100"
                                pool-min="1"
                                force-rollback-active-connection="true"/>

    <!--<database:mysql-data-source id="config_datasource_web"
                                db-name="${mysql.config.dbname}"
                                db-ip="${mysql.config.ip}"
                                db-port="${mysql.config.port}"
                                db-username="${mysql.config.username}"
                                db-password="${mysql.config.password}"
                                pool-max="${mysql.config.maxActive}"
                                pool-min="1"
                                force-rollback-active-connection="true"
                                enable-sql-monitor="false"/>-->

    <!--<mybatis-orm:auto-config data-source="imd_log_datasource" dao-base-package="com.aiya.kpy.**.foundation"/>-->
    <mybatis-orm:auto-config data-source="imd_ec_datasource" dao-base-package="com.aiya.kpy.**.foundation"/>
    <!--<mybatis-orm:auto-config data-source="config_datasource_web" -dao-base-package="com.aiya.**.entity,com.aiyounet.**.bean"/>-->


</beans>