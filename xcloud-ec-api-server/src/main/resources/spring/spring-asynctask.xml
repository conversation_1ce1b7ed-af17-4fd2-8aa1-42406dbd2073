<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:asynctask="http://www.aiya.com/schema/asynctask"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
     http://www.aiya.com/schema/asynctask
     http://www.aiya.com/schema/asynctask/async-task.xsd">

    <!-- 异步任务发送者 -->
    <asynctask:sender id="asyncTaskSender" redis-client="asyncTaskRedisClient">
        <asynctask:topic
                class-name="com.aiya.kpy.common.constants.AsyncTaskTopicName"
                field-name="ECS_ASYNC_TASK"/>
        <asynctask:debug enable="false" client-node-id="chenxing"/>
    </asynctask:sender>

    <!-- 异步任务接收者 -->
    <asynctask:receiver redis-client="asyncTaskRedisClient">
        <asynctask:topic
                class-name="com.aiya.kpy.common.constants.AsyncTaskTopicName"
                field-name="ECS_ASYNC_TASK"/>
        <asynctask:debug enable="false" client-node-id="chenxing"/>
    </asynctask:receiver>
</beans>