<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:database="http://www.aiya.com/schema/database" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:mybatis-orm="http://www.aiya.com/schema/mybatis-orm"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
                        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
                        http://www.aiya.com/schema/database http://www.aiya.com/schema/database/database.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd   http://www.aiya.com/schema/mybatis-orm http://www.aiya.com/schema/mybatis-orm/mybatis-orm.xsd">

    <!--tomcat jdbc pool数据源配置 -->
    <database:mysql-data-source id="dataSource_web"
                                db-name="${mysql.kpyweb.dbname}"
                                db-ip="${mysql.kpyweb.ip}"
                                db-port="${mysql.kpyweb.port}"
                                db-username="${mysql.kpyweb.username}"
                                db-password="${mysql.kpyweb.password}"
                                pool-max="${mysql.kpyweb.maxActive}"
                                pool-min="1"
                                enable-sql-monitor="false"/>

    <database:mysql-data-source id="log_datasource_web"
                                db-name="${mysql.log.dbname}"
                                db-ip="${mysql.log.ip}"
                                db-port="${mysql.log.port}"
                                db-username="${mysql.log.username}"
                                db-password="${mysql.log.password}"
                                pool-max="${mysql.log.maxActive}"
                                pool-min="1"
                                enable-sql-monitor="false"/>

    <!-- 新版mybatis注册,想要把这些去掉，就要把报名改成com.aiya... -->
    <mybatis-orm:auto-config data-source="dataSource_web"
                             dao-base-package="com.aiyounet.**.dao"
                             enums-base-package="com.aiya.**.enums,com.aiyounet.**.enums"
                             entity-base-package="com.aiya.**.entity,com.aiyounet.**.bean"
                             dao-scan-stragery="all"/>


    <!-- 配置hibernate -->
    <bean id="sessionFactory" class="org.springframework.orm.hibernate4.LocalSessionFactoryBean">
        <property name="dataSource" ref="dataSource_web"/>
        <!-- hibernate扫描实体包 -->
        <property name="packagesToScan">
            <list>
                <value>com.aiyounet.cs.api.bean</value>
            </list>
        </property>
    </bean>

    <!-- 配置Hibernate事务管理器 -->
    <bean id="transactionManager" class="org.springframework.orm.hibernate4.HibernateTransactionManager">
        <property name="sessionFactory" ref="sessionFactory"/>
    </bean>

    <!-- ============================== 事务代理拦截器的配置 ============================= -->
    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="*" propagation="REQUIRED"/>
        </tx:attributes>
    </tx:advice>
</beans>
