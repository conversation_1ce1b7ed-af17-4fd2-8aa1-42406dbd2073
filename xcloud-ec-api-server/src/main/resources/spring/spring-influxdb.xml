<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:influxdb="http://www.aiya.com/schema/influxdb" xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
		http://www.aiya.com/schema/influxdb http://www.aiya.com/schema/influxdb/influxdb.xsd">

    <influxdb:mapper entity-base-package="com.aiya.kpy.**.entity.influxdb" dao-base-package="com.aiya.kpy.**.dao.influxdb">
        <influxdb:datasource database="${influxdb.ec.datasbase}"
                             host="${influxdb.ec.ip}"
                             port="${influxdb.ec.port}"
                             scheme="${influxdb.ec.scheme}"
                             username="${influxdb.ec.username}"
                             password="${influxdb.ec.password}"/>
        <influxdb:config enable-batch="true"/>
    </influxdb:mapper>

    <influxdb:mapper entity-base-package="com.aiya.kpy.**.entity.statistics.influxdb"
                     dao-base-package="com.aiya.kpy.**.dao.statistics.influxdb">

        <influxdb:datasource database="${influxdb.statistics.datasbase}"
                             host="${influxdb.statistics.ip}"
                             port="${influxdb.statistics.port}"
                             scheme="${influxdb.statistics.scheme}"
                             username="${influxdb.statistics.username}"
                             password="${influxdb.statistics.password}"/>
        <influxdb:config enable-batch="false"/>
    </influxdb:mapper>
</beans>