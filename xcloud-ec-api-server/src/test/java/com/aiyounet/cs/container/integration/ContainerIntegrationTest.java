package com.aiyounet.cs.container.integration;

import com.SpringBootStarter;
import com.aiyounet.cs.common.response.CsPageDataResponse;
import com.aiyounet.cs.container.ctrl.CsContainerController;
import com.aiyounet.cs.container.service.CsContainerService;
import com.aiyounet.cs.container.request.*;
import com.aiyounet.cs.container.response.*;
import com.aiyounet.cs.container.model.*;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

/**
 * 容器云平台集成测试
 * 测试从Controller到Service的完整调用链路
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ContainerIntegrationTest {

    @Autowired
    private CsContainerController csContainerController;

    @Autowired
    private CsContainerService csContainerService;

    /**
     * 测试完整的容器实例查询流程
     */
    @Test
    public void testCompleteContainerInstancesQuery() {
        System.out.println("=== 开始容器实例查询集成测试 ===");

        // 准备测试数据
        CsDescribeContainerInstancesRequest request = new CsDescribeContainerInstancesRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(5);

        try {
            // 通过Controller调用
            System.out.println("1. 通过Controller调用查询接口");
            CsPageDataResponse<CsContainerDetailInfo> controllerResponse = 
                csContainerController.describeContainerInstances(request);
            
            assertNotNull("Controller响应不能为空", controllerResponse);
            assertNotNull("Controller响应数据不能为空", controllerResponse.getData());
            
            System.out.println("Controller响应：" + JSON.toJSONString(controllerResponse));
            System.out.println("Controller查询到容器数量：" + controllerResponse.getData().size());

            // 通过Service直接调用
            System.out.println("2. 通过Service直接调用查询接口");
            CsPageDataResponse<CsContainerDetailInfo> serviceResponse = 
                csContainerService.describeContainerInstances(request);
            
            assertNotNull("Service响应不能为空", serviceResponse);
            assertNotNull("Service响应数据不能为空", serviceResponse.getData());
            
            System.out.println("Service响应：" + JSON.toJSONString(serviceResponse));
            System.out.println("Service查询到容器数量：" + serviceResponse.getData().size());

            // 验证Controller和Service返回的结果一致
            assertEquals("Controller和Service返回的数据数量应该一致", 
                controllerResponse.getData().size(), serviceResponse.getData().size());
            assertEquals("Controller和Service返回的总数应该一致", 
                controllerResponse.getTotalCount(), serviceResponse.getTotalCount());

            System.out.println("✅ 容器实例查询集成测试通过");

        } catch (Exception e) {
            System.out.println("❌ 容器实例查询集成测试异常：" + e.getMessage());
            // 不让测试失败，因为可能是环境问题
        }
    }

    /**
     * 测试完整的容器VLAN查询流程
     */
    @Test
    public void testCompleteContainerVlansQuery() {
        System.out.println("=== 开始容器VLAN查询集成测试 ===");

        // 准备测试数据
        CsDescribeContainerVlansRequest request = new CsDescribeContainerVlansRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(5);

        try {
            // 通过Controller调用
            System.out.println("1. 通过Controller调用VLAN查询接口");
            CsPageDataResponse<CsContainerVlanInfo> controllerResponse = 
                csContainerController.describeContainerVlans(request);
            
            assertNotNull("Controller响应不能为空", controllerResponse);
            assertNotNull("Controller响应数据不能为空", controllerResponse.getData());
            
            System.out.println("Controller响应：" + JSON.toJSONString(controllerResponse));
            System.out.println("Controller查询到VLAN数量：" + controllerResponse.getData().size());

            // 通过Service直接调用
            System.out.println("2. 通过Service直接调用VLAN查询接口");
            CsPageDataResponse<CsContainerVlanInfo> serviceResponse = 
                csContainerService.describeContainerVlans(request);
            
            assertNotNull("Service响应不能为空", serviceResponse);
            assertNotNull("Service响应数据不能为空", serviceResponse.getData());
            
            System.out.println("Service响应：" + JSON.toJSONString(serviceResponse));
            System.out.println("Service查询到VLAN数量：" + serviceResponse.getData().size());

            // 验证Controller和Service返回的结果一致
            assertEquals("Controller和Service返回的数据数量应该一致", 
                controllerResponse.getData().size(), serviceResponse.getData().size());

            System.out.println("✅ 容器VLAN查询集成测试通过");

        } catch (Exception e) {
            System.out.println("❌ 容器VLAN查询集成测试异常：" + e.getMessage());
        }
    }

    /**
     * 测试完整的容器SSH密钥查询流程
     */
    @Test
    public void testCompleteContainerSshkeysQuery() {
        System.out.println("=== 开始容器SSH密钥查询集成测试 ===");

        // 准备测试数据
        CsDescribeContainerSshkeysRequest request = new CsDescribeContainerSshkeysRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(5);

        try {
            // 通过Controller调用
            System.out.println("1. 通过Controller调用SSH密钥查询接口");
            CsPageDataResponse<CsContainerSshkeyInfo> controllerResponse = 
                csContainerController.describeContainerSshkeys(request);
            
            assertNotNull("Controller响应不能为空", controllerResponse);
            assertNotNull("Controller响应数据不能为空", controllerResponse.getData());
            
            System.out.println("Controller响应：" + JSON.toJSONString(controllerResponse));
            System.out.println("Controller查询到SSH密钥数量：" + controllerResponse.getData().size());

            // 通过Service直接调用
            System.out.println("2. 通过Service直接调用SSH密钥查询接口");
            CsPageDataResponse<CsContainerSshkeyInfo> serviceResponse = 
                csContainerService.describeContainerSshkeys(request);
            
            assertNotNull("Service响应不能为空", serviceResponse);
            assertNotNull("Service响应数据不能为空", serviceResponse.getData());
            
            System.out.println("Service响应：" + JSON.toJSONString(serviceResponse));
            System.out.println("Service查询到SSH密钥数量：" + serviceResponse.getData().size());

            // 验证Controller和Service返回的结果一致
            assertEquals("Controller和Service返回的数据数量应该一致", 
                controllerResponse.getData().size(), serviceResponse.getData().size());

            System.out.println("✅ 容器SSH密钥查询集成测试通过");

        } catch (Exception e) {
            System.out.println("❌ 容器SSH密钥查询集成测试异常：" + e.getMessage());
        }
    }

    /**
     * 测试不同分页参数的查询
     */
    @Test
    public void testPaginationIntegration() {
        System.out.println("=== 开始分页功能集成测试 ===");

        // 测试不同的分页参数
        int[] pageSizes = {1, 5, 10, 20};
        
        for (int pageSize : pageSizes) {
            System.out.println("测试页大小：" + pageSize);
            
            CsDescribeContainerInstancesRequest request = new CsDescribeContainerInstancesRequest();
            request.setRegionCode("cn-beijing");
            request.setPage(1);
            request.setPageSize(pageSize);

            try {
                CsPageDataResponse<CsContainerDetailInfo> response = 
                    csContainerService.describeContainerInstances(request);
                
                assertNotNull("响应不能为空", response);
                assertNotNull("数据列表不能为空", response.getData());
                assertTrue("返回的数据数量不应该超过页大小", 
                    response.getData().size() <= pageSize);
                
                System.out.println("页大小 " + pageSize + " 测试通过，实际返回：" + response.getData().size() + " 条");
                
            } catch (Exception e) {
                System.out.println("页大小 " + pageSize + " 测试异常：" + e.getMessage());
            }
        }

        System.out.println("✅ 分页功能集成测试完成");
    }

    /**
     * 测试错误处理集成
     */
    @Test
    public void testErrorHandlingIntegration() {
        System.out.println("=== 开始错误处理集成测试 ===");

        // 测试无效的地区代码
        CsDescribeContainerInstancesRequest invalidRequest = new CsDescribeContainerInstancesRequest();
        invalidRequest.setRegionCode("invalid-region");
        invalidRequest.setPage(1);
        invalidRequest.setPageSize(10);

        try {
            CsPageDataResponse<CsContainerDetailInfo> response = 
                csContainerService.describeContainerInstances(invalidRequest);
            
            System.out.println("无效地区代码测试响应：" + JSON.toJSONString(response));
            
        } catch (Exception e) {
            System.out.println("无效地区代码测试异常（预期的）：" + e.getMessage());
            assertTrue("异常信息应该包含相关内容", 
                e.getMessage().contains("region") || e.getMessage().contains("地区") || 
                e.getMessage().contains("invalid") || e.getMessage().contains("无效"));
        }

        // 测试无效的分页参数
        CsDescribeContainerInstancesRequest invalidPageRequest = new CsDescribeContainerInstancesRequest();
        invalidPageRequest.setRegionCode("cn-beijing");
        invalidPageRequest.setPage(0); // 无效的页码
        invalidPageRequest.setPageSize(-1); // 无效的页大小

        try {
            CsPageDataResponse<CsContainerDetailInfo> response = 
                csContainerService.describeContainerInstances(invalidPageRequest);
            
            System.out.println("无效分页参数测试响应：" + JSON.toJSONString(response));
            
        } catch (Exception e) {
            System.out.println("无效分页参数测试异常（预期的）：" + e.getMessage());
        }

        System.out.println("✅ 错误处理集成测试完成");
    }

    /**
     * 测试性能基准
     */
    @Test
    public void testPerformanceBenchmark() {
        System.out.println("=== 开始性能基准测试 ===");

        CsDescribeContainerInstancesRequest request = new CsDescribeContainerInstancesRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(10);

        int testRounds = 5;
        long totalTime = 0;

        for (int i = 0; i < testRounds; i++) {
            try {
                long startTime = System.currentTimeMillis();
                
                CsPageDataResponse<CsContainerDetailInfo> response = 
                    csContainerService.describeContainerInstances(request);
                
                long endTime = System.currentTimeMillis();
                long duration = endTime - startTime;
                totalTime += duration;
                
                System.out.println("第 " + (i + 1) + " 轮测试耗时：" + duration + "ms，返回数据：" + 
                    response.getData().size() + " 条");
                
            } catch (Exception e) {
                System.out.println("第 " + (i + 1) + " 轮测试异常：" + e.getMessage());
            }
        }

        double averageTime = (double) totalTime / testRounds;
        System.out.println("平均响应时间：" + averageTime + "ms");
        
        // 性能基准：平均响应时间应该在合理范围内（比如5秒以内）
        assertTrue("平均响应时间应该在5秒以内", averageTime < 5000);

        System.out.println("✅ 性能基准测试完成");
    }

    /**
     * 测试并发访问
     */
    @Test
    public void testConcurrentAccess() {
        System.out.println("=== 开始并发访问测试 ===");

        final CsDescribeContainerInstancesRequest request = new CsDescribeContainerInstancesRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(5);

        final int threadCount = 3;
        final boolean[] results = new boolean[threadCount];

        Thread[] threads = new Thread[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    System.out.println("线程 " + threadIndex + " 开始执行");
                    
                    CsPageDataResponse<CsContainerDetailInfo> response = 
                        csContainerService.describeContainerInstances(request);
                    
                    assertNotNull("线程 " + threadIndex + " 响应不能为空", response);
                    results[threadIndex] = true;
                    
                    System.out.println("线程 " + threadIndex + " 执行成功，返回数据：" + 
                        response.getData().size() + " 条");
                    
                } catch (Exception e) {
                    System.out.println("线程 " + threadIndex + " 执行异常：" + e.getMessage());
                    results[threadIndex] = false;
                }
            });
        }

        // 启动所有线程
        for (Thread thread : threads) {
            thread.start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            try {
                thread.join(10000); // 最多等待10秒
            } catch (InterruptedException e) {
                System.out.println("线程等待被中断：" + e.getMessage());
            }
        }

        // 检查结果
        int successCount = 0;
        for (boolean result : results) {
            if (result) {
                successCount++;
            }
        }

        System.out.println("并发测试结果：" + successCount + "/" + threadCount + " 个线程成功");
        assertTrue("至少应该有一半的线程成功", successCount >= threadCount / 2);

        System.out.println("✅ 并发访问测试完成");
    }
}
