package com.aiyounet.cs.container.ctrl;

import com.SpringBootStarter;
import com.aiyounet.cs.common.response.CsPageDataResponse;
import com.aiyounet.cs.container.service.CsContainerService;
import com.aiyounet.cs.container.request.*;
import com.aiyounet.cs.container.response.*;
import com.aiyounet.cs.container.model.*;
import com.alibaba.fastjson.JSON;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * 容器云管理控制器单元测试
 * 测试 CsContainerController 的所有接口方法
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class CsContainerControllerTest {

    private MockMvc mockMvc;

    @Mock
    private CsContainerService csContainerService;

    @InjectMocks
    private CsContainerController csContainerController;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(csContainerController).build();
    }

    /**
     * 测试创建容器实例接口
     */
    @Test
    public void testCreateContainerInstance() throws Exception {
        // 准备测试数据
        CsCreateContainerInstanceRequest request = new CsCreateContainerInstanceRequest();
        request.setRegionCode("cn-beijing");
        request.setFlavorUuid("flavor-uuid-123");
        request.setImagePath("/test/image/path");
        request.setZoneUuid("zone-uuid-123");
        request.setDescription("测试容器实例");

        CsCreateContainerInstanceResponse response = new CsCreateContainerInstanceResponse();
        response.setRequestId("req-123456");
        response.setHostId("host-123456");
        response.setTaskUuid("task-uuid-123");

        // Mock 服务层方法
        when(csContainerService.createContainerInstance(any(CsCreateContainerInstanceRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/instances")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.hostId").value("host-123456"))
                .andExpect(jsonPath("$.taskUuid").value("task-uuid-123"));
    }

    /**
     * 测试查询容器实例列表接口
     */
    @Test
    public void testDescribeContainerInstances() throws Exception {
        // 准备测试数据
        CsDescribeContainerInstancesRequest request = new CsDescribeContainerInstancesRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(10);

        List<CsContainerDetailInfo> dataList = new ArrayList<>();
        CsContainerDetailInfo containerInfo = new CsContainerDetailInfo();
        containerInfo.setUuid("container-uuid-123");
        containerInfo.setDescription("测试容器");
        containerInfo.setState(1);
        dataList.add(containerInfo);

        CsPageDataResponse<CsContainerDetailInfo> response = new CsPageDataResponse<>(dataList, 1L, 1L);

        // Mock 服务层方法
        when(csContainerService.describeContainerInstances(any(CsDescribeContainerInstancesRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/instances/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].uuid").value("container-uuid-123"))
                .andExpect(jsonPath("$.totalCount").value(1));
    }

    /**
     * 测试删除容器实例接口
     */
    @Test
    public void testDeleteContainerInstance() throws Exception {
        // 准备测试数据
        CsDeleteContainerInstanceRequest request = new CsDeleteContainerInstanceRequest();
        request.setRegionCode("cn-beijing");
        request.setUuid("container-uuid-123");

        CsDeleteContainerInstanceResponse response = new CsDeleteContainerInstanceResponse();
        response.setRequestId("req-123456");
        response.setTaskUuid("task-uuid-123");

        // Mock 服务层方法
        when(csContainerService.deleteContainerInstance(any(CsDeleteContainerInstanceRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/instances/delete")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.taskUuid").value("task-uuid-123"));
    }

    /**
     * 测试启动容器实例接口
     */
    @Test
    public void testStartContainerInstance() throws Exception {
        // 准备测试数据
        CsStartContainerInstanceRequest request = new CsStartContainerInstanceRequest();
        request.setRegionCode("cn-beijing");
        request.setUuid("container-uuid-123");

        CsStartContainerInstanceResponse response = new CsStartContainerInstanceResponse();
        response.setRequestId("req-123456");
        response.setTaskUuid("task-uuid-123");

        // Mock 服务层方法
        when(csContainerService.startContainerInstance(any(CsStartContainerInstanceRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/instances/start")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.taskUuid").value("task-uuid-123"));
    }

    /**
     * 测试停止容器实例接口
     */
    @Test
    public void testStopContainerInstance() throws Exception {
        // 准备测试数据
        CsStopContainerInstanceRequest request = new CsStopContainerInstanceRequest();
        request.setRegionCode("cn-beijing");
        request.setUuid("container-uuid-123");

        CsStopContainerInstanceResponse response = new CsStopContainerInstanceResponse();
        response.setRequestId("req-123456");
        response.setTaskUuid("task-uuid-123");

        // Mock 服务层方法
        when(csContainerService.stopContainerInstance(any(CsStopContainerInstanceRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/instances/stop")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.taskUuid").value("task-uuid-123"));
    }

    /**
     * 测试创建容器网卡接口
     */
    @Test
    public void testCreateContainerNic() throws Exception {
        // 准备测试数据
        CsCreateContainerNicRequest request = new CsCreateContainerNicRequest();
        request.setRegionCode("cn-beijing");
        request.setContainerUuid("container-uuid-123");

        CsCreateContainerNicResponse response = new CsCreateContainerNicResponse();
        response.setRequestId("req-123456");
        response.setTaskUuid("task-uuid-123");

        // Mock 服务层方法
        when(csContainerService.createContainerNic(any(CsCreateContainerNicRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/nics")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.taskUuid").value("task-uuid-123"));
    }

    /**
     * 测试创建容器VLAN接口
     */
    @Test
    public void testCreateContainerVlan() throws Exception {
        // 准备测试数据
        CsCreateContainerVlanRequest request = new CsCreateContainerVlanRequest();
        request.setRegionCode("cn-beijing");
        request.setVlanName("test-vlan");

        CsCreateContainerVlanResponse response = new CsCreateContainerVlanResponse();
        response.setRequestId("req-123456");
        response.setVlanUuid("vlan-uuid-123");

        // Mock 服务层方法
        when(csContainerService.createContainerVlan(any(CsCreateContainerVlanRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/vlans")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.vlanUuid").value("vlan-uuid-123"));
    }

    /**
     * 测试查询容器VLAN列表接口
     */
    @Test
    public void testDescribeContainerVlans() throws Exception {
        // 准备测试数据
        CsDescribeContainerVlansRequest request = new CsDescribeContainerVlansRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(10);

        List<CsContainerVlanInfo> dataList = new ArrayList<>();
        CsContainerVlanInfo vlanInfo = new CsContainerVlanInfo();
        vlanInfo.setVlanUuid("vlan-uuid-123");
        vlanInfo.setVlanName("test-vlan");
        dataList.add(vlanInfo);

        CsPageDataResponse<CsContainerVlanInfo> response = new CsPageDataResponse<>(dataList, 1L, 1L);

        // Mock 服务层方法
        when(csContainerService.describeContainerVlans(any(CsDescribeContainerVlansRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/vlans/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].vlanUuid").value("vlan-uuid-123"))
                .andExpect(jsonPath("$.totalCount").value(1));
    }

    /**
     * 测试创建容器SSH密钥接口
     */
    @Test
    public void testCreateContainerSshkey() throws Exception {
        // 准备测试数据
        CsCreateContainerSshkeyRequest request = new CsCreateContainerSshkeyRequest();
        request.setRegionCode("cn-beijing");
        request.setKeyName("test-key");

        CsCreateContainerSshkeyResponse response = new CsCreateContainerSshkeyResponse();
        response.setRequestId("req-123456");
        response.setKeyUuid("key-uuid-123");

        // Mock 服务层方法
        when(csContainerService.createContainerSshkey(any(CsCreateContainerSshkeyRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/sshkeys")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.requestId").value("req-123456"))
                .andExpect(jsonPath("$.keyUuid").value("key-uuid-123"));
    }

    /**
     * 测试查询容器SSH密钥列表接口
     */
    @Test
    public void testDescribeContainerSshkeys() throws Exception {
        // 准备测试数据
        CsDescribeContainerSshkeysRequest request = new CsDescribeContainerSshkeysRequest();
        request.setRegionCode("cn-beijing");
        request.setPage(1);
        request.setPageSize(10);

        List<CsContainerSshkeyInfo> dataList = new ArrayList<>();
        CsContainerSshkeyInfo sshkeyInfo = new CsContainerSshkeyInfo();
        sshkeyInfo.setKeyUuid("key-uuid-123");
        sshkeyInfo.setKeyName("test-key");
        dataList.add(sshkeyInfo);

        CsPageDataResponse<CsContainerSshkeyInfo> response = new CsPageDataResponse<>(dataList, 1L, 1L);

        // Mock 服务层方法
        when(csContainerService.describeContainerSshkeys(any(CsDescribeContainerSshkeysRequest.class)))
                .thenReturn(response);

        // 执行测试
        mockMvc.perform(post("/api/v1/container/sshkeys/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON_UTF8))
                .andExpect(jsonPath("$.data").isArray())
                .andExpect(jsonPath("$.data[0].keyUuid").value("key-uuid-123"))
                .andExpect(jsonPath("$.totalCount").value(1));
    }

    /**
     * 测试参数验证失败的情况
     */
    @Test
    public void testCreateContainerInstanceWithInvalidParams() throws Exception {
        // 准备无效的测试数据（缺少必填字段）
        CsCreateContainerInstanceRequest request = new CsCreateContainerInstanceRequest();
        // 不设置必填字段 regionCode

        // 执行测试，期望返回400错误
        mockMvc.perform(post("/api/v1/container/instances")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(request)))
                .andExpect(status().isBadRequest());
    }
}
