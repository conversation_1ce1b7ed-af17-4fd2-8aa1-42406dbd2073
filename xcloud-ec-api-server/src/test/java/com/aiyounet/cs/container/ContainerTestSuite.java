package com.aiyounet.cs.container;

import com.aiyounet.cs.container.ctrl.CsContainerControllerTest;
import com.aiyounet.cs.container.service.CsContainerServiceTest;
import com.aiyounet.cs.container.service.impl.CsContainerServiceImplTest;
import com.aiyounet.cs.container.integration.ContainerIntegrationTest;
import org.junit.runner.RunWith;
import org.junit.runners.Suite;

/**
 * 容器云平台测试套件
 * 运行所有容器相关的单元测试和集成测试
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@RunWith(Suite.class)
@Suite.SuiteClasses({
    CsContainerControllerTest.class,        // Controller层单元测试
    CsContainerServiceTest.class,           // Service层单元测试
    CsContainerServiceImplTest.class,       // Service实现类单元测试
    ContainerIntegrationTest.class          // 集成测试
})
public class ContainerTestSuite {
    
    /**
     * 测试套件说明：
     * 
     * 1. CsContainerControllerTest - Controller层单元测试
     *    - 测试所有API接口的HTTP请求处理
     *    - 使用MockMvc模拟HTTP请求
     *    - 验证请求参数和响应格式
     *    - 测试参数验证和错误处理
     * 
     * 2. CsContainerServiceTest - Service层单元测试
     *    - 测试业务逻辑层的所有方法
     *    - 使用真实的Spring容器
     *    - 验证业务逻辑的正确性
     *    - 测试参数验证和异常处理
     * 
     * 3. CsContainerServiceImplTest - Service实现类单元测试
     *    - 使用Mock对象测试Service实现
     *    - 验证与Foundation层的交互
     *    - 测试数据转换和映射逻辑
     *    - 验证异常处理机制
     * 
     * 4. ContainerIntegrationTest - 集成测试
     *    - 测试完整的调用链路
     *    - 验证Controller和Service的集成
     *    - 测试分页、并发、性能等场景
     *    - 验证错误处理的端到端流程
     * 
     * 运行方式：
     * 1. IDE中右键运行此类
     * 2. Maven命令：mvn test -Dtest=ContainerTestSuite
     * 3. 运行所有容器测试：mvn test -Dtest=com.aiyounet.cs.container.**
     */
}
