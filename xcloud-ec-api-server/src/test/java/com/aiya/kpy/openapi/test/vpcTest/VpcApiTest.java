package com.aiya.kpy.openapi.test.vpcTest;

import com.SpringBootStarter;
import com.aiya.kpy.vpc.api.VpcAPI;
import com.aiya.kpy.vpc.api.request.*;
import com.aiya.kpy.vpc.api.response.*;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashSet;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class VpcApiTest {
    @Autowired
    private VpcAPI vpcAPI;

    @Test
    public void testCreateVpc() {
        CreateVpcRequest request = new CreateVpcRequest();
        request.setRegion("demo-1");
        request.setCidrBlock("**********/16");
        request.setDescription("测试用例api");
        request.setVpcName("test");
        request.setOprUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        CreateVpcResponse response = vpcAPI.createVpc(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeVpcList() {
        DescribeVpcListRequest request = new DescribeVpcListRequest();
        request.setRegion("demo-1");
        request.setOprUserId(1L);
//        request.setOprUserId(109595L);
//        request.setQueryUserId(109667L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        request.setOprUserPower("Management");
//        request.setPageSize(10);
//        request.setPageNumber(1);
        request.setVpcUUIDs(new String[]{"vpc-bl00000022ya", "vpc-bl00000022yu"});

        DescribeVpcListResponse response = vpcAPI.describeVpcList(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeVpcDetail() {
        DescribeVpcDetailRequest request = new DescribeVpcDetailRequest();
        request.setVpcId("vpc-bb00000026z3");
        request.setOprUserId(109595L);
        DescribeVpcDetailResponse response = vpcAPI.describeVpcDetail(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDeleteVpc() {
        DeleteVpcRequest request = new DeleteVpcRequest();
        request.setVpcId("vpc-l6000025q6j3");
        request.setOprUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        DeleteVpcResponse response = vpcAPI.deleteVpc(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testModifyVpcAttribute() {
        ModifyVpcAttributeRequest request = new ModifyVpcAttributeRequest();
        request.setVpcId("vpc-bb00000026z3");
        request.setVpcName("newName");
        request.setVpcDescription("new....");
        request.setOprUserId(109595L);
        ModifyVpcAttributeResponse response = vpcAPI.modifyVpcAttribute(request);
        System.out.println(JSON.toJSONString(response));
    }

}
