package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ecs.foundation.service.impl.ECloudEcsSaleStockServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/3/21
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudEcsServiceTest {
    @Autowired
    private ECloudEcsSaleStockServiceImpl ecsSaleStockService;

    @Test
    public void testToEcsGpuNumMap() {
        //String gpu = "A100(g4)";
        String gpu = "NVIDIA V100(6240)";
        ecsSaleStockService.toEcsUnitFreeMap(Params.poolId, gpu);
    }
}
