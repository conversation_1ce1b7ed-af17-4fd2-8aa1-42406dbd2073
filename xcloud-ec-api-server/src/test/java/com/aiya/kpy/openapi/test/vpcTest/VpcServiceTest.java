package com.aiya.kpy.openapi.test.vpcTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.utils.OpenAPIIDGenerator;
import com.aiya.kpy.vpc.api.response.CreateVpcResponse;
import com.aiya.kpy.vpc.foundation.service.VpcService;
import com.aiya.platform.foundation.utils.UUIDGenernator;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class VpcServiceTest {
    @Autowired
    private VpcService vpcService;

    @Test
    public void testCreateVpc() {
        CreateVpcResponse response = vpcService.createVpc("demo-1", "172.16.0.0/16", "测试", "测试vpc", 109595L, OpenAPIIDGenerator.genernateVpcUUID());
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void deletedVpc() {
        vpcService.deleteVpc("vpc-ff0000049y79", 109595L);
        System.out.println("删除成功！！");
    }

}
