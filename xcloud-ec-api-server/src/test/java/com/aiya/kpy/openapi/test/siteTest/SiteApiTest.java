package com.aiya.kpy.openapi.test.siteTest;

import com.SpringBootStarter;
import com.aiya.kpy.api.site.SiteApiWrapper;
import com.aiya.kpy.common.enums.ProductConfigItemEnum;
import com.aiya.kpy.site.api.model.sku.SkuSaleManageModel;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SiteApiTest {
    @Resource
    private SiteApiWrapper siteApiWrapper;

    //@Test
    //public void describeSkuSaleModelTreeTest() {
    //InstanceSeries instanceSeries = InstanceSeries.CPU;
    //String regionCode = "cn-fuzhou-8";
    //ResourceType resourceType = ResourceType.LIGHTNODE_ECS;
    //SkuChain skuChain = siteApiWrapper.describeSkuChain(ResourceType.LIGHTNODE_ECS, instanceSeries.getProductConfigItemByName(), 1L, 1L);
    //List<SkuSaleModelTree> saleModelTrees = siteApiWrapper.describeSkuSaleModelTree(skuChain, 1L, 1L, regionCode, resourceType);
    //System.out.println(JSON.toJSONString(saleModelTrees));
    //GPU配置链的SKU
    //[{"autoSellOut":false,"children":[{"autoSellOut":true,"children":[{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"1","saleStatus":"SELL_OUT","skuSaleId":679},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"2","saleStatus":"SELL_OUT","skuSaleId":680},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"3","saleStatus":"SELL_OUT","skuSaleId":681},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"4","saleStatus":"SELL_OUT","skuSaleId":682},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"5","saleStatus":"SELL_OUT","skuSaleId":683},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"6","saleStatus":"SELL_OUT","skuSaleId":684},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"7","saleStatus":"SELL_OUT","skuSaleId":685},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"8","saleStatus":"SELL_OUT","skuSaleId":686}],"configItem":"GPU_MODEL","configValue":"A100-PCIE-40GB","saleStatus":"FAKE_SELL_OUT","skuSaleId":642},{"autoSellOut":true,"children":[{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"1","saleStatus":"SELL_OUT","skuSaleId":643},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"2","saleStatus":"SELL_OUT","skuSaleId":644},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"3","saleStatus":"SELL_OUT","skuSaleId":645},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"4","saleStatus":"SELL_OUT","skuSaleId":646},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"5","saleStatus":"SELL_OUT","skuSaleId":647},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"6","saleStatus":"SELL_OUT","skuSaleId":648},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"7","saleStatus":"SELL_OUT","skuSaleId":649},{"autoSellOut":false,"configItem":"GPU_NUM","configValue":"8","saleStatus":"SELL_OUT","skuSaleId":650}],"configItem":"GPU_MODEL","configValue":"A100-SXM4-80GB","saleStatus":"FAKE_SELL_OUT","skuSaleId":637},{"autoSellOut":true,"children":[{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"1","saleStatus":"IN_SELL","skuSaleId":675},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"2","saleStatus":"IN_SELL","skuSaleId":676},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"3","saleStatus":"IN_SELL","skuSaleId":677},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"4","saleStatus":"IN_SELL","skuSaleId":678}],"configItem":"GPU_MODEL","configValue":"RTX 2080Ti","saleStatus":"IN_SELL","skuSaleId":641},{"autoSellOut":true,"children":[{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"1","saleStatus":"IN_SELL","skuSaleId":659},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"2","saleStatus":"IN_SELL","skuSaleId":660},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"3","saleStatus":"IN_SELL","skuSaleId":661},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"4","saleStatus":"FAKE_SELL_OUT","skuSaleId":662},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"5","saleStatus":"FAKE_SELL_OUT","skuSaleId":663},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"6","saleStatus":"FAKE_SELL_OUT","skuSaleId":664},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"7","saleStatus":"FAKE_SELL_OUT","skuSaleId":665},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"8","saleStatus":"FAKE_SELL_OUT","skuSaleId":666}],"configItem":"GPU_MODEL","configValue":"RTX 3080","saleStatus":"IN_SELL","skuSaleId":639},{"autoSellOut":true,"children":[{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"1","saleStatus":"IN_SELL","skuSaleId":667},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"2","saleStatus":"IN_SELL","skuSaleId":668},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"3","saleStatus":"IN_SELL","skuSaleId":669},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"4","saleStatus":"IN_SELL","skuSaleId":670},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"5","saleStatus":"IN_SELL","skuSaleId":671},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"6","saleStatus":"IN_SELL","skuSaleId":672},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"7","saleStatus":"IN_SELL","skuSaleId":673},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"8","saleStatus":"FAKE_SELL_OUT","skuSaleId":674}],"configItem":"GPU_MODEL","configValue":"RTX 3090","saleStatus":"IN_SELL","skuSaleId":640},{"autoSellOut":true,"children":[{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"1","saleStatus":"FAKE_SELL_OUT","skuSaleId":651},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"2","saleStatus":"FAKE_SELL_OUT","skuSaleId":652},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"3","saleStatus":"FAKE_SELL_OUT","skuSaleId":653},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"4","saleStatus":"FAKE_SELL_OUT","skuSaleId":654},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"5","saleStatus":"FAKE_SELL_OUT","skuSaleId":655},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"6","saleStatus":"FAKE_SELL_OUT","skuSaleId":656},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"7","saleStatus":"FAKE_SELL_OUT","skuSaleId":657},{"autoSellOut":true,"configItem":"GPU_NUM","configValue":"8","saleStatus":"FAKE_SELL_OUT","skuSaleId":658}],"configItem":"GPU_MODEL","configValue":"RTX 4090","saleStatus":"FAKE_SELL_OUT","skuSaleId":638}],"configItem":"INSTANCE_TYPE","configValue":"gpu","saleStatus":"IN_SELL","skuSaleId":636}]
    //CPU配置链的SKU
    //[{"autoSellOut":true,"children":[{"autoSellOut":true,"children":[{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u1.m1","saleStatus":"IN_SELL","skuSaleId":997},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u1.m2","saleStatus":"IN_SELL","skuSaleId":994},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u16.m32","saleStatus":"IN_SELL","skuSaleId":992},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u2.m4","saleStatus":"IN_SELL","skuSaleId":993},{"autoSellOut":false,"configItem":"INSTANCE_SPEC","configValue":"ecs.u4.m8","saleStatus":"SELL_OUT","skuSaleId":995},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u48.m384","saleStatus":"IN_SELL","skuSaleId":998},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u8.m16","saleStatus":"IN_SELL","skuSaleId":996},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u8.m32","saleStatus":"IN_SELL","skuSaleId":991},{"autoSellOut":true,"configItem":"INSTANCE_SPEC","configValue":"ecs.u96.m768","saleStatus":"IN_SELL","skuSaleId":999}],"configItem":"CPU_MODEL","configValue":"AMD EPYC 7302","saleStatus":"IN_SELL","skuSaleId":990}],"configItem":"INSTANCE_TYPE","configValue":"anygpug6","saleStatus":"IN_SELL","skuSaleId":989}]
    //}

    //@Test
    //public void describeSkuChainTest() {
    //    SkuChain skuChain = siteApiWrapper.describeSkuChain(ResourceType.LIGHTNODE_ECS, ProductConfigItemEnum.GPU, 1L, 1L);
    //    System.out.println(JSON.toJSONString(skuChain));
    //}

    @Test
    public void describeSkuChainTest() {
        List<SkuSaleManageModel> models = siteApiWrapper.describeSkuSaleManageList("cn-fuzhou-8", null, ProductConfigItemEnum.GPU_MODEL);
        System.out.println(JSON.toJSONString(models));
    }
}
