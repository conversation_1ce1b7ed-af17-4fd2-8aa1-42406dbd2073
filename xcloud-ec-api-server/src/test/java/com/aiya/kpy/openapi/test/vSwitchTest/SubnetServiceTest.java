package com.aiya.kpy.openapi.test.vSwitchTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.utils.OpenAPIIDGenerator;
import com.aiya.kpy.subnet.foundation.bean.SubnetParams;
import com.aiya.kpy.subnet.foundation.service.SubnetService;
import com.aiya.kpy.subnet.foundation.service.exception.CreateVlanException;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SubnetServiceTest {
    @Autowired
    private SubnetService subnetService;

    @Test
    public void createSubnet() {
        SubnetParams subnetParams = new SubnetParams();
        subnetParams.setDescription("test");
        subnetParams.setOprUserId(109595L);
        subnetParams.setCidrBlock("**********/24");
        subnetParams.setSubnetUUID(OpenAPIIDGenerator.genernateSubnetUUID());
        subnetParams.setZoneCode("demo-1-a");
        subnetParams.setSubnetName("test0002");
        subnetService.createSubnet("demo-1", "vpc-xx000004dez2", subnetParams);
        System.out.println("创建成功");
    }


    @Test
    public void createVlan() {
        try {
            subnetService.createVlan(OpenAPIIDGenerator.genernateSubnetUUID(), "demo-1", "demo-1-a", "test",
                    "test", 109595L);
        } catch (CreateVlanException e) {
            System.out.println(e.getIaasErrorMsg());
        }
    }

    @Test
    public void deleteSubnet(){
        subnetService.deleteSubnet("vnet-uz000004dj62", 109595L);
    }


}
