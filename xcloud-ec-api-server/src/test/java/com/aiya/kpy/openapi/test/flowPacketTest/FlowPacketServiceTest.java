package com.aiya.kpy.openapi.test.flowPacketTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ProdInfoChangeMessageType;
import com.aiya.kpy.flowpacket.foundation.entity.FlowPacketEntity;
import com.aiya.kpy.flowpacket.foundation.service.FlowPacketService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class FlowPacketServiceTest {
	@Autowired
	private FlowPacketService flowPacketService;

	@Test
	public void testAddToRecycleByTask(){
		FlowPacketEntity flowPacketEntity = flowPacketService.queryFlowPacketByPlatformUUID("fpt-7y00001l6qst");
		flowPacketService.addToRecycleByTask(flowPacketEntity,null,null, ProdInfoChangeMessageType.RECYCLE);

	}

}
