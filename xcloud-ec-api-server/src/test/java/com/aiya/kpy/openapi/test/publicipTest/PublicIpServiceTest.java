package com.aiya.kpy.openapi.test.publicipTest;

import com.SpringBootStarter;
import com.aiya.kpy.api.uic.UicApiWrapper;
import com.aiya.kpy.common.enums.*;
import com.aiya.kpy.common.util.ResourcePeriodUtil;
import com.aiya.kpy.ec.foundation.utils.PageBuilder;
import com.aiya.kpy.ecs.foundation.model.TimeParam;
import com.aiya.kpy.publicip.api.enums.*;
import com.aiya.kpy.publicip.foundation.bean.PublicIpPageDataResult;
import com.aiya.kpy.publicip.foundation.bean.PublicIpQueryCondition;
import com.aiya.kpy.publicip.foundation.bean.ReplaceIpParam;
import com.aiya.kpy.publicip.foundation.entity.PublicIpEntity;
import com.aiya.kpy.publicip.foundation.entity.PublicIpProtectionExpandEntity;
import com.aiya.kpy.publicip.foundation.entity.PublicIpRelationEntity;
import com.aiya.kpy.publicip.foundation.exception.ProdReleasePublicIpException;
import com.aiya.kpy.publicip.foundation.service.PublicIpBillService;
import com.aiya.kpy.publicip.foundation.service.PublicIpLogService;
import com.aiya.kpy.publicip.foundation.service.PublicIpService;
import com.aiya.kpy.uic.api.model.UserInfo;
import com.alibaba.fastjson.JSON;
import org.apache.ibatis.session.RowBounds;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class PublicIpServiceTest {
    @Autowired
    private PublicIpService publicIpService;
    @Autowired
    private UicApiWrapper uicApiWrapper;
    @Autowired
    private PublicIpBillService publicIpBillService;
    @Autowired
    private PublicIpLogService ipLogService;
    private static Long userId = 109595L;

    @Test
    public void releasePublicIp() throws ProdReleasePublicIpException {
        publicIpService.releasePublicIp("eip-4f00000021z2");
    }

    @Test
    public void testAllocatePublicIp() {
        final UserInfo userInfo = uicApiWrapper.queryUicUserInfoByUserId(userId);
        TimeParam timeParam = new TimeParam();
        timeParam.setTryOut(true);
        timeParam.setBillingperiod(BillingCycleEnum.MONTH);
        timeParam.setPeriod(1);

        PublicIpEntity ipEntity = new PublicIpEntity();
        ipEntity.setResourceUUID("eip-as000000025");
        ipEntity.setCreateTime(new Date());
        ipEntity.setRegionId(11L);
        ipEntity.setPublicIpStatus(PublicIpStatus.PENDING);
        ipEntity.setPublicIpPendingStatus(PublicIpPendingStatus.CREATING);
        ipEntity.setInternetChargeType(InternetChargeType.PAY_BY_BAND_WIDTH);
        ipEntity.setBandwidth(1);
        Date expireTime = ResourcePeriodUtil.getExpireTime(timeParam.getBillingperiod(), timeParam.getPeriod());
        ipEntity.setExpireDate(expireTime);
        ipEntity.setAutoRenew(AutoRenewStatus.NONE);
        ipEntity.setBandwidthType(BandwidthType.BGP);
        ipEntity.setChargeType(InstanceChargeType.PRE_PAID);
        ipEntity.setOverdueDays(null);
        ipEntity.setLimitFlow(BooleanEnum.FALSE);
        ipEntity.setSourceResourceType(ResourceType.OWN_EIP);
        ipEntity.setPublicIpResourceType(PublicIpResourceType.EIP);
        ipEntity.setFreeFlow(BigDecimal.ZERO);
        ipEntity.setSourceChannelType(SourceChannelType.INNER_API);
        ipEntity.setResourceType(ResourceTypeGroup.EIP.getResourceType(ResourceProvider.OWN));

        String uuid = publicIpService.allocatePublicIpByOrder(userInfo, ipEntity, timeParam, "test1048080", null, null, null);

        System.out.println(uuid);
    }


    @Test
    public void testQueryPublicIpProtectionExpand() {
        List<PublicIpProtectionExpandEntity> expandEntities = publicIpService.queryPublicIpProtectionExpandByResourceUUID(Arrays.asList("eip-6u0000gpnyx212"));
        System.out.println(JSON.toJSONString(expandEntities));
    }

    @Test
    public void testGetPublicIpPageList() {
        final RowBounds rowBounds = PageBuilder.builder(1, 10);
        PublicIpQueryCondition condition = new PublicIpQueryCondition();
        condition.setPublicIpType(PublicIpResourceType.EIP);
        PublicIpPageDataResult result = publicIpService.getPublicIpPageList("demo-1", condition, rowBounds);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void testAssocaitePublicIp() {
        publicIpService.assocaitePublicIpByTask("eip-as000000015", "ecs-7c000024nesm", PublicIpBindLevel.SECONDARY, userId);

        publicIpService.assocaitePublicIpByTask("eip-as000000025", "ecs-7c000024nesm", PublicIpBindLevel.SECONDARY, userId);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testUnassocaitePublicIp() {
        publicIpService.unassociatePublicIpByTask("ecs-7c000024nesm", "eip-as000000015", userId);

        publicIpService.unassociatePublicIpByTask("ecs-5800002a0kpd", "eip-5800002a0kpg", userId);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testModifyPublicIpNetworkSpec() {
        final UserInfo userInfo = uicApiWrapper.queryUicUserInfoByUserId(userId);
        PublicIpEntity ipEntity = publicIpService.queryPublicIpByPlatformUUID("eip-as000000013");
        publicIpService.modifyPublicIpNetworkSpecByOrder(ipEntity, null, null, userInfo, 2, null, null, null,
                "test000002", ProdInfoChangeMessageType.CHANGE_SPEC);
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testAddToRecycle() {
        PublicIpEntity ipEntity = publicIpService.queryPublicIpByPlatformUUID("eip-as000000014");
        PublicIpRelationEntity publicIpRelationEntity = publicIpService.queryPublicIpRelationByIpUUID("eip-as000000014");

        publicIpService.addToRecycle(ipEntity, new Date(), null, publicIpRelationEntity, "test000003", ProdInfoChangeMessageType.REFUND);
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testReleasePublicIp() {
        publicIpService.releasePublicIpByTask("eip-as000000015", userId, BooleanEnum.TRUE);
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRenewPublicIp() {
        TimeParam timeParam = new TimeParam(false, 1, BillingCycleEnum.MONTH);
        PublicIpEntity ipEntity = publicIpService.queryPublicIpByPlatformUUID("eip-as000000014");
        publicIpService.renewPublicIp(ipEntity, timeParam, null, null, AutoRenewStatus.NONE, "test1234");
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testReplacePublicIpAddress() {
        ReplaceIpParam replaceIpParam = new ReplaceIpParam();
        replaceIpParam.setOwnerId(userId);
        replaceIpParam.setOprUserId(userId);
        replaceIpParam.setIpUUID("eip-as000000015");
        replaceIpParam.setBandwidthType(BandwidthType.BGP);
        replaceIpParam.setInheritFlow(BooleanEnum.TRUE);
        replaceIpParam.setIpCidrBlock("**********/24");
        replaceIpParam.setOrderUUID("test1234");
        replaceIpParam.setRemark("");
        String str = publicIpService.replacePublicIpAddress(replaceIpParam);
        System.out.println(str);
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testRegularPublicIp() {
        TimeParam timeParam = new TimeParam(true, 1, BillingCycleEnum.MONTH);
        PublicIpEntity ipEntity = publicIpService.queryPublicIpByPlatformUUID("eip-as000000016");
        publicIpService.regularPublicIp(ipEntity, timeParam, AutoRenewStatus.NONE, InternetChargeType.PAY_BY_BAND_WIDTH, InstanceChargeType.POST_PAID, "test1234");

    }

    @Test
    public void testQueryPublicIpMonitorData() {
//
//        PublicIpMonitorDataResult publicIpMonitorDataResult = publicIpService.queryPublicIpMonitorData("eip-sy000026u6be", DateTimeUtils.addDays(new Date(), -7), DateTimeUtils.addDays(new Date(), 7), 1, userId, UserPower.MANAGEMENT.getCode());
//        System.out.println(JSON.toJSONString(publicIpMonitorDataResult));
    }

    @Test
    public void testDescribePublicIpUseFlowInSomePeriod() {
//
//        BigDecimal bigDecimal = publicIpService.describePublicIpUseFlowInSomePeriod("eip-sy000026u6be", DateTimeUtils.addDays(new Date(), -7), DateTimeUtils.addDays(new Date(), 7));
//        System.out.println(JSON.toJSONString(bigDecimal));
    }

    @Test
    public void testDescribeSumFlowData() {

//        BigDecimal bigDecimal = publicIpBillService.describeSumFlowData("eip-sy000026u6be", DateTimeUtils.addDays(new Date(), -7), DateTimeUtils.addDays(new Date(), 7));
//        System.out.println(JSON.toJSONString(bigDecimal));
    }

    @Test
    public void testDescribeSumFlowDataNew() {

//        BigDecimal bigDecimal = publicIpBillService.describeSumFlowDataNew("eip-sy000026u6be", DateTimeUtils.addDays(new Date(), -7), DateTimeUtils.addDays(new Date(), 7));
//        System.out.println(JSON.toJSONString(bigDecimal));
    }

    @Test
    public void testCreateIpBill() {

//        BigDecimal bigDecimal = publicIpBillService.createIpBill("eip-as000000016", DateUtils.addDays(new Date(), -7), DateUtils.addDays(new Date(), 7));
//        System.out.println(JSON.toJSONString(bigDecimal));
    }

    @Test
    public void testDescribeSumFlowDataByResourceUUID() {
//
//        BigDecimal bigDecimal = publicIpBillService.describeSumFlowDataByResourceUUID("eip-sy000026u6be", DateTimeUtils.addDays(new Date(), -7), DateTimeUtils.addDays(new Date(), 7));
//        System.out.println(JSON.toJSONString(bigDecimal));
    }

    @Test
    public void testCountChangeIpNumByTime() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date beginTime = null;
        Date endTime = null;
        try {
            beginTime = dateFormat.parse("2023-01-03 20:00:00");
            endTime = dateFormat.parse("2023-01-04 17:00:00");
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Integer integer = ipLogService.countReplaceIp("eip-xn00003ivtbk", beginTime);
        System.out.println("integer = " + integer);
        final BigDecimal billUseAmount = BigDecimal.valueOf(ipLogService.countReplaceIpByBillTime("eip-xn00003ivtbk", beginTime, endTime));
        System.out.println("billUseAmount = " + billUseAmount);
    }

    @Test
    public void publicIpFlowLimitOrResumeTest() {
        String ecsInstanceUUID = "ecs-5g00000pkixg";
        List<PublicIpEntity> ipEntities = publicIpService.queryPublicIpByInstanceUUID(ecsInstanceUUID, PublicIpRelationStatus.AVAILABLE);
        publicIpService.publicIpFlowLimitOrResume(ipEntities.get(0));
    }
}
