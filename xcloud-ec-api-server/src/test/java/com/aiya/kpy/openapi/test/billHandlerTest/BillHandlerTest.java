package com.aiya.kpy.openapi.test.billHandlerTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.common.enums.ResourceTypeGroup;
import com.aiya.kpy.ec.BaseResourceBillService;
import com.aiya.kpy.ec.task.batch.ResourceBillAdapterFactory;
import com.aiya.kpy.publicip.task.batch.PublicIpBillHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class BillHandlerTest {
    @Autowired
    private PublicIpBillHandler publicIpBillHandler;
    @Autowired
    private ResourceBillAdapterFactory resourceBillAdapterFactory;

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Test
    public void dealWithResourceBillByResourceUUIDTest() {
        publicIpBillHandler.dealWithResourceBillByResourceUUID(31107L, "eip-7f00000peodg", cn.hutool.core.date.DateUtil.offsetHour(new Date(), 1));
        throw new RuntimeException();
    }

    @Test
    public void test() {
        Date beginDate = getDate("2024-12-17 14:00:00");
        Date endDate = getDate("2024-12-17 15:00:00");

        BaseResourceBillService ecsBaseResourceBillService = resourceBillAdapterFactory.getHandlerByResourceTypeGroup(ResourceTypeGroup.ECS);
        ecsBaseResourceBillService.getRealUseAmount(new BigDecimal("60.00"), "ecs-5g00000pko09", ResourceType.LIGHTNODE_ECS, beginDate, endDate);
    }

    private Date getDate(String dateTimeString) {
        LocalDateTime localDateTime = LocalDateTime.parse(dateTimeString, formatter);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
}
