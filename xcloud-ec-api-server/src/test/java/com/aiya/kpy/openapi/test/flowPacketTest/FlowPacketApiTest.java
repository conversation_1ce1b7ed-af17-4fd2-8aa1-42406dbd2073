package com.aiya.kpy.openapi.test.flowPacketTest;

import com.SpringBootStarter;
import com.aiya.kpy.flowpacket.api.FlowPacketAPI;
import com.aiya.kpy.flowpacket.api.request.RemoveFlowPacketIpRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class FlowPacketApiTest {
	@Autowired
	private FlowPacketAPI flowPacketAPI;

	@Test
	public void testRemoveFlowPacketIp(){
		RemoveFlowPacketIpRequest request = new RemoveFlowPacketIpRequest();
	}

}
