package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.service.impl.ECloudEcsServiceImpl;
import com.aiya.kpy.ec.foundation.iaas.enums.HostStatus;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSEcsManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.*;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeHostInfo;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeHostsInfo;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeVncInfo;
import com.aiya.kpy.ec.foundation.iaas.service.exception.*;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSEcsServiceImpl;
import com.aiya.kpy.ec.foundation.utils.DateTimeUtils;
import com.aiya.kpy.res.sdk.request.DescribeHostsRequest;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.text.DateFormat;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasEcsServiceTest {
    @Autowired
    private IaaSEcsServiceImpl iaaSEcsService;
    @Autowired
    private ECloudEcsServiceImpl eCloudEcsService;
    @Autowired
    private IaaSEcsManager iaaSEcsManager;

    @Test
    public void testPauseInstance() {
//		try {
//			iaaSEcsService.pauseEcs("ecs-e900002517da", 813L);
//		} catch (IaaSPauseEcsException e) {
//			e.printStackTrace();
//		}
    }

    @Test
    public void testResumeInstance() {
//		try {
//			iaaSEcsService.resumeEcs("ecs-e900002517da", 813L);
//		} catch (IaaSResumeEcsException e) {
//			e.printStackTrace();
//		}
    }

    /**
     * 测试校验主机类型
     */
    @Test
    public void testValidInstanceType() {
        try {
            //Boolean aBoolean = iaaSEcsService.checkInstanceType("demo-2", "demo-2-a", "ecs.n1.tiny", 1, 1);
            Boolean aBoolean = eCloudEcsService.checkInstanceType("cn-guangzhou-3", "cn-guangzhou-3-a", "g4a.2xlarge.8", 8, 64);
            System.out.println(aBoolean);
        } catch (IaaSInstanceTypesException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试创建主机
     */
    @Test
    public void testCreataEcsBymanager() {
        try {
            String instance = iaaSEcsManager.createInstance("cn-fuzhou-8", "cn-fuzhou-8-a", "KaoPu1234567.", "ecs.u96.m768",
                    "e8cb9596-7b77-4b55-b6ef-e3acd4c305a6", "d0e7db66-bbfa-4452-8022-971b1b2828d2", "测试CPU型云主机", "local_ssd", "anygpug6", 0);
            System.out.println("instance = " + instance);
        } catch (CreateInstanceException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试创建vpc主机
     */
    @Test
    public void testCreateVpcEcs() {

    }

    /**
     * 测试描述主机详情
     */
    @Test
    public void testDescirbeEcs() {
        try {
            iaaSEcsManager.describeInstances("cn-fuzhou-4", "cn-fuzhou-4-a", new String[]{"e88b35fd-2b77-4a59-85df-8b7c233368bd"});
        } catch (DescribeInstancesException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试卸载硬盘
     */
    @Test
    public void testUnattachDisk() {
        try {
            iaaSEcsManager.unattachDisk("cn-fuzhou-4", "cn-fuzhou-4-b", "", "");
        } catch (UnattachDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试卸载硬盘
     */
    @Test
    public void testUnattachDiskByManager() {
        try {
            iaaSEcsManager.unattachDisk("cn-fuzhou-4", "cn-fuzhou-4-b", "247151dd-6388-4f53-8a93-619968ef4f03", "ae7199d8-91de-4ff9-9eff-0f2c191d16f4");
        } catch (UnattachDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试挂载硬盘
     */
    @Test
    public void testAttachDiskByManager() {
        try {
            iaaSEcsManager.attachDisk("cn-fuzhou-4", "cn-fuzhou-4-b", "247151dd-6388-4f53-8a93-619968ef4f03", "ae7199d8-91de-4ff9-9eff-0f2c191d16f4");
        } catch (com.aiya.kpy.ec.foundation.iaas.manager.exception.AttachDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试挂载硬盘
     */
    @Test
    public void testAttachDisk() {
//		try {
//			iaaSEcsService.attachDisk("123", 117L, 147L);
//		} catch (IaaSEcsAttachDiskException e) {
//			e.printStackTrace();
//		}
    }

    /**
     * 测试关机
     */
    @Test
    public void testStop() {
        try {
            iaaSEcsService.forceStopEcs("123", "", "", "");
        } catch (IaaSStopEcsException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试开机
     */
    @Test
    public void testStart() {
        try {
            String resourceUUID = "ecs-zp0000326mbd";
            String iaasUUID = "5e8a4215-0d6e-4e97-94f7-ec2602c26922";
            String region = "demo-2";
            String zone = "demo-2-a";
            iaaSEcsService.startEcs(resourceUUID, region, zone, iaasUUID);
        } catch (IaaSStartEcsException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试更换系统盘
     */
    @Test
    public void testReplaceSystemDisk() {
        try {
            iaaSEcsService.replaceSystemDisk("123", "", "", "", "", "", "");
        } catch (IaaSEcsReplaceSystemDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试更换系统盘
     */
    @Test
    public void testReplaceSystemDiskByManager() {
        try {
            iaaSEcsManager.replaceSystemDisk("1", "", "cn-fuzhou-4", "cn-fuzhou-4-b", null, "QWEr12345678");
        } catch (com.aiya.kpy.ec.foundation.iaas.manager.exception.ReplaceSystemDiskException e) {
            e.printStackTrace();
        }

    }

    /**
     * 测试修改实例配置
     */
    @Test
    public void testMidifyEcsType() {
        try {
            iaaSEcsService.resizeInstance("123", "", "", "", "");
        } catch (IaaSResizeEcsException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试修改主机属性
     */
    @Test
    public void testModifyEcsAttribute() {
        try {
            iaaSEcsService.modifyEcsAttribute("123", "", "", "", "", "");
        } catch (IaaSModifyEcsAttributeException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试通过manager释放主机
     */
    @Test
    public void releaseEcs() {
        try {
            iaaSEcsManager.releaseInstance("cn-fuzhou-4", "cn-fuzhou-4-b", "gfgsdfgdfgrtgsr");
        } catch (ReleaseInstanceException e) {
            e.printStackTrace();
        } catch (ResourceAlreadyReleaseException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试services释放主机
     */
    @Test
    public void releaseEcsByService() {
        try {
            iaaSEcsService.releaseEcs("123", "", "", "");
        } catch (IaaSReleaseEcsException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试监控
     */
    @Test
    public void testMonitorEcs() {
        try {
            DateFormat dateFormat = DateTimeUtils.getDateFormat();
            iaaSEcsManager.describeEcsMonitorDate("iaasEcsUUID", "cn-fuzhou-4",
                    "cn-fuzhou-4-b", dateFormat.format(new Date()), dateFormat.format(new Date()), 60);
        } catch (RecordNotFoundException e) {
            e.printStackTrace();
        } catch (DescribeEcsMonitorDataException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试vnc描述
     */
    @Test
    public void testVnc() {
        DescribeVncInfo info = null;
        try {
            info = iaaSEcsManager.describeVnc("demo-1", "demo-1-a", "7b97863a-c197-4e7d-966a-9433a7301864");
        } catch (DescribeVncException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(info));
    }

    /**
     * 测试vnc重启
     */
    @Test
    public void testVncReboot() {
        try {
            iaaSEcsService.vncRestartEcs("123", "", "", "");
        } catch (IaaSRestartEcsException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试获取宿主机详情
     */
    @Test
    public void testDescribeHostDetail() {
        try {
            DescribeHostInfo info = iaaSEcsManager.describeHostDetail("cn-dev-2", "819298570");
            System.out.println(JSON.toJSONString(info));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试获取宿主机列表
     */
    @Test
    public void testDescribeHosts() throws Exception {
        //String[] regionCode = {"cn-fuzhou-8", "cn-shanghai", "cn-guangzhou", "cn-beijing", "demo1", "cn-dev-2"};
        //List<DescribeHostsInfo> infos = iaaSEcsManager.describeHosts();
        //System.out.println("获取数据成功:" + JSON.toJSONString(infos));

        DescribeHostsInfo infos = iaaSEcsManager.describeHosts(new DescribeHostsRequest("cn-fuzhou-8", new Integer[]{HostStatus.ONLINE.getCode()}, "anygpug6"));
        System.out.println("获取数据成功:" + JSON.toJSONString(infos));
        //Map<String, GpuNumInfo> map = iaaSEcsService.describeGpuNumInfo();
        //System.out.println("获取数据成功:" + JSON.toJSONString(map));
        //Map<String, List<Integer>> gpuNumInfo = iaaSEcsService.describeGpuNumInfo("cn-fuzhou-8");
        //System.out.println(JSON.toJSONString(gpuNumInfo));
    }
}
