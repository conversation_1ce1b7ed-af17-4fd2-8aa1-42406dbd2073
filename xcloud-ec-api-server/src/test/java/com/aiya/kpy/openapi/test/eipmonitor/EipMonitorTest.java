package com.aiya.kpy.openapi.test.eipmonitor;

import com.SpringBootStarter;
import com.aiya.kpy.publicip.task.batch.EipMonitorDataHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/12
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EipMonitorTest {
    @Resource
    private EipMonitorDataHandler eipMonitorDataHandler;

    @Test
    public void testBill() {
        try {
            eipMonitorDataHandler.recoverJob("2025-02-12 00:00:00,2025-02-12 14:00:00");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
