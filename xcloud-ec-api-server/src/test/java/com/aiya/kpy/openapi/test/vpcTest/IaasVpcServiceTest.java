package com.aiya.kpy.openapi.test.vpcTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSCreateVpcException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSReleaseVpcException;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSVpcServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasVpcServiceTest {
    @Autowired
    private IaaSVpcServiceImpl iaaSVpcService;

    /**
     * 创建VPC
     */
    @Test
    public void testCreateVpc() {
        try {
            String iaasVpcUUID = iaaSVpcService.createVpc("vpc-bl00000011z1", "demo-1", "172.16.0.0/16");
            System.out.println(iaasVpcUUID);
        } catch (IaaSCreateVpcException e) {
            System.out.println(e.getMessage());
        }
    }

    /**
     * 释放VPC
     */
    @Test
    public void testReleaseVpc() {
        try {
            iaaSVpcService.releaseVpc("vpc-bl00000011z1", "demo-1", "4b83b30c-56d3-4efc-8256-036775f769f7");
        } catch (IaaSReleaseVpcException e) {
            System.out.println(e.getMessage());
        }
    }

}
