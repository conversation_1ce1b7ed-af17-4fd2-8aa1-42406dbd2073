package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceSmsNotifyTypeEnum;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.common.message.ResourceSmsNotifyMessage;
import com.aiya.kpy.ec.foundation.handler.sms.SmsNotifyAdapter;
import com.aiya.kpy.ec.foundation.handler.sms.SmsNotifyAdapterFactory;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;

/**
 * <AUTHOR> by huangxy
 * @date 2020/8/3
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EcsNotifyHandleTest {
    @Autowired
    private SmsNotifyAdapterFactory smsFactory;

    @Test
    public void testSendResourceUnsubscribeSuccessSms() {
        SmsNotifyAdapter handler = smsFactory.getSmsNotifyHandler(ResourceType.LIGHTNODE_ECS.getResourceTypeGroup());
        ResourceSmsNotifyMessage message = new ResourceSmsNotifyMessage();
        message.setResourceSmsNotifyType(ResourceSmsNotifyTypeEnum.RESOURCE_UNSUBSCRIBE_SUCCESS);
        message.setUserId(109627L);
        message.setResourceType(ResourceType.FORTNODE_ECS);
        message.setIsTryOut(false);
        message.setResourceUUIDLst(Arrays.asList("ecs-qu000000c7y9"));
        handler.sendResourceUnsubscribeSuccessSms(message);
    }
}
