package com.aiya.kpy.openapi.test.imageTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.enums.ImageType;
import com.aiya.kpy.image.api.request.DescribeImagesRequest;
import com.aiya.kpy.image.api.response.DescribeImagesResponse;
import com.aiya.kpy.image.foundation.dao.ImageDAO;
import com.aiya.kpy.image.foundation.entity.ImageEntity;
import com.aiya.kpy.image.foundation.service.ImageService;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ImageServiceTest {
	@Autowired
	private ImageService imageService;

	@Autowired
	private ImageDAO imageDAO;

	@Test
	public void testDescribeImages(){
//		List<ImageEntity> imageEntities = imageDAO.selectExpiredSelfImage();
//
//		System.out.println(imageEntities);
//		imageService.deleteExpiredSelfImage();

	}

}
