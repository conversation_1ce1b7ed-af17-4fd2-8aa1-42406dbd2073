package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudImsManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.DescribeImageException;
import com.aiya.kpy.res.ecloud.enums.ChargeTypeEnum;
import com.aiya.kpy.res.ecloud.enums.FeeTypeEnum;
import com.aiya.kpy.res.ecloud.sdk.response.GetCustomImageDetailResponse;
import com.aiya.kpy.res.ecloud.sdk.response.GetServerPublicImagesResponse;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudImsManagerTest {
    @Resource
    private ECloudImsManager eCloudImsManager;

    @Test
    public void getServerPublicImagesTest() throws DescribeImageException {
        GetServerPublicImagesResponse response = eCloudImsManager.getServerPublicImages(Params.poolId, Params.region, "c5.large.2", ChargeTypeEnum.BOTH, FeeTypeEnum.DURATION);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"imageId":"bd09fd1c-935d-409d-8439-5e36112ebf87","minDisk":"20","name":"BC-Linux 7.1 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"12896f96-36d9-4be3-8fd5-0f3f58bcdd7a","minDisk":"20","name":"CentOS 7.1 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"1a6e1780-23aa-498f-8a73-66a1b484be5d","minDisk":"20","name":"CentOS 7.5 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"b6e4c91a-bfd5-4817-99ce-d56998ba0eb6","minDisk":"20","name":"Ubuntu 14.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"0b9ed60d-e7ef-43b7-9326-c541bf51b0fd","minDisk":"20","name":"Ubuntu 16.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"6d9af28e-a5f8-4675-a4a4-87f40a82519a","minDisk":"20","name":"Ubuntu 18.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"be0261ab-9b1e-4990-9378-13c1e8f56fff","minDisk":"40","name":"Windows Server 2012 R2 Datacenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa738","minDisk":"40","name":"Windows Server 2016 Datacenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa739","minDisk":"20","name":"CentOS 7.4 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa740","minDisk":"20","name":"CentOS 7.2 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"ae454f7c-4898-4afc-a341-8658e172883c","minDisk":"20","name":"CentOS 7.3 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"f8d2e64d-35d7-41a5-a4e8-53a84731c502","minDisk":"20","name":"OpenSUSE 42.3 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"777023ac-a2f4-43b6-a4da-c6c7ea62a4ba","minDisk":"20","name":"CentOS 7.6 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"068e1c7d-f8b7-482f-bfbc-8263396c96c7","minDisk":"20","name":"CentOS 7.7 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"f4063971-526a-4479-995e-1f6e48b94dbe","minDisk":"40","name":"Windows Server 2019 DataCenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"beb697d3-41d9-450d-9f84-7f8b4e008c3c","minDisk":"20","name":"BC-Linux 7.6 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"6ec4f105-50bf-443a-9ce6-a476438847b3","minDisk":"20","name":"BC-Linux 7.5 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"d6b652cf-7ece-4d21-a902-4e7682a533a3","minDisk":"20","name":"BC-Linux 7.4 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"be396df1-25b4-432a-bec6-55d257ec0886","minDisk":"20","name":"CentOS 7.0 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"cac4dd55-9e3f-43b8-8d97-0c4a99788aee","minDisk":"40","name":"Windows Server 2012 R2 Standard 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"9eb0c81f-ecbf-4dd0-82d6-62be5f2aedec","minDisk":"40","name":"Windows Server 2012 R2 Standard 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"ce5a17ba-1ff9-4160-ab86-1ca7efd8009a","minDisk":"40","name":"Windows Server 2012 R2 Datacenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"8fadd2c3-e271-4746-a12d-1823bdd94f00","minDisk":"40","name":"Windows Server 2016 Datacenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"483a096b-989e-43dd-97d0-14bb7d2f24be","minDisk":"40","name":"Windows Server 2019 DataCenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"8add1236-f092-40dd-b756-f36f93d2e5dd","minDisk":"20","name":"BC-Linux 8.1 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"c82bb33a-69e0-4f10-b8e0-856502066384","minDisk":"20","name":"BC-Linux 7.7 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"de715a8b-37f8-455a-9d53-127882186833","minDisk":"20","name":"CentOS 7.8 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"be843c01-bbb7-4255-bf9e-ca88d3b9ab6a","minDisk":"20","name":"CentOS 7.9 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"76f03145-5574-43e7-87f8-bfa6da0e06c0","minDisk":"20","name":"Ubuntu 20.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"695ae969-d95a-4601-94a0-1e2cf2f145ec","minDisk":"20","name":"OpenSUSE 15.2 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"e87d1078-3a33-4db2-9ad2-fe375b22cad4","minDisk":"20","name":"Debian 10.9 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"dc2ad076-15a3-46dd-9b3b-c4eac12478d8","minDisk":"20","name":"Debian 10.7 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"02d97d1a-08b9-49bd-8da2-c82d798b360d","minDisk":"20","name":"Debian 10.2 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"e42aea00-8cbe-4606-8eec-e5bc1aa1f969","minDisk":"20","name":"openEuler 20.03 64位","osName":"openEuler","osType":"linux","status":"ACTIVE"},{"imageId":"9fc61d66-4d51-4757-93fb-ad6a38c96807","minDisk":"20","name":"BC-Linux 8.2 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"a3d6b50a-601a-40d7-8e4d-fd2a65da4166","minDisk":"20","name":"BC-Linux for Euler 21.10 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"b0d05bd0-887d-427f-a669-fbde48dd21f6","minDisk":"20","name":"BC-Linux 7.8 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"b197d26c-8b53-484b-be0e-119038d4034f","minDisk":"20","name":"Ubuntu 22.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"89fb5e86-ea26-4bac-b078-213093b807c3","minDisk":"20","name":"BC-Linux 8.4 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"a07aea82-2e84-416e-b008-3690ae38e251","minDisk":"20","name":"OpenSUSE 15.3 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"5631897c-b7ae-499e-9e4a-7a27d9a9d431","minDisk":"40","name":"Windows Server 2022 DataCenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"9b549974-fe65-46a5-990a-81522002fe04","minDisk":"40","name":"Windows Server 2022 DataCenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"6f6561ac-a986-12ec-994b-5987e37a5290","minDisk":"20","name":"Debian 11.6 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"08507a70-79ea-bb17-ed32-1b2eed4ce7a3","minDisk":"20","name":"openEuler 22.03 64位","osName":"openEuler","osType":"linux","status":"ACTIVE"},{"imageId":"5a2cea75-d211-4481-e892-7eccf9e64478","minDisk":"20","name":"Rocky 8.5 64位","osName":"Rocky","osType":"linux","status":"ACTIVE"},{"imageId":"0569c11d-f5ee-ecc9-6e32-328c2cc56067","minDisk":"20","name":"Rocky 9.1 64位","osName":"Rocky","osType":"linux","status":"ACTIVE"},{"imageId":"3c58f5d6-424c-592a-8671-b6028f2a2503","minDisk":"20","name":"Anolis 8.6 64位","osName":"Anolis","osType":"linux","status":"ACTIVE"},{"imageId":"9d652618-58a6-04ff-9d51-400480b79909","minDisk":"20","name":"BC-Linux for Euler 22.10 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"e2f73472-97c7-124e-574c-fea377c7dbb3","minDisk":"20","name":"BC-Linux 8.6 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"2e6184fd-e3ac-1d77-86d8-28f49e231265","minDisk":"40","name":"Windows Server 2019 Standard 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"b01de63f-cd0a-7566-f020-344d8faba7a5","minDisk":"40","name":"Windows Server 2019 Standard 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"337f1981-370e-babd-15c3-58392c38ea0f","minDisk":"20","name":"AlmaLinux 8.8 64位","osName":"AlmaLinux","osType":"linux","status":"ACTIVE"},{"imageId":"74293fa6-9432-73c9-a536-f0aac2d7efed","minDisk":"20","name":"CentOS Stream 8.0 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"ad4b2691-ad14-50d4-ff5e-41fbec72a0b2","minDisk":"20","name":"OpenSUSE 15.5 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"9046c892-5df5-aab1-9a92-c6857f710b0f","minDisk":"20","name":"Anolis 8.8 64位","osName":"Anolis","osType":"linux","status":"ACTIVE"}],"total":55}
    }

    @Test
    public void getCustomImagesTest() throws DescribeImageException {
        //GetCustomImagesRequest request = GetCustomImagesRequest.builder().build();
        //GetCustomImagesResponse response = eCloudImsManager.getCustomImages(Params.poolId, request);
        //System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getImageDetailTest() throws DescribeImageException {
        String imageId = "bd09fd1c-935d-409d-8439-5e36112ebf87";
        GetCustomImageDetailResponse response = eCloudImsManager.getCustomImageDetail(Params.poolId, imageId);
        System.out.println(JSON.toJSONString(response));
        //
    }

}
