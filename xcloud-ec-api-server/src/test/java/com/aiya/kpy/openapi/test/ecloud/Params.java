package com.aiya.kpy.openapi.test.ecloud;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/3/25
 */
@Data
public class Params {
    public static final String name = "testopenapi";

    //public static final String poolId = "CIDC-RP-26"; //华南-广州3
    public static final String poolId = "cn-guangzhou-3"; //华南-广州3
    //public static final String region = "DGJD"; //可用区1
    //public static final String networkId = "0b954893-33de-4bfc-9fa4-cafb265d548e"; //网络id
    //public static final String keyPair = "testCreateKeyPair1"; //密钥对,会生成在对应的账号下[可用区],订购主机时可以看到
    //public static final String serverId = "445ec824-220f-410a-9068-62ee1285c23e"; //主机id,创建主机后查看
    //public static final String volumeId = "8c3a549e-536c-40a2-9801-ea83f9706af2"; //硬盘id


    //public static final String poolId = "CIDC-RP-31"; //华东-济南
    public static final String region = "N0531-SD-JNSC01"; //可用区1
    public static final String vpcId = "2cc13a4aa5834249a86cd6175fe3eff3"; //vpc
    public static final String routerId = "02c9300d-48c7-4963-9d94-2e2782f88011"; //vpc路由id
    public static final String networkId = "e65324aa-2d26-41fa-b10f-3477fa439cf6"; //网络id
    public static final String keyPair = ""; //密钥对,会生成在对应的账号下[可用区],订购主机时可以看到
    //public static final String serverId = "bf0cd0ef-bb97-4b67-8b33-4a7cd01f6d61"; //主机id,创建主机后查看
    public static final String serverId = "ccdd7fe4-f28a-4947-afd5-6a0eb09eb086"; //主机id,创建主机后查看
    public static final String volumeId = "32d8d5a9-464f-46eb-9065-b396317c76bd"; //硬盘id
    public static final String ipId = "9e375023-310c-46ae-b2e3-2a989b352027"; //ipid
    public static final String bandwithId = "668e0073-d80f-4555-a254-0708a4412063"; //带宽id
    public static final String securityGroupId = "a4f9b2de-e913-4eda-9177-c348d2e352cf"; //安全组id


}
