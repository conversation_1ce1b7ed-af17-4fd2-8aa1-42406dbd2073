package com.aiya.kpy.openapi.test.resourceTest;

import com.SpringBootStarter;
import com.aiya.kpy.disk.foundation.entity.DiskEntity;
import com.aiya.kpy.ec.foundation.service.ResourceCommonService;
import com.aiya.kpy.ecs.foundation.entity.EcsInstanceEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @description
 * @date 2024/1/10 10:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ResourceServiceTest {
    @Resource
    private ResourceCommonService<EcsInstanceEntity> ecsResourceServiceOperate;

    @Resource
    private ResourceCommonService<DiskEntity> diskResourceServiceOperate;

    @Test
    public void resourceOperateTest() {
        List<EcsInstanceEntity> ecsInstanceEntities = ecsResourceServiceOperate.listByNeedToExpire();
        System.out.println(ecsInstanceEntities);

        List<DiskEntity> diskEntities = diskResourceServiceOperate.listByNeedToExpire();
        System.out.println(diskEntities);
    }


}
