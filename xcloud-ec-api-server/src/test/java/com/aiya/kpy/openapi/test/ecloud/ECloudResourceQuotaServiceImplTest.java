package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.service.impl.ECloudResourceQuotaServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/4/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudResourceQuotaServiceImplTest {
    @Resource
    private ECloudResourceQuotaServiceImpl eCloudResourceQuotaService;

    @Test
    public void getMinQuotaTest() {
        Integer minQuota = eCloudResourceQuotaService.getMinQuota(Params.poolId);
        System.out.println(minQuota);
    }
}
