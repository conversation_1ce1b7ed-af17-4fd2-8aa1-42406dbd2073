package com.aiya.kpy.openapi.test.prodInfoTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.message.ProdInfoChangeMessage;
import com.aiya.kpy.ec.task.mq.ProdInfoChangeMessageListener;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/4/24
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ProdInfoChangeMessageTest {
    @Resource
    private ProdInfoChangeMessageListener prodInfoChangeMessageListener;

    @Test
    public void renewResourceTest() {
        String str = "{\"configParams\":[{\"billingCycle\":\"MONTH\",\"buyTime\":1,\"region\":\"cn-guangzhou-3\",\"zone\":\"cn-guangzhou-3-a\",\"consumptTranType\":\"PAYMENT_BEFORE_DELIVERY\",\"configModel\":[{\"relation\":\"MASTER\",\"configJson\":\"{\\\"autoRenew\\\":\\\"Infinite\\\"}\",\"resourceType\":\"ECLOUD_ECS\",\"resourceUUID\":\"ecs-by00000eeirq\"}]}],\"orderType\":\"RENEW_PRODUCT\",\"timestamp\":1714356209,\"buyQuantity\":1}";
        ProdInfoChangeMessage message = JSONObject.parseObject(str, ProdInfoChangeMessage.class);
        prodInfoChangeMessageListener.receive(message);
    }
}
