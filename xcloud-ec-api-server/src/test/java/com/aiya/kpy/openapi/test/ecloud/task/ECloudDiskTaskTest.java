package com.aiya.kpy.openapi.test.ecloud.task;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.task.DeleteECloudDiskTask;
import com.aiya.kpy.openapi.test.ecloud.Params;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudDiskTaskTest {

    @Test
    public void testDeleteECloudEcsTask() throws Exception {
        DeleteECloudDiskTask deleteECloudDiskTask = DeleteECloudDiskTask.builder()
                .platformRegionCode(Params.poolId)
                .thirdDiskUUID(Params.volumeId)
                .build();
        Boolean result = deleteECloudDiskTask.call();
        System.out.println(result);
    }
}
