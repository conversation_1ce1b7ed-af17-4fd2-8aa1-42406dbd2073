package com.aiya.kpy.openapi.test.publicipTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.BandwidthType;
import com.aiya.kpy.common.enums.BooleanEnum;
import com.aiya.kpy.common.enums.UserPower;
import com.aiya.kpy.ec.foundation.utils.DateTimeUtils;
import com.aiya.kpy.ec.task.batch.ResourceBillCompensateHandler;
import com.aiya.kpy.publicip.api.PublicIpAPI;
import com.aiya.kpy.publicip.api.enums.PublicIpBindLevel;
import com.aiya.kpy.publicip.api.enums.PublicIpResourceType;
import com.aiya.kpy.publicip.api.request.*;
import com.aiya.kpy.publicip.api.response.*;
import com.aiya.kpy.publicip.task.batch.PublicIpBillHandler;
import com.aiya.platform.foundation.utils.UUIDGenernator;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.HashSet;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class PublicIpApiTest {
    @Autowired
    private PublicIpAPI publicIpAPI;
    @Autowired
    private ResourceBillCompensateHandler compensateResourceBillHandler;
    @Autowired
    private PublicIpBillHandler ipBillHandler;


    @Test
    public void testCreateBill(){
        try {
//            compensateResourceBillHandler.execute(null);
            ipBillHandler.execute(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testAssociatePublicIp(){
        AssociatePublicIpRequest request = new AssociatePublicIpRequest();
        request.setInstanceId("ecs-e90000211idvz");
        request.setIpId("eip-e100002d322cm");
        request.setIpBindLevel(PublicIpBindLevel.SECONDARY);
        request.setOprUserId(109627L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(2L);
        request.setDomainIds(hashSet);
        request.setClientToken(UUIDGenernator.nextUUID());
        AssociatePublicIpResponse response = publicIpAPI.associatePublicIp(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribePublicIpList() {
        DescribePublicIpListRequest request = new DescribePublicIpListRequest();
        request.setRegion("demo-2");
        request.setPublicIpType(PublicIpResourceType.EIP);
        request.setOprUserId(2L);
        request.setOprUserPower("Management");
//        request.setQueryUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        hashSet.add(2L);
        hashSet.add(3L);
        request.setDomainIds(hashSet);
//        request.setOprUserPower("Management");
//        request.setPageSize(10);
//        request.setPageNumber(1);
//        request.setQueryDomainId(3L);
        request.setPublicIpUUIDs(new String[]{"eip-6v000000ftga","eip-t9000000lvih", "eip-t9000000nno5"});

        DescribePublicIpListResponse response = publicIpAPI.describePublicIpList(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribePublicIpDetail() {
        DescribePublicIpDetailRequest request = new DescribePublicIpDetailRequest();
        request.setIpUUID("eip-6u0000gpnyxda6");
        request.setOprUserId(109595L);
        DescribePublicIpDetailResponse response = publicIpAPI.describePublicIpDetail(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribePublicIpMonitorData() {
        DescribePublicMonitorDataRequest request = new DescribePublicMonitorDataRequest();
        request.setOprUserId(109595L);
        request.setIpUUID("eip-as000000016");
        request.setBeginTime(DateTimeUtils.addDays(new Date(),-7));
        request.setEndTime(DateTimeUtils.addDays(new Date(),7));
        request.setPeriod(1);
        DescribePublicIpMonitorDataResponse response = publicIpAPI.describePublicIpMonitorData(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testReplacePublicIpAddress(){
        ReplacePublicIpAddressRequest request = new ReplacePublicIpAddressRequest();
        request.setOwnerId(109617L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(3L);
        request.setDomainIds(domainIds);
        request.setIpUUID("eip-1u0000gpnyxdc");
        request.setIpCidrBlock("**********/24");
        request.setBandwidthType(BandwidthType.BGP);
        request.setRemark("测试");
        request.setInheritFlow(BooleanEnum.TRUE);
        request.setClientToken(UUIDGenernator.nextUUID());
        request.setOprUserId(1L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        ReplacePublicIpAddressResponse response = publicIpAPI.replacePublicIpAddress(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribePublicIpOperatorLog(){
        DescribePublicIpOperationalLogRequest request = new DescribePublicIpOperationalLogRequest();
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(1L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
//        request.setRegionCode("demo-2");
        request.setOrderBy("operationTime");
        request.setOrderType("ASC");
        request.setEndTime(new Date());
        request.setStartTime(DateTimeUtils.addDays(new Date(), -3));
        final DescribePublicIpOperationalLogResponse response = publicIpAPI.describePublicIpOperationalLog(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testMonifyPublicIpAttribute(){
        ModifyPublicIpAttributeRequest request = new ModifyPublicIpAttributeRequest();
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109627L);
        request.setIpId("eip-e100002d322sv");
        request.setResourceName("xxx");
        ModifyPublicIpAttributeResponse response = publicIpAPI.modifyPublicIpAttribute(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testUnAssociatePublicIp(){
        UnassociatePublicIpRequest request = new UnassociatePublicIpRequest();
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109627L);
        request.setInstanceId("ecs-e90000211idvz");
        request.setIpId("eip-e100002d322sv");
        UnassociatePublicIpResponse response = publicIpAPI.unassociatePublicIp(request);
        System.out.println(JSON.toJSONString(response));
    }

}
