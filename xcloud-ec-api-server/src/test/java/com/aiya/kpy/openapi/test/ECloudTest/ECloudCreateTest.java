package com.aiya.kpy.openapi.test.ECloudTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.message.ProdInfoChangeMessage;
import com.aiya.kpy.ec.api.model.ProdConfiguration;
import com.aiya.kpy.ecs.foundation.handler.EcsOrderHandle;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/16 13:54
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudCreateTest {
    @Resource
    EcsOrderHandle handle;

    @Test
    public void createResourceTest() {
        String resourceSuffix = String.valueOf(System.currentTimeMillis());
        String str = "{\n" +
                "  \"_class\": \"com.aiya.kpy.common.message.ProdInfoChangeMessage\",\n" +
                "  \"orderId\": 321023,\n" +
                "  \"orderUUID\": \"yk00000fiajr\",\n" +
                "  \"orderPayStatus\": \"PAY_SUCCESS\",\n" +
                "  \"resourceType\": \"LIGHTNODE_ECS\",\n" +
                "  \"resourceUUID\": \"ecs-n700000fibl1\",\n" +
                "  \"messageType\": \"CREATE\",\n" +
                "  \"prodConfigJson\": \"{\\\"subnetId\\\":\\\"\\\",\\\"hostName\\\":\\\"20240522145548974923\\\",\\\"image\\\":\\\"linux\\\",\\\"instanceChargeType\\\":\\\"PostPaid\\\",\\\"multiItems\\\":[{\\\"diskSize\\\":50,\\\"diskType\\\":\\\"System\\\",\\\"diskCategory\\\":\\\"local_ssd\\\",\\\"multiItemId\\\":999}],\\\"imageId\\\":\\\"img-4k000006rjre\\\",\\\"imageName\\\":\\\"BT-Panel 7.7.0\\\",\\\"memory\\\":1,\\\"netType\\\":\\\"classicNet\\\",\\\"instanceType\\\":\\\"anygpug6\\\",\\\"cpuModel\\\":\\\"AMD EPYC 7302\\\",\\\"cpu\\\":1,\\\"osDistroVersion\\\":\\\"CentOS\\\",\\\"password\\\":\\\"N/Tr7r@~.)\\\",\\\"zone\\\":\\\"cn-fuzhou-8-a\\\",\\\"instanceSpec\\\":\\\"ecs.u1.m1\\\",\\\"vpcId\\\":\\\"\\\",\\\"autoRenew\\\":\\\"None\\\",\\\"region\\\":\\\"cn-fuzhou-8\\\"}\",\n" +
                "  \"userId\": 30888,\n" +
                "  \"orderType\": \"BuyProduct\",\n" +
                "  \"subResourceMessages\": {\n" +
                "    \"OWN_EIP\": [\n" +
                "      {\n" +
                "        \"resourceUUID\": \"eip-n700000fibl5\",\n" +
                "        \"prodConfigJson\": \"{\\\"bandwidthType\\\":\\\"BGP\\\",\\\"instanceChargeType\\\":\\\"PostPaid\\\",\\\"chargeMode\\\":\\\"PayByBandwidth\\\",\\\"bandwidth\\\":100,\\\"actualFlow\\\":1000,\\\"region\\\":\\\"cn-fuzhou-8\\\"}\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"OWN_DISK\": [\n" +
                "      {\n" +
                "        \"resourceUUID\": \"disk-n700000fibl9\",\n" +
                "        \"prodConfigJson\": \"{\\\"diskSize\\\":60,\\\"zone\\\":\\\"cn-fuzhou-8-a\\\",\\\"diskType\\\":\\\"Data\\\",\\\"diskCategory\\\":\\\"local_ssd\\\",\\\"region\\\":\\\"cn-fuzhou-8\\\"}\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"clientToken\": \"yk00000fiajw\",\n" +
                "  \"billingCycle\": \"MINUTE\",\n" +
                "  \"domainId\": 1\n" +
                "}";
        ProdInfoChangeMessage message = JSONObject.parseObject(str, ProdInfoChangeMessage.class);
        ProdConfiguration prodConfig = JSON.parseObject(message.getProdConfigJson(), ProdConfiguration.class);
        handle.createResource(prodConfig, message);
    }

    @Test
    public void refundResourceTest() {
//        String resourceSuffix = String.valueOf(System.currentTimeMillis());
        String resourceSuffix = "1713929635864";
        String str = "{\n" +
                "  \"messageType\" : \"CREATE\",\n" +
                "  \"orderId\" : 319458,\n" +
                "  \"orderPayStatus\" : \"PAY_SUCCESS\",\n" +
                "  \"orderType\" : \"BUY_PRODUCT\",\n" +
                "  \"billingCycle\" : \"MONTH\",\n" +
                "  \"buyTime\" : 1,\n" +
                "  \"orderUUID\" : \"dj00000b5ug1\",\n" +
                "  \"prodConfigJson\" : \"{\\\"subnetId\\\":\\\"\\\",\\\"hostName\\\":\\\"2024021914444155460\\\",\\\"instanceChargeType\\\":\\\"PrePaid\\\",\\\"multiItems\\\":[{\\\"diskSize\\\":200,\\\"diskType\\\":\\\"System\\\",\\\"diskCategory\\\":\\\"local_ssd\\\",\\\"multiItemId\\\":999}],\\\"imageId\\\":\\\"img-pv02300czjtq\\\",\\\"imageName\\\":\\\"Ubuntu 20.04 64位\\\",\\\"memory\\\":64,\\\"netType\\\":\\\"classicNet\\\",\\\"cpuModel\\\":\\\"Intel Xeon Gold 6248R\\\",\\\"instanceType\\\":\\\"gpu\\\",\\\"cpu\\\":8,\\\"videoMemory\\\":16,\\\"osDistroVersion\\\":\\\"\\\",\\\"password\\\":\\\"SJ=+yz}08\\\",\\\"zone\\\":\\\"cn-guangzhou-3-a\\\",\\\"gpuModel\\\":\\\"NVIDIA T4\\\",\\\"vpcId\\\":\\\"\\\",\\\"autoRenew\\\":\\\"Infinite\\\",\\\"gpuNum\\\":1,\\\"cpuSpec\\\":\\\"g4t.2xlarge.8\\\",\\\"region\\\":\\\"cn-guangzhou-3\\\"}\",\n" +
                "  \"resourceType\" : \"ECLOUD_ECS\",\n" +
                "  \"resourceUUID\" : \"ecs-" + resourceSuffix + "\",\n" +
                "  \"subResourceMessages\" : {\n" +
                "    \"ECLOUD_DISK\" : [\n" +
                "      {\n" +
                "        \"prodConfigJson\" : \"{\\\"diskSize\\\":200,\\\"zone\\\":\\\"cn-guangzhou-3-a\\\",\\\"diskType\\\":\\\"Data\\\",\\\"diskCategory\\\":\\\"local_ssd\\\",\\\"region\\\":\\\"cn-guangzhou-3\\\"}\",\n" +
                "        \"resourceUUID\" : \"disk-" + resourceSuffix + "\"\n" +
                "      }\n" +
                "    ],\n" +
                "    \"ECLOUD_EIP\" : [\n" +
                "      {\n" +
                "        \"prodConfigJson\" : \"{\\\"bandwidthType\\\":\\\"CMCC\\\",\\\"instanceChargeType\\\":\\\"PrePaid\\\",\\\"chargeMode\\\":\\\"PayByBandwidthPackage\\\",\\\"bandwidth\\\":20,\\\"region\\\":\\\"cn-guangzhou-3\\\"}\",\n" +
                "        \"resourceUUID\" : \"eip-" + resourceSuffix + "\"\n" +
                "      }\n" +
                "    ]\n" +
                "  },\n" +
                "  \"userId\" : 30888\n" +
                "}";
        ProdInfoChangeMessage message = JSONObject.parseObject(str, ProdInfoChangeMessage.class);
        ProdConfiguration prodConfig = JSON.parseObject(message.getProdConfigJson(), ProdConfiguration.class);
        handle.refundResource(prodConfig, message);
    }
}
