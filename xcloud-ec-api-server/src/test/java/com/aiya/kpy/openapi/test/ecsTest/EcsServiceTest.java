package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.*;
import com.aiya.kpy.common.util.AssertUtil;
import com.aiya.kpy.ec.api.constant.ErrorCodes;
import com.aiya.kpy.ec.foundation.iaas.model.SwitchProviderData;
import com.aiya.kpy.ec.foundation.iaas.service.ResourceProviderService;
import com.aiya.kpy.ec.foundation.provider.IaasServiceFactory;
import com.aiya.kpy.ec.task.dingding.DingDingNoticeEvent;
import com.aiya.kpy.ecs.foundation.entity.EcsInstanceAcceleratorNodeEntity;
import com.aiya.kpy.ecs.foundation.entity.EcsInstanceEntity;
import com.aiya.kpy.ecs.foundation.model.TimeParam;
import com.aiya.kpy.ecs.foundation.service.EcsAcceleratorNodeService;
import com.aiya.kpy.ecs.foundation.service.EcsService;
import com.aiya.kpy.ecs.foundation.service.impl.ECloudEcsSaleStockServiceImpl;
import com.aiya.kpy.ecs.task.batch.ReleaseStopEcsInstanceHandler;
import com.aiya.platform.foundation.exception.BusinessException;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EcsServiceTest {
    @Autowired
    private EcsService ecsService;
    @Autowired
    private ApplicationEventPublisher publisher;
    @Autowired
    private ReleaseStopEcsInstanceHandler releaseStopEcsInstanceHandler;
    @Resource
    private ECloudEcsSaleStockServiceImpl ecsSaleStockService;
    @Resource
    private EcsAcceleratorNodeService ecsAcceleratorNodeService;

    /**
     * 测试创建主机
     */
    @Test
    public void testCreateEcs() {
        try {
//			final DiskEntity a = ecsService.createInstance();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试释放主机
     */
    @Test
    public void testReleaseEcs() {
        try {
            ecsService.releaseEcsFromRecycleStation("ecs-123456", Boolean.FALSE);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试回收主机
     */
    @Test
    public void testRecycleEcs() {
        final EcsInstanceEntity ecsInstanceEntity = ecsService.queryInstanceByPlatformUUID("ecs-11368996035");
        ecsService.addToRecycle(123L, ecsInstanceEntity, new Date(), null, "1234", ProdInfoChangeMessageType.RECYCLE);
    }

    /**
     * 测试续费主机
     */
    @Test
    public void testRenewEcs() {
        final EcsInstanceEntity ecsInstanceEntity = ecsService.queryEcsEntityByResourceUUID("ecs-11368996035");
        TimeParam timeParam = new TimeParam();
        timeParam.setTryOut(false);
        timeParam.setPeriod(12);
        timeParam.setBillingperiod(BillingCycleEnum.MONTH);
        ecsService.renewEcs(ecsInstanceEntity, timeParam, AutoRenewStatus.INFINITE, "123", null, null, null);
    }

    @Test
    public void testDingDing() {
        //报警
        String url = "https://oapi.dingtalk.com/robot/send?access_token=d1094eec7f1d569f6d355d6667eec153922b8d2df21e813016f5fc00f3d5773d";
        String secret = "SECc1e23f4e08ee17708e66e06cc729498853cb71076ce1b72cab5900841858fba0";
        StringBuffer content = new StringBuffer("你好，\n")
                .append("测试不@所有人~");
        publisher.publishEvent(new DingDingNoticeEvent(content, Lists.newArrayList("null"), url, false, secret));
    }


    @Test
    public void testReleaseStopEcsNotifyHandler() throws Exception {
        System.out.println("测试releaseStopEcsInstanceHandler");
        Thread.sleep(3000L);
        releaseStopEcsInstanceHandler.execute2("2024-01-09 14:00:00");
        //releaseStopEcsInstanceHandler.execute(null);
    }


    @Test
    public void testGetGpuFreeCount() {
        Integer freeCount = ecsService.getGpuNumModel("cn-fuzhou-8", null).getAvailableFreeGpuNum();
        System.out.println(freeCount);
    }

    @Test
    public void testUpdateSkuSaleManage() throws Exception {
        System.out.println("testUpdateSkuSaleManage");
        Thread.sleep(3000L);
        ecsSaleStockService.updateSkuSaleManage("cn-xiamen-1");
        //ecsSaleStockService.updateSkuSaleManage("cn-dev-2");
    }

    @Test
    public void testGetAcceleratorNodeByRegionCode() {
        EcsInstanceAcceleratorNodeEntity acceleratorNode = ecsAcceleratorNodeService.getAcceleratorNodeByRegionCode("cn-dev-2", AcceleratorTypeEnum.NAT_ACCELERATOR);
        System.out.println(JSON.toJSONString(acceleratorNode));
        AssertUtil.notNull(acceleratorNode, () -> new BusinessException(ErrorCodes.INVALID_ECS_ACCELERATOR_NODE_NOT_EXISTED));
    }

    @Test
    public void testGetExcludeSubnetVswitch() {
        String[] excludeSubnetVswitch = this.excludeSubnetVswitch(ResourceProvider.OWN, "cn-fuzhou-8");
        System.out.println(JSON.toJSONString(excludeSubnetVswitch));
    }

    private String[] excludeSubnetVswitch(ResourceProvider resourceProvider, String regionCode) {
        ResourceProviderService resourceProviderService = IaasServiceFactory.getIaasService(resourceProvider, ResourceProviderService.class);
        List<SwitchProviderData> switchProviderDataList = resourceProviderService.loadSwitchDatasByBandwidthType(regionCode, BandwidthTypeEnum.INTRANET);
        return switchProviderDataList.stream().map(SwitchProviderData::getVswitchId).toArray(String[]::new);
    }
}
