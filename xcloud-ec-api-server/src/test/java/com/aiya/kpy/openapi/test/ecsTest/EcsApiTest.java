package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.AutoRenewStatus;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.common.enums.UserPower;
import com.aiya.kpy.ec.api.enums.EcsStatus;
import com.aiya.kpy.ec.api.enums.MonitorPeriod;
import com.aiya.kpy.ec.foundation.utils.DateTimeUtils;
import com.aiya.kpy.ecs.api.EcsAPI;
import com.aiya.kpy.ecs.api.request.*;
import com.aiya.kpy.ecs.api.response.*;
import com.aiya.kpy.ecs.foundation.model.EcsInstanceTaskModel;
import com.aiya.kpy.ecs.foundation.service.EcsService;
import com.aiya.kpy.ecs.task.batch.EcsBillHandler;
import com.aiya.kpy.ecs.task.batch.ReleaseEcsInstanceHandler;
import com.aiya.kpy.ecs.task.batch.ResourceAutoExpireHandler;
import com.aiya.kpy.publicip.task.batch.PublicIpFlowMonitorHandler;
import com.aiya.platform.foundation.utils.UUIDGenernator;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EcsApiTest {
    @Autowired
    private EcsAPI ecsAPI;
    @Autowired
    private EcsService ecsService;
    @Autowired
    private ReleaseEcsInstanceHandler releaseEcsInstanceHandler;
    @Autowired
    private PublicIpFlowMonitorHandler flowMonitorHandler;
    @Autowired
    private EcsBillHandler ecsBillHandler;

    @Autowired
    private ResourceAutoExpireHandler resourceAutoExpireHandler;

    @Test
    public void testBill() {
        try {
            ecsBillHandler.execute(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testSuperReleaseEcs() {
        SuperReleaseEcsRequest request = new SuperReleaseEcsRequest();
        request.setOwnerId(109724L);
        request.setOprUserId(1L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(145L);
        request.setDomainIds(domainIds);
        request.setResourceUUID("ecs-58000029otkk");

        SuperReleaseEcsResponse response = ecsAPI.superReleaseEcs(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeEcsCount() {
        DescribeEcsQuantityRequest request = new DescribeEcsQuantityRequest();
        request.setQueryUserId(109595L);
        request.setOprUserId(1L);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        domainIds.add(160L);
        domainIds.add(2L);
        domainIds.add(162L);
        domainIds.add(164L);
        domainIds.add(166L);
        domainIds.add(167L);
        domainIds.add(145L);
        domainIds.add(146L);
        domainIds.add(148L);
        domainIds.add(156L);
        domainIds.add(159L);
        request.setDomainIds(domainIds);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        DescribeEcsQuantityResponse response = ecsAPI.describeEcsQuantity(request);
        System.out.println(JSON.toJSONString(response));

    }

    @Test
    public void testFlowMonitor() {
        try {
            flowMonitorHandler.execute(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("执行完毕");
    }

    @Test
    public void testExcute() {
        try {
            releaseEcsInstanceHandler.execute(null);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println("执行完成");
    }

    @Test
    public void testDescribeEcsDetail() {
        DescribeEcsDetailRequest request = new DescribeEcsDetailRequest();
        request.setEcsResourceUUID("ecs-8b0002lja781da3");
        request.setRegionCode("demo-1");
        request.setZoneCode("demo-1-a");
//		request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        request.setOprUserId(109595L);
//		request.setOprUserId(1L);

        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        final DescribeEcsDetailResponse response = ecsAPI.queryEcsDetail(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testStopEcs() {
        EcsStopInstanceRequest request = new EcsStopInstanceRequest();
        request.setEcsInstanceUUID("ecs-a20000211d1z");
        request.setRegion("demo-2");
        request.setZone("demo-2-a");
        request.setOprUserId(109595L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);

        final EcsStopInstanceResponse response = ecsAPI.stopInstance(request);
        try {
            Thread.sleep(60000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testStartEcs() {
        EcsStartInstanceRequest request = new EcsStartInstanceRequest();
        request.setEcsInstanceUUID("ecs-a20000211d1z");
        request.setRegion("demo-2");
        request.setZone("demo-2-a");
        request.setOprUserId(109595L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        EcsStartInstanceResponse response = ecsAPI.startInstance(request);
        try {
            Thread.sleep(60000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testRebootEcs() {
        EcsRebootInstanceRequest request = new EcsRebootInstanceRequest();
        request.setEcsInstanceUUID("ecs-a20000211d1z");
        request.setRegion("demo-2");
        request.setZone("demo-2-a");
        request.setOprUserId(109595L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        EcsRebootInstanceResponse response = ecsAPI.rebootInstance(request);
        try {
            Thread.sleep(60000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeEcsMonitorData() {
        EcsMonitorDataRequest request = new EcsMonitorDataRequest();
        request.setResourceUUID("ecs-8b0002lja781da3");
        request.setEndTime(new Date());
        request.setStartTime(DateTimeUtils.addDays(new Date(), -1));
        request.setMonitorPeriod(MonitorPeriod.ONE_MINUTE);
        request.setOprUserId(109595L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        EcsMonitorDataResponse response = ecsAPI.queryEcsMonitorData(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeEcsList() {
        DescribeEcsListRequest request = new DescribeEcsListRequest();
        request.setRegionCode("demo-2");
//		request.setOprUserId(109624L);
        request.setOprUserId(1L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        request.setRequestMicroServer("supervisor");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(145L);
//		domainIds.add(2L);
        request.setDomainIds(domainIds);
//		request.setQueryDomainId(1L);
//		request.setTenantId(109624L);
        request.setResourceType(new ResourceType[]{ResourceType.LIGHTNODE_ECS});
        request.setInstanceUUIDs(new String[]{"ecs-ym00001zbzhi", "ecs-ym00001zc0m1", "ecs-ym00001zc238", "ecs-ym00001zc2dx", "ecs-rs00001zs9wb"});
//		request.setEcsStatus(EcsStatus.RECYCLE);
//		request.setIncludeRecycle(BooleanEnum.TRUE);
//		request.setEcsStatusFilter(Arrays.asList(EcsStatus.RECYCLE));
//		request.setEcsPendingStatusFilter(Arrays.asList(EcsPendingStatus.RECYCLING));
        DescribeEcsListResponse response = ecsAPI.queryEcsList(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testAdvanceEndTryOut() {
        AdvanceEndEcsTryOutRequest request = new AdvanceEndEcsTryOutRequest();
        request.setResourceUUID("ecs-9c0200s217da");
        request.setUserId(109595L);
        request.setOprUserId(1L);
        request.setClientToken(UUIDGenernator.nextUUID());
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        final AdvanceEndEcsTryOutResponse response = ecsAPI.advanceEndEcsTryOut(request);
        try {
            Thread.sleep(150000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testEcsRename() {
        DescribeEcsRenameRequest request = new DescribeEcsRenameRequest();
        request.setEcsResourceUUID("ecs-e90000211idvz");
        request.setOprUserId(109627L);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        request.setDomainIds(domainIds);
        request.setNewEcsName("userId: 109627,domainId: 2");
        DescribeEcsRenameResponse response = ecsAPI.ecsRename(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testEcsRenewStatus() {
        DescribeModifyEcsRenewRequest request = new DescribeModifyEcsRenewRequest();
        request.setEcsResourceUUID("ecs-e90000211idvz");
        request.setAutoRenew(AutoRenewStatus.INFINITE);
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109627L);
        DescribeModifyEcsRenewResponse response = ecsAPI.modifyEcsRenewStatus(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testReinstallSystem() {
        DescribeEcsReinstallSystemRequest request = new DescribeEcsReinstallSystemRequest();
        request.setEcsResourceUUID("ecs-a20000211d1z");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        request.setRegionCode("demo-2");
        request.setZoneCode("demo-2-a");
        request.setImageResourceUUID("img-573aa1b35085bb001");
        request.setPassword("12345678aA");
        request.setClientToken(UUIDGenernator.nextUUID());
        DescribeEcsReinstallSystemResponse response = ecsAPI.ecsReinstallSystem(request);
        try {
            Thread.sleep(90000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testReleaseFromRecycle() {
        DescribeReleaseEcsFromRecycleStationRequest request = new DescribeReleaseEcsFromRecycleStationRequest();
        request.setEcsResourceUUID("ecs-e9c000s217da");
        request.setRegionCode("demo-2");
        request.setZoneCode("demo-2-a");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        DescribeReleaseEcsFromRecycleStationResponse response = ecsAPI.releaseEcsFromRecycleStation(request);
        try {
            Thread.sleep(50000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testModifyEcsPassword() {
        DescribeModifyEcsPasswordRequest request = new DescribeModifyEcsPasswordRequest();
        request.setEcsResourceUUID("ecs-a20000211d1z");
        request.setPassword("123456789aA");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        DescribeModifyEcsPasswordResponse response = ecsAPI.modifyEcsPassword(request);
        try {
            Thread.sleep(50000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testQueryDdosEcsList() {
        //TODO
    }

    @Test
    public void testCheckResizeEcs() {
        CheckResizeInstanceInfoRequest request = new CheckResizeInstanceInfoRequest();
        request.setResourceUUID("ecs-8b0002lja781da3");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        CheckResizeInstanceInfoResponse response = ecsAPI.checkResizeInstanceInfo(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testQueryEcsVpnUrl() {
        DescribeVncRequest request = new DescribeVncRequest();
        request.setEcsResourceUUID("ecs-8b0002lja781da3");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        DescribeVncResponse response = ecsAPI.getInstanceVncUrl(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testEcsReinstallOperationLog() {
        DescribeEcsReinstallOperationLogRequest request = new DescribeEcsReinstallOperationLogRequest();
        request.setEcsResourceUUID("ecs-8b0002lja781da3");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        DescribeEcsReinstallOperationLogResponse response = ecsAPI.getEcsReinstallOperationLog(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testPseudoDeleteEcs() {
        PseudoDeleteEcsRequest request = new PseudoDeleteEcsRequest();
        request.setEcsUUID("ecs-e90000211idvz");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109627L);
        PseudoDeleteEcsResponse response = ecsAPI.pseudoDeleteEcs(request);
        System.out.println(JSON.toJSONString(response));

    }

    @Test
    public void testUpdatePseudoDeleteSuccess() {
        EcsInstanceTaskModel taskModel = new EcsInstanceTaskModel();
        taskModel.setEcsInstanceId(753L);
        taskModel.setEcsInstanceUUID("ecs-e900002517da");
//		taskModel.setIaaSEcsUUID(813L);
        taskModel.setNewStatus(EcsStatus.LOCKED);

        //ecsService.updateLockOrDeleteEcsSuccess(taskModel);
        System.out.println("执行结束");
    }

    @Test
    public void testPause() {
        PseudoDeleteEcsRequest request = new PseudoDeleteEcsRequest();
        request.setEcsUUID("ecs-9c0200s217d1a");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);
        request.setOprUserId(109595L);
        PseudoDeleteEcsResponse response = ecsAPI.pseudoDeleteEcs(request);
        try {
            Thread.sleep(30000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));

//		ecsService.lockOrDeleteEcs("ecs-a90000211idvz", EcsStatus.LOCKED);

//		ecsService.resumeEcsInstance(ecsService.queryEcsEntityByResourceUUID("ecs-a90000211idvz"), BooleanEnum.TRUE);
//		System.out.println("执行结束");
    }

    @Test
    public void testRecoverEcs() {
        RecoverEcsRequest request = new RecoverEcsRequest();
        request.setOprUserId(1L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(1L);
        request.setDomainIds(domainIds);

        request.setEcsResourceUUID("ecs-9c0200s217d1a");
        RecoverEcsResponse response = ecsAPI.recoverEcs(request);
        try {
            Thread.sleep(50000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testCreateEcs() {

//		ecsService.createInstance();
    }

    @Test
    public void testResourceAutoExpireHandler() {
        try {
            resourceAutoExpireHandler.execute(null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
