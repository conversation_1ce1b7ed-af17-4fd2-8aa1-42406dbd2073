package com.aiya.kpy.openapi.test.tasktest;

import com.SpringBootStarter;
import com.aiya.kpy.async.monitor.AsyncTaskMonitorEventListener;
import com.aiya.platform.asynctask.dao.mongo.PlatAsyncTaskLogDAO;
import com.aiya.platform.asynctask.entity.mongo.PlatAsyncTaskLogEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/2/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class TaskMonitorTest {
    @Resource
    private AsyncTaskMonitorEventListener asyncTaskMonitorEventListener;
    @Resource
    private PlatAsyncTaskLogDAO platAsyncTaskLogDAO;

    @Test
    public void testCreateEcs() {
        PlatAsyncTaskLogEntity log = platAsyncTaskLogDAO.findOneByTaskIdResourceIdAndTaskName(2191826L, "ecs-4600000rzj5g", "createEcsTaskName");
        String traceId = asyncTaskMonitorEventListener.getTraceId(log.getBusinessData());
        System.out.println(traceId);
    }
}
