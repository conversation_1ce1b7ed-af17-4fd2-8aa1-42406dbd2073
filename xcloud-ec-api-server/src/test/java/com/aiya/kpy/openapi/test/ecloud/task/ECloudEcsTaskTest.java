package com.aiya.kpy.openapi.test.ecloud.task;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.task.DeleteECloudEcsTask;
import com.aiya.kpy.ec.foundation.ecloud.task.RenewECloudEcsTask;
import com.aiya.kpy.openapi.test.ecloud.Params;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2024/3/28
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudEcsTaskTest {

    @Test
    public void testDeleteECloudEcsTask() throws Exception {
        DeleteECloudEcsTask deleteECloudEcsTask = DeleteECloudEcsTask.builder()
                .platformRegionCode(Params.poolId)
                .thirdInstanceUUID(Params.serverId)
                .build();
        Boolean result = deleteECloudEcsTask.call();
        System.out.println(result);
    }

    @Test
    public void testRenewECloudEcsTask() throws Exception {
        RenewECloudEcsTask renewECloudEcsTask = RenewECloudEcsTask.builder()
                .platformRegionCode(Params.poolId)
                .thirdInstanceUUID(Params.serverId)
                .duration(1)
                .build();
        Boolean result = renewECloudEcsTask.call();
        System.out.println(result);
    }


}
