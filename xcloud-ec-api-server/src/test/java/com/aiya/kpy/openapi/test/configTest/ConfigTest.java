package com.aiya.kpy.openapi.test.configTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceProvider;
import com.aiya.kpy.ec.foundation.config.ZoneProviderAccount;
import com.aiya.kpy.ec.foundation.config.ZoneProviderAccountConfig;
import com.aiya.kpy.ecs.task.batch.InstanceShareTokenExpireHandler;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/5/9
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ConfigTest {
    @Resource
    private ZoneProviderAccountConfig zoneProviderAccountConfig;

    @Test
    public void getZoneProviderAccountByProviderTest() {
        ZoneProviderAccount account = zoneProviderAccountConfig.getZoneProviderAccountByProvider(ResourceProvider.ECLOUD);
        System.out.println(JSON.toJSONString(account));
    }

    @Resource
    private InstanceShareTokenExpireHandler instanceShareTokenExpireHandler;

    @Test
    public void instanceShareTokenExpireHandler() throws Exception {
        instanceShareTokenExpireHandler.execute(null);
    }
}
