package com.aiya.kpy.openapi.test.dingdingTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.DateStyleEnum;
import com.aiya.kpy.common.util.DateUtil;
import com.aiya.kpy.ec.task.dingding.DingDingNoticeEvent;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class DingDingNoticeTest {
	@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
	@Autowired
	private ApplicationEventPublisher publisher;

	@Test
	public void testBroadMsgNotice() {
		Map<String, String> noticeMap = new HashMap<>(6);
		noticeMap.put("orderUUID", "123456");
		noticeMap.put("resourceUUID", "ecs-123456");
		noticeMap.put("messageType", "CREATE");
		noticeMap.put("resourceType", "OwnEcs");
		noticeMap.put("errorMsg", "Hello World! i am xds");
		noticeMap.put("createTime", DateUtil.dateToString(new Date(), DateStyleEnum.YYYY_MM_DD_HH_MM_CN));
		String content = JSON.toJSONString(noticeMap);
		publisher.publishEvent(new DingDingNoticeEvent(content));
	}

}

