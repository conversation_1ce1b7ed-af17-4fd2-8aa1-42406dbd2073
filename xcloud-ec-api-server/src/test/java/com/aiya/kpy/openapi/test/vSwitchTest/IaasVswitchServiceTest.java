package com.aiya.kpy.openapi.test.vSwitchTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSCreateVswitchException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSReleaseVswitchException;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSVswitchServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasVswitchServiceTest {
    @Autowired
    private IaaSVswitchServiceImpl iaaSVswitchService;

    /**
     * 创建
     */
    @Test
    public void testCreateVswitch() {
        try {
            String iaasVswithUUID = iaaSVswitchService.createVswitch("vnet-p30000002833", "demo-1",
                    "demo-1-a", "05ea2623-644e-4908-9d4a-3bd0c90de45a", "172.16.5.0/24");
            System.out.println(iaasVswithUUID);
        } catch (IaaSCreateVswitchException e) {
            System.out.println(e.getMessage());
        }
    }

    /**
     * 释放
     */
    @Test
    public void testReleaseVswitch() {
        try {
            iaaSVswitchService.releaseVSwitch("vnet-p30000002833", "demo-1", "demo-1-a", "a66f8762-f40b-40b8-87c5-41a14357a4e5", "05ea2623-644e-4908-9d4a-3bd0c90de45a");
        } catch (IaaSReleaseVswitchException e) {
            System.out.println(e.getMessage());
        }
    }

}
