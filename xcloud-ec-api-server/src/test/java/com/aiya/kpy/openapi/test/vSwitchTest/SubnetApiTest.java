package com.aiya.kpy.openapi.test.vSwitchTest;

import com.SpringBootStarter;
import com.aiya.kpy.subnet.api.SubnetAPI;
import com.aiya.kpy.subnet.api.request.*;
import com.aiya.kpy.subnet.api.response.*;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.HashSet;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SubnetApiTest {
    @Autowired
    private SubnetAPI subnetAPI;

    @Test
    public void testCreateSubnet() {
        CreateSubnetRequest request = new CreateSubnetRequest();
        request.setRegion("demo-1");
        request.setCidrBlock("**********/24");
        request.setDescription("测试用例api");
        request.setVpcId("vpc-8s000025q7j3");
        request.setSubnetName("test");
        request.setZone("demo-1-a");
        request.setOprUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        CreateSubnetResponse response = subnetAPI.createSubnet(request);
        try {
            Thread.sleep(30000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testCreateVlan() {
        CreateVlanSubnetRequest request = new CreateVlanSubnetRequest();
        request.setRegion("demo-1");
        request.setDescription("测试用例api");
        request.setSubnetName("test");
        request.setZone("demo-1-a");
        request.setOprUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        CreateVlanSubnetResponse response = subnetAPI.createVlanSubnet(request);
        try {
            Thread.sleep(50000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeSubnetList() {
        DescribeSubnetListRequest request = new DescribeSubnetListRequest();
        request.setRegion("demo-1");
        request.setOprUserId(1L);
//        request.setQueryUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        request.setOprUserPower("Management");
        request.setVpcId("vpc-x80000008kme");
//        request.setPageSize(10);
//        request.setPageNumber(1);
        request.setSubnetUUIDs(new String[]{"vnet-ma00000023aa","vnet-kb0000002grg"});

        DescribeSubnetListResponse response = subnetAPI.describeSubnetList(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDescribeSubnetDetail() {
        DescribeSubnetDetailRequest request = new DescribeSubnetDetailRequest();
        request.setSubnetId("vnet-ta00000027s3");
        request.setOprUserId(109595L);
        DescribeSubnetDetailResponse response = subnetAPI.describeSubnetDetail(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDeleteSubnet() {
        DeleteSubnetRequest request = new DeleteSubnetRequest();
        request.setSubnetId("vnet-i7000025qa73");
        request.setOprUserId(109595L);
        HashSet<Long> hashSet = new HashSet<>();
        hashSet.add(1L);
        request.setDomainIds(hashSet);
        DeleteSubnetResponse response = subnetAPI.deleteSubnet(request);
        try {
            Thread.sleep(30000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testModifyVpcAttribute() {
        ModifySubnetAttributeRequest request = new ModifySubnetAttributeRequest();
        request.setSubnetId("vnet-ta00000027s3");
        request.setSubnetName("newName");
        request.setDescription("new....");
        request.setOprUserId(109595L);
        ModifySubnetAttributeResponse response = subnetAPI.modifySubnetAttribute(request);
        System.out.println(JSON.toJSONString(response));
    }

}
