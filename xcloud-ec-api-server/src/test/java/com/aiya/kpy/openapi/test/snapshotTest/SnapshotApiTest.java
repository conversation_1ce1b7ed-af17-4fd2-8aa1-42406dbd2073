package com.aiya.kpy.openapi.test.snapshotTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.common.enums.UserPower;
import com.aiya.kpy.snapshot.api.SnapshotAPI;
import com.aiya.kpy.snapshot.api.request.*;
import com.aiya.kpy.snapshot.api.response.*;
import com.aiya.platform.foundation.utils.UUIDGenernator;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Set;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SnapshotApiTest {
    private static final String regionCode = "demo-2";
    private static final String zoneCode = "demo-2-a";
    private static final Long userId = 109725L;
    private static final Long domainId = 145L;
    @Autowired
    private SnapshotAPI snapshotAPI;

    /**
     * 测试快照列表接口
     */
    @Test
    public void testGetSnapshotList() {
        DescribeSnapshotListRequest request = new DescribeSnapshotListRequest();
        request.setOprUserId(userId);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
//		request.setInstanceUUID("ecs-iw000003g3bi");
        request.setRegion(regionCode);
//		request.setDiskType(DiskType.SYSTEM.getCode());
//		request.setDiskId("disk-iw000003g3bx");
//		request.setField("instanceId");
//		request.setKeyword("ecs-iw000003g3bi");
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
		request.setSnapshotUUIDs(new String[]{"snp-7z000025y765"});
        DescribeSnapshotListResponse response = snapshotAPI.describeSnapshotListByCondition(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testCreateLocalDiskSnapshot() {
        CreateSnapshotByLocalDiskRequest request = new CreateSnapshotByLocalDiskRequest();
        request.setDescription("接口创建快照-cyw");
        String diskUUID = "disk-zp000031e4c6";
        request.setDiskUUID(diskUUID);
        String region = "demo-2";
        String zone = "demo-2-a";
        Long userId = 109725L;
        request.setRegionCode(region);
        request.setZoneCode(zone);
        request.setSnapshotName("test-snapshot by LocalDisk-CYW");
        request.setOprUserId(userId);
        request.setClientToken(UUIDGenernator.nextUUID());

        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        CreateSnapshotByLocalDiskResponse response = snapshotAPI.createSnapshotByLocalDisk(request);
        try {
            Thread.sleep(10000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString("ASyn_ID"+response.getAsyncTaskUUID()));
    }

    @Test
    public void testCreateSnapshotByInstance() {
        CreateSnapshotByInstanceRequest request = new CreateSnapshotByInstanceRequest();
        request.setInstanceResourceType(ResourceType.FORTNODE_ECS);
        request.setInstanceUUID("ecs-zp0000326m75");
        request.setRegion("demo-2");
        request.setSnapshotName("test-snapshot-instance-CYWdd--");
        request.setZone("demo-2-a");
        request.setDescription("接口测试-创建实例-镜像");
        request.setOprUserId(109725L);
        request.setClientToken(UUIDGenernator.nextUUID());

        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(145L);
        request.setDomainIds(domainIds);
        CreateSnapshotByInstanceResponse response = snapshotAPI.createSnapshotByInstance(request);
        System.out.println(JSON.toJSONString("RESPONSE:"+response.getAsynctaskUUID()));

    }

    @Test
    public void testDeleteLocalDiskSnapshot() {
        DeleteSnapshotByLocalDiskRequest request = new DeleteSnapshotByLocalDiskRequest();
        request.setSnapshotUUID("snp-o8000032dnla");
        request.setOprUserId(userId);
        request.setClientToken(UUIDGenernator.nextUUID());

        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        DeleteSnapshotByLocalDiskResponse response = snapshotAPI.deleteSnapshotByLocalDisk(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testCreateSnapshotByDisk() {
        CreateSnapshotByDiskRequest request = new CreateSnapshotByDiskRequest();
        request.setDescription("接口创建快照 __ CYW");
        request.setDiskUUID("disk-t30000326m5a");
        request.setRegion(regionCode);
        request.setZone(zoneCode);
        request.setSnapshotName("test-snapshot by Disk _ _ CYW");
        request.setOprUserId(userId);
        request.setClientToken(UUIDGenernator.nextUUID());
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        CreateSnapshotByDiskResponse response = snapshotAPI.createSnapshotByDisk(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testDeleteDiskSnapshot() {
        DeleteSnapshotByLocalDiskRequest request = new DeleteSnapshotByLocalDiskRequest();
        request.setSnapshotUUID("snp-ao000032dfbe");
        request.setOprUserId(userId);
        request.setClientToken(UUIDGenernator.nextUUID());

        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        DeleteSnapshotByLocalDiskResponse response = snapshotAPI.deleteSnapshotByLocalDisk(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void testRollbackDisk() {
        ResetDiskRequest request = new ResetDiskRequest();
        request.setOprUserId(userId);
        request.setRegion(regionCode);
        request.setZone(zoneCode);
        request.setSnapshotId("snp-ic000032dnr8");
        request.setClientToken(UUIDGenernator.nextUUID());

        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        ResetDiskResponse response = snapshotAPI.rollbackDisk(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }
}
