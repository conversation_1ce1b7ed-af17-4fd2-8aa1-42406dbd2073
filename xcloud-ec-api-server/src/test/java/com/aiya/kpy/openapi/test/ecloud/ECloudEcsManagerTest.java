package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudEcsManager;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudImsManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.*;
import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.sdk.request.CreateInstanceRequest;
import com.aiya.kpy.res.ecloud.sdk.response.*;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateRequestBootVolume;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateRequestDataVolume;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VmCreateRequestNetworks;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/29
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudEcsManagerTest {
    @Resource
    private ECloudEcsManager eCloudEcsManager;
    @Resource
    private ECloudImsManager eCloudImsManager;


    @Test
    public void testGetInstances() throws DescribeInstancesException {
        GetInstancesResponse response = eCloudEcsManager.getInstances(Params.poolId, Params.serverId, 1, 10);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"availabilityZone":"ecloud_normal_zone","bootVolumeType":"PERFORMANCEOPTIMIZATION","createdTime":"2024-03-12 17:17:49","credibleStatus":"UNOPENED","deleted":0,"ecStatus":"ACTIVE","flavorRef":"ecloud-normal-5118-0.5-000100020020","groupId":"","hwHostId":"","id":"85b7c06c-9700-471e-87f1-69ab406ac3a4","imageName":"BC-Linux 7.1 64位","imageOsType":"LINUX","imageRef":"9aa71e7c-ec11-407c-8af0-17e45ec93771","keyName":"testCreateKeyPair1","name":"testopenapi","opStatus":"OK","operationFlag":0,"productType":"NORMAL","recycleCount":0,"recycleStatus":"UNRECYCLE","region":"DGJD","serverType":"VM","serverVmType":"COMMON","specsName":"s1.medium.2","status":1,"systemDiskId":"6a55bd2c-1fff-49ed-942f-f1bf8ba0f90f","task":"","userId":"CIDC-U-5c1b7c2d213f443db148d7a7e5ca1699","vcpu":1,"vdisk":40,"vmemory":2048}],"page":1,"size":10,"total":1}
        //{"content":[{"availabilityZone":"ecloud_normal_zone","bootVolumeType":"PERFORMANCEOPTIMIZATION","createdTime":"2024-03-12 17:17:49","credibleStatus":"UNOPENED","deleted":0,"ecStatus":"SHUTOFF","flavorRef":"ecloud-normal-5118-0.5-000100020020","groupId":"","hwHostId":"","id":"85b7c06c-9700-471e-87f1-69ab406ac3a4","imageName":"BC-Linux 7.1 64位","imageOsType":"LINUX","imageRef":"9aa71e7c-ec11-407c-8af0-17e45ec93771","keyName":"testCreateKeyPair1","name":"testopenapi","opStatus":"OK","operationFlag":0,"productType":"NORMAL","recycleCount":0,"recycleStatus":"UNRECYCLE","region":"DGJD","serverType":"VM","serverVmType":"COMMON","specsName":"s1.medium.2","status":16,"systemDiskId":"6a55bd2c-1fff-49ed-942f-f1bf8ba0f90f","task":"","userId":"CIDC-U-5c1b7c2d213f443db148d7a7e5ca1699","vcpu":1,"vdisk":40,"vmemory":2048}],"total":1}
    }

    @Test
    public void testGetInstanceDetail() throws DescribeInstancesException {
        //GetInstanceDetailRequest request = GetInstanceDetailRequest.builder().serverId(Params.serverId).detail(false).build();
        //GetInstanceDetailRequest request = GetInstanceDetailRequest.builder().serverId("7494769c-249c-433b-82ba-7c99eb6d92d8").detail(false).build();
        GetInstanceDetailResponse response = eCloudEcsManager.getInstanceDetail(Params.poolId, "f13016c3-7bec-4b2c-939f-11a5481aa28f", true);
        System.out.println(JSON.toJSONString(response));
        //{"availabilityZone":"ecloud_normal_entry_level_zone","bootVolumeType":"PERFORMANCEOPTIMIZATION","cpu":1,"createdTime":"2024-03-25 16:21:49","disk":40,"flavorId":"ecloud-normalEntry-5118-0.2-000100020020","id":"445ec824-220f-410a-9068-62ee1285c23e","imageId":"9aa71e7c-ec11-407c-8af0-17e45ec93771","imageName":"BC-Linux 7.1 64位","keypairName":"testCreateKeyPair1","maxBandwidth":"0.2","memory":2048,"modifiedTime":"2024-03-25 16:55:45","name":"testopenapi","recycle":false,"region":"DGJD","serverVmType":"COMMONINTRODUCTORY","specsName":"t2.medium.2","status":1,"systemDisk":"5deace2d-bd38-4f86-b9c4-28a990eec77a"}
    }

    @Test
    public void testGetProductOffer() throws DescribeInstancesException {
        List<GetProductOfferResponseContent> response = eCloudEcsManager.getProductOffer(Params.poolId);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"offerId":"1ab7650b904cef5ac2078a00fc2eca60","offerName":"通用网络优化型云主机"},{"offerId":"225eb7f72f424f78ae18eb41212ff9a9","offerName":"VMware云主机专属版"},{"offerId":"5691272bb306174e7b2ed84210a28fd3","offerName":"内存网络优化型云主机"},{"offerId":"762db914c221ddb9fd8ce7f456e8bcf8","offerName":"GPU型云主机"},{"offerId":"795ed2017b3f26a49e34b1cb2bd0ac3d","offerName":"通用入门型云主机"},{"offerId":"a715136ad3147ceb0bc0ba005b8e8897","offerName":"计算网络优化型云主机"},{"offerId":"aa40a113d03520947e613bae515c21ad","offerName":"超高主频型云主机"},{"offerId":"bbcb90abefd93fb38ce994be310e0854","offerName":"计算型云主机"},{"offerId":"bf81ee49b985f547233726b3fbbad3a6","offerName":"内存优化型云主机"},{"offerId":"c3b2e3a175b5ceae7f2971ad3fca1e07","offerName":"大数据型云主机"},{"offerId":"d0a7122eacdd7f997285ff20f0301519","offerName":"超大内存型云主机"},{"offerId":"fe0ca5a14938cfab007929bce91e06b9","offerName":"通用型云主机"}]}
    }

    @Test
    public void getFlavorByRegion() throws DescribeInstancesException {
        List<GetProductOfferResponseContent> list = eCloudEcsManager.getProductOffer(Params.poolId);
        GetProductOfferResponseContent product = list.stream()
                .filter(c -> c.getOfferName().equals("GPU型云主机"))
                .findFirst()
                .get();
        List<GetFlavorByRegionResponseContent> response = eCloudEcsManager.getFlavorByRegion(Params.poolId, Params.region, product.getOfferId());
        System.out.println(JSON.toJSONString(response));
        //[{"soldOut":"0","specsName":"g4t.8xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g4t.4xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g3v.xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g4t.2xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3t.2xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4a.20xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3v.14xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3t.14xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3t.4xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4t.20xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3t.8xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g4v.2xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4v.4xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3v.8xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4v.8xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4v.20xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4a.2xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4a.8xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g4a.4xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g3v.2xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"g3v.4xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"1","specsName":"g3t.xlarge.8","zoneDesc":"DGJD","zoneName":"可用区一"}]
    }

    //private String getFlavorByRegion() {
    //    GetFlavorByRegionRequest request = GetFlavorByRegionRequest.builder()
    //            .region(region)
    //            .offerId("795ed2017b3f26a49e34b1cb2bd0ac3d")
    //            .build();
    //    List<GetFlavorByRegionResponseContent> response = eCloudEcsManager.getFlavorByRegion(poolId, request);
    //    System.out.println(JSON.toJSONString(response));
    //    //{"content":[{"soldOut":"0","specsName":"t2.4xlarge.1","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.4xlarge.2","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.large.2","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.2xlarge.4","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.xlarge.4","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.2xlarge.2","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.xlarge.2","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.medium.4","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.xlarge.1","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.2xlarge.1","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.medium.2","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.4xlarge.4","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.large.1","zoneDesc":"DGJD","zoneName":"可用区一"},{"soldOut":"0","specsName":"t2.large.4","zoneDesc":"DGJD","zoneName":"可用区一"}]}
    //    GetFlavorByRegionResponseContent body = response.stream()
    //            .filter(c -> c.getSpecsName().equals("t2.medium.2"))
    //            .findFirst()
    //            .get();
    //    return body.getSpecsName();
    //}

    @Test
    public void testCreateInstance() throws CreateInstanceException, DescribeInstancesException {
        CreateInstanceRequest request = CreateInstanceRequest.builder()
                .autoRenew(null)
                .region(this.getRegion())
                //.region(Params.region)
                .billingType(BillingTypeEnum.MONTH)
                .vmType(VmTypeEnum.COMMON)
                .cpu(2)
                .ram(4)
                .specsName(null)
                .bootVolume(this.getBootVolume())
                //.dataVolume(this.getDataVolume())
                //.imageName(this.getImageName(this.getFlavorByRegion()))
                .imageName("BC-Linux 7.1 64位")
                .name(Params.name)
                .password(null)
                //.keypairName(Params.keyPair)
                .quantity(1)
                .duration(0)
                .networks(this.getNetworks())
                .build();
        CreateInstanceResponse result = eCloudEcsManager.createInstance(Params.poolId, request);
        System.out.println(JSON.toJSONString(result));
        //{"orderExtTypes":["VM","PERFORMANCEOPTIMIZATION","SSD"],"orderExts":["MOP-T-24031229764944","MOP-T-24031229764946","MOP-T-24031229764948"],"orderId":"MOP-O-24031212873200","procedureCode":"POSTPAID"}
        //{"orderExtTypes":["VM","PERFORMANCEOPTIMIZATION"],"orderExts":["MOP-T-24032638623516","MOP-T-24032638623518"],"orderId":"MOP-O-24032686119642","procedureCode":"POSTPAID"}
    }

    @Test
    public void getRegionTest() throws DescribeInstancesException {
        List<GetRegionResponseContent> response = eCloudEcsManager.getRegion(Params.poolId);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"component":"NOVA","deleted":false,"id":3,"name":"可用区1","region":"DGJD","visible":true},{"component":"NOVA","deleted":false,"id":39,"name":"可用区2","region":"N020-GD-GZNJ01","visible":true},{"component":"NOVA","deleted":false,"id":51,"name":"可用区3","region":"N020-GD-GZFH01","visible":true}]}
    }

    private String getRegion() throws DescribeInstancesException {
        List<GetRegionResponseContent> response = eCloudEcsManager.getRegion(Params.poolId);
        String result = response.get(0).getRegion();
        System.out.println("getRegion:" + result);
        return result;
    }

    private VmCreateRequestBootVolume getBootVolume() {
        return VmCreateRequestBootVolume.builder().volumeType(DiskPerformanceEnum.PERFORMANCEOPTIMIZATION).size(40).build();
    }

    private List<VmCreateRequestDataVolume> getDataVolume() {
        VmCreateRequestDataVolume request = VmCreateRequestDataVolume.builder()
                .size(10)
                .isShare(false)
                .resourceType(DiskTypeEnum.SSD)
                .build();
        return Lists.newArrayList(request);
    }

    private String getImageName(String specsName) {
        try {
            GetServerPublicImagesResponse response = eCloudImsManager.getServerPublicImages(Params.poolId, "", specsName, ChargeTypeEnum.BOTH, FeeTypeEnum.DURATION);
            System.out.println("获取镜像名称:" + JSON.toJSONString(response));
            //获取镜像名称:{"content":[{"imageId":"bd09fd1c-935d-409d-8439-5e36112ebf87","minDisk":"20","name":"BC-Linux 7.1 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"12896f96-36d9-4be3-8fd5-0f3f58bcdd7a","minDisk":"20","name":"CentOS 7.1 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"1a6e1780-23aa-498f-8a73-66a1b484be5d","minDisk":"20","name":"CentOS 7.5 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"b6e4c91a-bfd5-4817-99ce-d56998ba0eb6","minDisk":"20","name":"Ubuntu 14.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"0b9ed60d-e7ef-43b7-9326-c541bf51b0fd","minDisk":"20","name":"Ubuntu 16.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"6d9af28e-a5f8-4675-a4a4-87f40a82519a","minDisk":"20","name":"Ubuntu 18.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"be0261ab-9b1e-4990-9378-13c1e8f56fff","minDisk":"40","name":"Windows Server 2012 R2 Datacenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa738","minDisk":"40","name":"Windows Server 2016 Datacenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa739","minDisk":"20","name":"CentOS 7.4 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"a7fe02ee-86e9-4520-8742-750c2acaa740","minDisk":"20","name":"CentOS 7.2 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"ae454f7c-4898-4afc-a341-8658e172883c","minDisk":"20","name":"CentOS 7.3 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"f8d2e64d-35d7-41a5-a4e8-53a84731c502","minDisk":"20","name":"OpenSUSE 42.3 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"777023ac-a2f4-43b6-a4da-c6c7ea62a4ba","minDisk":"20","name":"CentOS 7.6 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"068e1c7d-f8b7-482f-bfbc-8263396c96c7","minDisk":"20","name":"CentOS 7.7 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"f4063971-526a-4479-995e-1f6e48b94dbe","minDisk":"40","name":"Windows Server 2019 DataCenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"beb697d3-41d9-450d-9f84-7f8b4e008c3c","minDisk":"20","name":"BC-Linux 7.6 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"6ec4f105-50bf-443a-9ce6-a476438847b3","minDisk":"20","name":"BC-Linux 7.5 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"d6b652cf-7ece-4d21-a902-4e7682a533a3","minDisk":"20","name":"BC-Linux 7.4 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"be396df1-25b4-432a-bec6-55d257ec0886","minDisk":"20","name":"CentOS 7.0 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"cac4dd55-9e3f-43b8-8d97-0c4a99788aee","minDisk":"40","name":"Windows Server 2012 R2 Standard 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"9eb0c81f-ecbf-4dd0-82d6-62be5f2aedec","minDisk":"40","name":"Windows Server 2012 R2 Standard 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"ce5a17ba-1ff9-4160-ab86-1ca7efd8009a","minDisk":"40","name":"Windows Server 2012 R2 Datacenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"8fadd2c3-e271-4746-a12d-1823bdd94f00","minDisk":"40","name":"Windows Server 2016 Datacenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"483a096b-989e-43dd-97d0-14bb7d2f24be","minDisk":"40","name":"Windows Server 2019 DataCenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"8add1236-f092-40dd-b756-f36f93d2e5dd","minDisk":"20","name":"BC-Linux 8.1 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"c82bb33a-69e0-4f10-b8e0-856502066384","minDisk":"20","name":"BC-Linux 7.7 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"de715a8b-37f8-455a-9d53-127882186833","minDisk":"20","name":"CentOS 7.8 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"be843c01-bbb7-4255-bf9e-ca88d3b9ab6a","minDisk":"20","name":"CentOS 7.9 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"76f03145-5574-43e7-87f8-bfa6da0e06c0","minDisk":"20","name":"Ubuntu 20.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"695ae969-d95a-4601-94a0-1e2cf2f145ec","minDisk":"20","name":"OpenSUSE 15.2 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"e87d1078-3a33-4db2-9ad2-fe375b22cad4","minDisk":"20","name":"Debian 10.9 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"dc2ad076-15a3-46dd-9b3b-c4eac12478d8","minDisk":"20","name":"Debian 10.7 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"02d97d1a-08b9-49bd-8da2-c82d798b360d","minDisk":"20","name":"Debian 10.2 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"e42aea00-8cbe-4606-8eec-e5bc1aa1f969","minDisk":"20","name":"openEuler 20.03 64位","osName":"openEuler","osType":"linux","status":"ACTIVE"},{"imageId":"9fc61d66-4d51-4757-93fb-ad6a38c96807","minDisk":"20","name":"BC-Linux 8.2 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"a3d6b50a-601a-40d7-8e4d-fd2a65da4166","minDisk":"20","name":"BC-Linux for Euler 21.10 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"b0d05bd0-887d-427f-a669-fbde48dd21f6","minDisk":"20","name":"BC-Linux 7.8 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"b197d26c-8b53-484b-be0e-119038d4034f","minDisk":"20","name":"Ubuntu 22.04 64位","osName":"Ubuntu","osType":"linux","status":"ACTIVE"},{"imageId":"89fb5e86-ea26-4bac-b078-213093b807c3","minDisk":"20","name":"BC-Linux 8.4 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"a07aea82-2e84-416e-b008-3690ae38e251","minDisk":"20","name":"OpenSUSE 15.3 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"5631897c-b7ae-499e-9e4a-7a27d9a9d431","minDisk":"40","name":"Windows Server 2022 DataCenter 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"9b549974-fe65-46a5-990a-81522002fe04","minDisk":"40","name":"Windows Server 2022 DataCenter 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"6f6561ac-a986-12ec-994b-5987e37a5290","minDisk":"20","name":"Debian 11.6 64位","osName":"Debian","osType":"linux","status":"ACTIVE"},{"imageId":"08507a70-79ea-bb17-ed32-1b2eed4ce7a3","minDisk":"20","name":"openEuler 22.03 64位","osName":"openEuler","osType":"linux","status":"ACTIVE"},{"imageId":"5a2cea75-d211-4481-e892-7eccf9e64478","minDisk":"20","name":"Rocky 8.5 64位","osName":"Rocky","osType":"linux","status":"ACTIVE"},{"imageId":"0569c11d-f5ee-ecc9-6e32-328c2cc56067","minDisk":"20","name":"Rocky 9.1 64位","osName":"Rocky","osType":"linux","status":"ACTIVE"},{"imageId":"3c58f5d6-424c-592a-8671-b6028f2a2503","minDisk":"20","name":"Anolis 8.6 64位","osName":"Anolis","osType":"linux","status":"ACTIVE"},{"imageId":"9d652618-58a6-04ff-9d51-400480b79909","minDisk":"20","name":"BC-Linux for Euler 22.10 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"e2f73472-97c7-124e-574c-fea377c7dbb3","minDisk":"20","name":"BC-Linux 8.6 64位","osName":"BCLinux","osType":"linux","status":"ACTIVE"},{"imageId":"2e6184fd-e3ac-1d77-86d8-28f49e231265","minDisk":"40","name":"Windows Server 2019 Standard 64位 中文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"b01de63f-cd0a-7566-f020-344d8faba7a5","minDisk":"40","name":"Windows Server 2019 Standard 64位 英文版","osName":"Windows","osType":"windows","status":"ACTIVE"},{"imageId":"337f1981-370e-babd-15c3-58392c38ea0f","minDisk":"20","name":"AlmaLinux 8.8 64位","osName":"AlmaLinux","osType":"linux","status":"ACTIVE"},{"imageId":"74293fa6-9432-73c9-a536-f0aac2d7efed","minDisk":"20","name":"CentOS Stream 8.0 64位","osName":"CentOS","osType":"linux","status":"ACTIVE"},{"imageId":"ad4b2691-ad14-50d4-ff5e-41fbec72a0b2","minDisk":"20","name":"OpenSUSE 15.5 64位","osName":"OpenSUSE","osType":"linux","status":"ACTIVE"},{"imageId":"9046c892-5df5-aab1-9a92-c6857f710b0f","minDisk":"20","name":"Anolis 8.8 64位","osName":"Anolis","osType":"linux","status":"ACTIVE"}],"total":55}
            return response.getContent().get(0).getName();
        } catch (DescribeImageException e) {
            throw new RuntimeException(e);
        }
    }

    private VmCreateRequestNetworks getNetworks() {
        return VmCreateRequestNetworks.builder().networkId(Params.networkId).build();
    }

    @Test
    public void testCreateKeyPair() throws InstanceKeyPairException {
        CreateKeypairResponse response = eCloudEcsManager.createKeypair(Params.poolId, Params.region, Params.keyPair);
        System.out.println(JSON.toJSONString(response));

    }

    @Test
    public void testGetKeyPairList() throws InstanceKeyPairException {
        GetKeyPairListResponse response = eCloudEcsManager.getKeyPairList(Params.poolId, "test", 1, 10);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"fingerPrint":"ee:51:da:7f:53:54:27:62:af:d2:81:d1:fe:e9:b3:c2","id":"63f683fb34ed456db11fcf862c91cc0a","name":"testCreateKeyPair4","region":"DGJD"},{"fingerPrint":"58:92:30:a0:1e:bf:09:87:4a:b3:a0:4c:02:78:f9:81","id":"c10561d3f88c40f9ba3feecc6178530b","name":"testCreateKeyPair3","region":"DGJD"},{"fingerPrint":"45:53:e8:66:2b:f6:ab:1e:9c:67:09:98:99:f7:34:ae","id":"8ae4d08a7c6f43009d05a98d138ce9c2","name":"testCreateKeyPair2","region":"DGJD"},{"fingerPrint":"5a:95:43:09:85:ad:89:46:b9:31:14:30:a2:64:67:06","id":"6f428da6e9814ea28de38af0e10e9a91","name":"testCreateKeyPair","region":"DGJD"}],"total":4}
    }


    @Test
    public void testStartInstance() throws StartInstanceException {
        eCloudEcsManager.startInstance(Params.poolId, Params.serverId);
    }

    @Test
    public void testStopInstance() throws StopInstanceException {
        eCloudEcsManager.stopInstance(Params.poolId, Params.serverId);
    }

    @Test
    public void testRebootInstance() throws RebootInstanceException {
        eCloudEcsManager.rebootInstance(Params.poolId, Params.serverId);
    }

    @Test
    public void testDeleteInstance() throws ReleaseInstanceException {
        eCloudEcsManager.deleteInstance(Params.poolId, Params.serverId, true, true);
    }

    @Test
    public void testGetVnc() throws DescribeVncException {
        GetVncResponse response = eCloudEcsManager.getVnc(Params.poolId, Params.serverId);
        System.out.println(JSON.toJSONString(response));
        //{"content":"https://console-jinan-1.cmecloud.cn:6080/vnc_auto.html?token=8a2acc72-c28a-45cd-bddc-e96d233ac4e3&title=bf0cd0ef-bb97-4b67-8b33-4a7cd01f6d61"}
    }

    @Test
    public void testUpdatePassword() throws ModifyEcsAttributeException {
        String password = "Timb8sGI0AVI9ZYytFvsZ8zhGaS2rraw5XN4QWBb8M/cMmU+eCj6DsVP5O1wKVFXUgp6pi0lWS9ftM9ibSCLbLMtS/AwmhT7wZfP7lC3XXWCSJQ9pfVinHMvX7HiEdBxYMshEwUQ/A5x20AkX5ugBelpo43gtABuhLmTQvcbdrY=";
        UpdatePasswordResponse response = eCloudEcsManager.updatePassword(Params.poolId, Params.serverId, password);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getOrderInfoTest() throws GetOrderInfoException {
        String orderId = "MOP-O-24040232673682";
        List<GetOrderInfoResponseContent> response = eCloudEcsManager.getOrderInfo(Params.poolId, orderId);
        System.out.println(JSON.toJSONString(response));
        //ecs创建
        //[{"bossOrderId":"10034181747","instanceId":"bf0cd0ef-bb97-4b67-8b33-4a7cd01f6d61","measureId":"111710091","status":"6","tradeId":"MOP-T-24032739205110"},{"bossOrderId":"10034181746","instanceId":"dec8309d55994fb58ce17bcf435f6445","measureId":"111810088","status":"6","tradeId":"MOP-T-24032739205112"}]
        //ecs续订
        //[{"bossOrderId":"10034181746","instanceId":"dec8309d55994fb58ce17bcf435f6445","measureId":"111810088","status":"28","tradeId":"MOP-T-24032839805106"},{"bossOrderId":"10034181747","instanceId":"bf0cd0ef-bb97-4b67-8b33-4a7cd01f6d61","measureId":"111710091","status":"28","tradeId":"MOP-T-24032839805104"}]
        //eip创建
        //[{"bossOrderId":"","instanceId":"75003d36-8105-44d3-90a4-32c3b6542862","measureId":"111715001","status":"6","tradeId":"MOP-T-24032739249518"},{"bossOrderId":"10034186555","instanceId":"75e8a845-96a4-4251-89ab-d8746b74661e","measureId":"111716420","status":"6","tradeId":"MOP-T-24032739249520"}]
        //eip退订
        //[{"bossOrderId":"10034186555","instanceId":"75e8a845-96a4-4251-89ab-d8746b74661e","measureId":"111716420","status":"14","tradeId":"MOP-T-24032739332416"},{"bossOrderId":"","instanceId":"75003d36-8105-44d3-90a4-32c3b6542862","measureId":"111715001","status":"14","tradeId":"MOP-T-24032739332418"}]
        //vpc创建
        //[{"bossOrderId":"","instanceId":"5908fe1f-1b62-4602-a90c-186d5927435b","measureId":"111791003","status":"6","tradeId":"MOP-T-24032739030872"}]
        //disk退订
        //[{"bossOrderId":"10034177967","instanceId":"99cb0355-3b8f-46d2-89c0-c9c9f1e9ce17","measureId":"111806090","status":"14","tradeId":"MOP-T-24032739156570"}]
        //disk扩容
        //[{"bossOrderId":"10034456013","instanceId":"5c067f62-042d-4235-8b62-d1d84db3e5fd","measureId":"111806090","status":"58","tradeId":"MOP-T-24040243657826"}]
    }

    @Test
    public void testSetServerAutoRenew() throws ModifyEcsAttributeException {
        eCloudEcsManager.setServerAutoRenew(Params.poolId, Params.serverId, false);
    }

    @Test
    public void testRenewInstance() throws RenewInstanceException {
        RenewInstanceResponse renewInstanceResponse = eCloudEcsManager.renewInstance(Params.poolId, Params.serverId, 1);
        System.out.println(renewInstanceResponse);
    }
}
