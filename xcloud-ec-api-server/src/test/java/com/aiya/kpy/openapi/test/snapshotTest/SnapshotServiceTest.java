package com.aiya.kpy.openapi.test.snapshotTest;

import com.SpringBootStarter;
import com.aiya.kpy.snapshot.foundation.service.SnapshotService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @date 2018/5/16 19:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SnapshotServiceTest {
	@Autowired
	private SnapshotService snapshotService;

}
