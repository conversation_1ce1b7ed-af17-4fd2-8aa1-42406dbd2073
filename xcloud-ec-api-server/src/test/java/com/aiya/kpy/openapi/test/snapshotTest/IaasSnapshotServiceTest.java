package com.aiya.kpy.openapi.test.snapshotTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSSnapShotManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.ReleaseSnapShotException;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.ResourceAlreadyReleaseException;
import com.aiya.kpy.ec.foundation.iaas.model.SnapshotModel;
import com.aiya.kpy.ec.foundation.iaas.service.IaaSSnapshotService;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSCreateSnapshotException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSDescribeSnapshotException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSReleaseSnapshotException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSRollbackSnapshotException;
import com.alibaba.fastjson.JSON;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasSnapshotServiceTest{
	@Resource(name = "iaaSSnapShotServiceImpl")
	private IaaSSnapshotService iaaSSnapshotService;
	@Autowired
	private IaaSSnapShotManager iaaSSnapShotManager;

	/**
	 * 测试创建快照
	 */
	@Test
	public void testCreateSnapshot() {
		try {
			iaaSSnapshotService.createSnapShot("feilong", "demo-1", "demo-1-a", "5f6172c0-87db-4fe9-bc7b-c3830567e22e");
		} catch (IaaSCreateSnapshotException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试释放快照
	 */
	@Test
	public void testReleaseSnapshot() {
		Assert.assertNotNull(iaaSSnapshotService);
		try {
			iaaSSnapshotService.releaseSnapshot("hflCreateSnapshot28", "demo-1","demo-1-a","84e57732-5fc6-4f29-b5ee-5436997daa77");
		} catch (IaaSReleaseSnapshotException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试释放快照
	 */
	@Test
	public void releaseSnapshot() {
		try {
			iaaSSnapShotManager.releaseSnapShot("cn-fuzhou-4", "cn-fuzhou-4-b", "ae7199d8-91de-4ff9-9eff-0f2c191d16f4");
		} catch (ReleaseSnapShotException e) {
			e.printStackTrace();
		} catch (ResourceAlreadyReleaseException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试service回滚
	 */
	@Test
	public void testRollBackSnapshot() {
//		try {
//			iaaSSnapshotService.rollBackSnapShot("snap-12138", 42L);
//		} catch (IaaSRollbackSnapshotException e) {
//			e.printStackTrace();
//		}
	}

	/**
	 * 测试manager回滚
	 * @throws ReleaseSnapShotException
	 */
	@Test
	public void testRollbackSnapShot() throws ReleaseSnapShotException {
		try {
			iaaSSnapshotService.rollBackSnapShot("aaa", "demo-2", "demo-2-a","4f7f5226-4c30-4e96-a818-9124191bccdb");
		} catch (IaaSRollbackSnapshotException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试manager回滚
	 * @throws ReleaseSnapShotException
	 */
	@Test
	public void test1()  {
//		try {
//			DescribeSnapShotInfo describeSnapShotInfo =	iaaSSnapShotManager.describeSnapShot(new String[]{"4f7f5226-4c30-4e96-a818-9124191bccdb"}, null,"demo-2", "demo-2-a");
//			System.out.println(JSON.toJSONString(describeSnapShotInfo));
//		} catch (DescribeSnapShotException e) {
//			e.printStackTrace();
//		}

		try {
			SnapshotModel snapshotModel = iaaSSnapshotService.describeIaasSnapshotInfo("demo-2", "demo-2-a","4f7f5226-4c30-4e96-a818-9124191bccdb");
			System.out.println(JSON.toJSONString(snapshotModel));
		} catch (IaaSDescribeSnapshotException e) {
			e.printStackTrace();
		}
	}
}
