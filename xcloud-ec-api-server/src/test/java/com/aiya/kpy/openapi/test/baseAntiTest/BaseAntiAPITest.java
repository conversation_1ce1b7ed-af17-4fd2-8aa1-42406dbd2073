package com.aiya.kpy.openapi.test.baseAntiTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.UserPower;
import com.aiya.kpy.ddos.api.DdosOpenAPI;
import com.aiya.kpy.ddos.api.request.DescribeBaseAntiIPListRequest;
import com.aiya.kpy.ddos.api.response.DescribeBaseAntiIPListResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;

/**
 * <AUTHOR> by huangxy
 * @date 2020/7/16
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class BaseAntiAPITest {

    @Autowired
    private DdosOpenAPI ddosAPI;

    @Test
    public void testDescribeBaseAntiIpList(){
        DescribeBaseAntiIPListRequest request = new DescribeBaseAntiIPListRequest();
        request.setRegion("demo-1");
        HashSet<Long> domainIds = Sets.newHashSet();
        domainIds.add(2L);
        domainIds.add(1L);
        request.setDomainIds(domainIds);
//        request.setOprUserId(109620L);
        request.setOprUserId(1L);
        request.setUserId(109620L);
        request.setOprUserPower(UserPower.MANAGEMENT.getCode());
        DescribeBaseAntiIPListResponse response = ddosAPI.describeBaseAntiIPList(request);
        System.out.println(JSON.toJSONString(response));
    }


}
