package com.aiya.kpy.openapi.test.listenerTest;

import cn.hutool.json.JSONUtil;
import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.model.AccountChangeNotifyMessage;
import com.aiya.kpy.ec.task.mq.AccountChangeMessageListener;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/1/15
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class AccountChangeMessageListenerTest {
    @Resource
    private AccountChangeMessageListener accountChangeMessageListener;

    @Test
    public void testDealWithResourceLock() {
        AccountChangeNotifyMessage message = JSONUtil.toBean("{\"status\":\"LOCKING\",\"users\":[{\"domainId\":1,\"id\":31078}]}", AccountChangeNotifyMessage.class);
        accountChangeMessageListener.dealWithResourceLock(message);
    }
}
