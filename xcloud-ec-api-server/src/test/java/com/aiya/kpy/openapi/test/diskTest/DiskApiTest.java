package com.aiya.kpy.openapi.test.diskTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.DateStyleEnum;
import com.aiya.kpy.common.util.DateUtil;
import com.aiya.kpy.disk.api.DiskAPI;
import com.aiya.kpy.disk.api.model.DiskMonitorDataInfo;
import com.aiya.kpy.disk.api.model.DiskSummary;
import com.aiya.kpy.disk.api.request.*;
import com.aiya.kpy.disk.api.response.*;
import com.aiya.kpy.disk.task.batch.DiskBillHandler;
import com.aiya.kpy.ec.api.enums.MonitorPeriod;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;
import java.util.List;
import java.util.Set;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class DiskApiTest {
	@Autowired
	private DiskAPI diskAPI;
	@Autowired
	private DiskBillHandler diskBillHandler;

	@Test
	public void testCreateBill(){
		try {
			diskBillHandler.execute(null);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 测试硬盘列表
	 */
	@Test
	public void testQueryDiskList(){
		DescribeDiskListRequest diskListRequest = new DescribeDiskListRequest();
		diskListRequest.setOprUserId(109667L);
		diskListRequest.setRegion("demo-1");
//		diskListRequest.setZone("demo-1-a");

		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(1L);
		diskListRequest.setDomainIds(domainIds);
		diskListRequest.setDiskUUIDs(new String[]{"disk-f3000003f39h","disk-f7000003ewz2"});
//		diskListRequest.setField("instanceId");
//		diskListRequest.setKeyword("ecs-12138");
		DescribeDiskListResponse response = diskAPI.describeDiskList(diskListRequest);
		System.out.println(JSON.toJSONString(response));
		System.out.println("结果");
	}

	/**
	 * 测试硬盘详情
	 */
	@Test
	public void testQueryDiskDetail(){
		DescribeDiskDetailRequest diskDetailRequest = new DescribeDiskDetailRequest();
		diskDetailRequest.setDiskId("disk-asd123123sd1");
		diskDetailRequest.setOprUserId(109724L);
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		diskDetailRequest.setDomainIds(domainIds);
		DescribeDiskDetailResponse response = diskAPI.describeDiskDetail(diskDetailRequest);
		System.out.println(JSON.toJSONString(response));
	}

	/**
	 * 测试硬盘监控
	 */
	@Test
	public void testQueryDiskMonitor(){
		DiskMonitorDataRequest diskMonitorDataRequest = new DiskMonitorDataRequest();
		diskMonitorDataRequest.setMonitorPeriod(MonitorPeriod.ONE_MINUTE);
		diskMonitorDataRequest.setStartTime(new Date());
		diskMonitorDataRequest.setEndTime(new Date());
		diskMonitorDataRequest.setResourceUUID("disk-ma00000023e1");
		diskMonitorDataRequest.setOprUserId(109595L);
		DiskMonitorDataResponse response = diskAPI.queryDiskMonitorData(diskMonitorDataRequest);
		List<DiskMonitorDataInfo> diskMonitorDataInfos = response.getInstanceMonitorData();
		System.out.println(diskMonitorDataInfos);
	}

	/**
	 * 测试修改属性
	 */
	@Test
	public void testModifyDiskAttribute(){
		ModifyDiskAttributeRequest request = new ModifyDiskAttributeRequest();
		request.setDescription("123456");
		request.setDiskId("disk-ma00000023e1");
		request.setDiskName("12138");
		request.setOprUserId(123456L);
		diskAPI.modifyDiskAttribute(request);
	}

	/**
	 * 测试释放硬盘
	 */
	@Test
	public void testReleaseDisk(){
		ReleaseDiskRequest request = new ReleaseDiskRequest();
		request.setDiskId("disk-asd123123sd1");
		request.setOprUserId(109724L);
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		diskAPI.releaseDisk(request);
		try {
			Thread.sleep(50000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testAttachDisk(){
		DiskAttachDiskRequest request = new DiskAttachDiskRequest();
		request.setDiskId("disk-asd123123sd1");
		request.setInstanceId("ecs-1y000025xyht");
		request.setOprUserId(109724L);
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		diskAPI.attachDisk(request);
		try {
			Thread.sleep(50000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	@Test
	public void testDetachDisk(){
		DiskDetachDiskRequest request = new DiskDetachDiskRequest();
		request.setDiskId("disk-asd123123sd1");
		request.setInstanceId("ecs-1y000025xyht");
		request.setOprUserId(109724L);
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		diskAPI.detachDisk(request);
		try {
			Thread.sleep(50000);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

}
