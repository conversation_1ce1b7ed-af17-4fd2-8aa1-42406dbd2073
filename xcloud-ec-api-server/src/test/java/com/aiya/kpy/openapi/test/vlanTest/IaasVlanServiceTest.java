package com.aiya.kpy.openapi.test.vlanTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.api.enums.NetWorkMode;
import com.aiya.kpy.ec.foundation.entity.KpyRegionEntity;
import com.aiya.kpy.ec.foundation.iaas.model.VlanModel;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSCreateVlanException;
import com.aiya.kpy.ec.foundation.iaas.service.exception.IaaSReleaseVlanException;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSVlanServiceImpl;
import com.aiya.kpy.ec.foundation.service.RegionZoneService;
import com.alibaba.dubbo.common.utils.NetUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.aspectj.lang.annotation.AfterReturning;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasVlanServiceTest {
    @Autowired
    private IaaSVlanServiceImpl iaaSVlanService;
    @Autowired
    private RegionZoneService regionZoneService;

    /**
     * 创建Vlan
     */
    @Test
    public void testCreateVlan() throws ExecutionException, InterruptedException {
        CompletableFuture<VlanModel> vlanModelCompletableFuture1 = CompletableFuture.supplyAsync(() -> {
            try {
                return iaaSVlanService.createVlan("vnet-" + System.currentTimeMillis(), "cn-dev-2", "cn-dev-2-a");
            } catch (IaaSCreateVlanException e) {
                throw new RuntimeException(e);
            }
        });
        CompletableFuture<VlanModel> vlanModelCompletableFuture2 = CompletableFuture.supplyAsync(() -> {
            try {
                return iaaSVlanService.createVlan("vnet-" + System.currentTimeMillis(), "cn-dev-2", "cn-dev-2-a");
            } catch (IaaSCreateVlanException e) {
                throw new RuntimeException(e);
            }
        });
        CompletableFuture.allOf(vlanModelCompletableFuture1, vlanModelCompletableFuture2);
    }

    /**
     * 释放Vlan
     */
    @Test
    public void testReleaseVlan() {
        try {
            iaaSVlanService.releaseVlan("vnet-bl00000011z1", "demo-1", "demo-1-a", "977a1ccc-05c7-4a29-b277-a3123e4566aa");
        } catch (IaaSReleaseVlanException e) {
            System.out.println(e.getMessage());
        }
    }

    /**
     * 检查底层VLAN
     */
    @Test
    public void testCheckAvailableVswitch() {
        iaaSVlanService.checkAvailableVswitch("vnet-bl00000011z1", "cn-fuzhou-0", "cn-fuzhou-0-a");
    }

    @Test
    public void testSelectAllRegion() {
        List<KpyRegionEntity> regionEntityList = regionZoneService.selectAvailableRegionListByNetWorkModes(Lists.newArrayList(NetWorkMode.VLAN_MODE, NetWorkMode.HYBRID_MODE));
        for (KpyRegionEntity kpyRegionEntity : regionEntityList) {
            System.out.println("kpyRegionEntity.getRegionCode() = " + kpyRegionEntity.getRegionCode());
        }

    }

}
