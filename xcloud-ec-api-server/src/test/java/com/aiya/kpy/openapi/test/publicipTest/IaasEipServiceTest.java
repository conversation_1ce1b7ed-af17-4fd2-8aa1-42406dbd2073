package com.aiya.kpy.openapi.test.publicipTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.entity.influxdb.IaaSEipMonitorInfluxdbEntity60s;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSEipManager;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeEipInfo;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeNicDetail;
import com.aiya.kpy.ec.foundation.iaas.model.PublicIPParams;
import com.aiya.kpy.ec.foundation.iaas.service.exception.*;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSEipServiceImpl;
import com.aiya.kpy.ec.foundation.utils.DateTimeUtils;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasEipServiceTest {
    @Autowired
    private IaaSEipServiceImpl iaaSEipService;
    @Autowired
    private IaaSEipManager iaaSEipManager;

    @Test
    public void testQueryEipInfo() {
        try {
            DescribeEipInfo iaaSEipInfo = iaaSEipService.getIaaSEipInfo("cn-fuzhou-8", null, "b3490c69-9691-4397-9aa9-428284fc860c");
            System.out.println(JSON.toJSONString(iaaSEipInfo));
        } catch (IaaSDescribeEipException.DescribeIaasEipException e) {
            e.printStackTrace();
        } catch (IaaSDescribeEipException.EipNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testTransferMonitorData() {
        final Date nowTime = new Date();
        final Date firstDayOfMonth = DateTimeUtils.getFirstDayOfMonth(nowTime);
        try {
            final List<IaaSEipMonitorInfluxdbEntity60s> monitorDataList = iaaSEipService.queryEipMonitorData("c215c9da-2077-4ec0-9ded-375d4a662a13", 60,
                    firstDayOfMonth.getTime(), nowTime.getTime());
            if (CollectionUtils.isNotEmpty(monitorDataList)) {
                List<IaaSEipMonitorInfluxdbEntity60s> resultDataList = Lists.newArrayList();
//                for (int i = 0; i < monitorDataList.size(); i++){
//                    IaaSEipMonitorInfluxdbEntity60s data = monitorDataList.get(i);
//                    data.setIaasResourceUUID("c215c9da-2077-4ec0-9ded-375d4a662a14");
//                    resultDataList.add(data);
//                    iaaSEipService.batchInserMonitorData(resultDataList);
//                    System.out.println("i:" + i);
//                }
                for (IaaSEipMonitorInfluxdbEntity60s data : monitorDataList) {
                    IaaSEipMonitorInfluxdbEntity60s item = new IaaSEipMonitorInfluxdbEntity60s();
                    BeanUtils.copyProperties(data, item);
                    item.setResourceUUID("eip-7i00002g1bxp");
                    item.setIaasResourceUUID("c215c9da-2077-4ec0-9ded-375d4a66288");
                    resultDataList.add(item);
                }
//                int size = resultDataList.size();
//                for (int i = 0; i < size; i = i + 1000){
//                    int endIndex = i + 1000;
//                    if(endIndex >= size){
//                        endIndex = size - 1;
//                    }
//                    System.out.println("startIndex:" + i + ", endIndex:" + endIndex);
//                    System.out.println(resultDataList.subList(i, endIndex).size());
//                    iaaSEipService.batchInserMonitorData(resultDataList.subList(i, endIndex));
//                }
                iaaSEipService.batchInserMonitorData(resultDataList);
            }
        } catch (IaaSQueryEipMonitorDataException e) {
            e.printStackTrace();
        }


    }


    @Test
    public void testAllocateEip() {
        PublicIPParams publicIPParams = new PublicIPParams();
        publicIPParams.setResourceUUID("eip-1u00000ss01");
        publicIPParams.setBandwidth(1);
        publicIPParams.setBandwidthIn(1);
        try {
            iaaSEipService.allocateSecondaryEip("eip-1u00000ss01", "demo-1", null, publicIPParams, "2253e9cf-6d82-4744-96c8-************", null);
        } catch (IaaSCreateEipException e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void testReleaseEip() {
        try {
            iaaSEipService.releaseEip("eip-yb000000bz01", "cn-fuzhou-8", "cn-fuzhou-8-a", "91a5fc5b-cca8-4291-8066-b3dbd6f42bba");
        } catch (IaaSReleaseEipException e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void testModifyEipBandwidth() {
        try {
            iaaSEipService.modifyEipBandwidth("eip-1u00000ss01", "", "", "", BigDecimal.TEN, BigDecimal.TEN);
        } catch (IaaSModifyEipBandWidthException e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    public void testGetIaaSNicInfo() {
        //String nicId = "32bbd90d-54de-4dfb-bef0-1dfd8927b69d";
        String nicId = "a723ab31-d542-4dfd-958c-3e1639fd2448";
        try {
            DescribeNicDetail iaaSNicInfo = iaaSEipService.getIaasNicDetail("cn-dev-2", "cn-dev-2-a", nicId);
            System.out.println(JSON.toJSONString(iaaSNicInfo));
            //{"nics":{"Nic":[{"bandwidth":1024,"cidrBlock":"***********/24","creationTime":"2024-10-11 14:16:15","deviceName":"vmeth0-jdliv7o5","instanceId":"9d03fc28-9f76-49a0-8493-9bad06edc3df","ipAddress":"*************","isMain":false,"nicId":"32bbd90d-54de-4dfb-bef0-1dfd8927b69d","nicMac":"52:54:08:9e:d4:34","source":"vmeth0-jdliv7o5","vlanId":2206}]},"pageNumber":1,"pageSize":1,"totalCount":1}
            //{"nics":{"Nic":[{"bandwidth":20,"cidrBlock":"*******/24","creationTime":"2024-10-11 14:16:24","deviceName":"vmeth1-jdliv7o5","instanceId":"9d03fc28-9f76-49a0-8493-9bad06edc3df","ipAddress":"*********","isMain":true,"nicId":"a723ab31-d542-4dfd-958c-3e1639fd2448","nicMac":"52:54:8d:ac:df:6f","source":"vmeth1-jdliv7o5","vlanId":3500}]},"pageNumber":1,"pageSize":1,"totalCount":1}
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testModifyNicAttribute() {
        try {
            iaaSEipManager.modifyNicAttribute("cn-dev-2", "cn-dev-2-a", "a723ab31-d542-4dfd-958c-3e1639fd2448", "default", "default", null, null);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
