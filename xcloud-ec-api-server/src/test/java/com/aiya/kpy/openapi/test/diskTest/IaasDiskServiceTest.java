package com.aiya.kpy.openapi.test.diskTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSDiskManager;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSEcsManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.DescribeDiskException;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.ResourceAlreadyReleaseException;
import com.aiya.kpy.ec.foundation.iaas.manager.model.DescribeDiskInfo;
import com.aiya.kpy.ec.foundation.iaas.model.DiskModel;
import com.aiya.kpy.ec.foundation.iaas.service.exception.*;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSDiskServiceImpl;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasDiskServiceTest {

    @Autowired
    private IaaSEcsManager iaaSEcsManager;

    @Autowired
    private IaaSDiskManager iaaSDiskManager;

    @Autowired
    private IaaSDiskServiceImpl iaaSDiskService;

    /**
     * 测试创建云盘
     */
    @Test
    public void testCreateCloudDisk() {
//        try {
//            iaaSDiskService.createCloudDiskByImage("disk-12138","",50, DiskCategory.CLOUD);
//        } catch (IaaSCreateDiskException e) {
//            e.printStackTrace();
//        }

    }

    /**
     * 测试创建本地盘
     *
     * @throws IaaSCreateDiskException
     */
    @Test
    public void testCreateLocalDisk() throws IaaSCreateDiskException {

    }

    /**
     * 测试释放硬盘
     *
     * @throws IaaSReleaseDiskException
     */
    @Test
    public void testReleaseDisk() throws IaaSReleaseDiskException {
        Assert.assertNotNull(iaaSDiskService);
//		iaaSDiskService.releaseDisk("ecs-create-disk-010", 123L);
    }

    /**
     * 测试扩容硬盘
     *
     * @throws IaaSResizeDiskException
     */
    @Test
    public void testResizeDisk() throws IaaSResizeDiskException {
        Assert.assertNotNull(iaaSDiskService);
//		iaaSDiskService.resizeDisk("ecs-create-disk-010", 139L, 30);
    }

    /**
     * 测试磁盘和云主机的解绑
     */
    @Test
    public void testUnattachLocalDisk() throws IaaSEcsUnAttachDiskException {
//		iaaSEcsService.unAttachDisk("123", 156L, 225L);
    }

    /**
     * 测试绑定本地盘
     */
    @Test
    public void testAttachLocalDisk() {
        try {
            iaaSEcsManager.attachDisk("cn-fuzhou-4", "cn-fuzhou-4-a", "4655e08a-3506-4605-967c-3e9a3409d5e4", "b0ae64bf-986a-4725-a0c3-71f697910503");
        } catch (com.aiya.kpy.ec.foundation.iaas.manager.exception.AttachDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试查询硬盘
     */
    @Test
    public void testDescribeDisk() {
//		String[] disk = new String[]{"909388d1-dbeb-47a3-ba1d-1ac376267dea"};
//		try {
//			iaaSDiskManager.describeDisks("demo-2", "demo-2-a", disk);
//		} catch (DescribeDiskException e) {
//			e.printStackTrace();
//		}
        try {
            DiskModel diskModel = iaaSDiskService.describeIaasDiskInfo("demo-2", "demo-2-a", "960e8d5a-026f-4f50-b28c-8381a7890939");
        } catch (IaaSDescribeDiskException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试删除(释放磁盘)
     */
    @Test
    public void testRelaseDiskByManager() {
        try {
            iaaSDiskManager.releaseDisk("cn-dev-2", "cn-dev-2-a", "a09f7ba4-521f-4dc8-ae67-42fca9a0f919", false);
            DescribeDiskInfo describeDiskInfo = iaaSDiskManager.describeDisks("cn-dev-2", "cn-dev-2-a", new String[]{"a09f7ba4-521f-4dc8-ae67-42fca9a0f919"});
            System.out.println(describeDiskInfo);
        } catch (com.aiya.kpy.ec.foundation.iaas.manager.exception.ReleaseDiskException e) {
            e.printStackTrace();
        } catch (ResourceAlreadyReleaseException e) {
            e.printStackTrace();
        } catch (DescribeDiskException e) {
            throw new RuntimeException(e);
        }
    }
}
