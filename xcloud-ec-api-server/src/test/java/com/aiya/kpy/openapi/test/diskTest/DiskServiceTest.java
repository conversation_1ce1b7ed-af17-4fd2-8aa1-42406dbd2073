package com.aiya.kpy.openapi.test.diskTest;

import com.SpringBootStarter;
import com.aiya.kpy.api.uic.UicApiWrapper;
import com.aiya.kpy.common.enums.*;
import com.aiya.kpy.common.util.ResourcePeriodUtil;
import com.aiya.kpy.disk.foundation.entity.DiskEntity;
import com.aiya.kpy.disk.foundation.service.DiskService;
import com.aiya.kpy.disk.foundation.service.exception.ReleaseDiskException;
import com.aiya.kpy.ec.api.enums.DiskPendingStatus;
import com.aiya.kpy.ec.api.enums.DiskStatus;
import com.aiya.kpy.ec.foundation.entity.KpyRegionEntity;
import com.aiya.kpy.ec.foundation.entity.KpyZoneEntity;
import com.aiya.kpy.ec.foundation.enums.DiskAttatchStatus;
import com.aiya.kpy.ec.foundation.model.RegionZoneInfo;
import com.aiya.kpy.ec.foundation.service.RegionZoneService;
import com.aiya.kpy.ecs.foundation.entity.EcsInstanceEntity;
import com.aiya.kpy.ecs.foundation.model.TimeParam;
import com.aiya.kpy.ecs.foundation.service.EcsService;
import com.aiya.kpy.uic.api.model.UserInfo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Date;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class DiskServiceTest {
    @Autowired
    private DiskService diskService;
    @Autowired
    private EcsService ecsService;
    @Autowired
    private RegionZoneService regionZoneService;
    @Autowired
    private UicApiWrapper uicApiWrapper;

    /**
     * 测试创建硬盘
     */
    @Test
    public void testCreateDisk() {
        final RegionZoneInfo regionZoneInfo = regionZoneService.getRegionZoneInfo("demo-2", "demo-2-a");
        final KpyRegionEntity regionEntity = regionZoneInfo.getRegionEntity();
        final KpyZoneEntity zoneEntity = regionZoneInfo.getKpyZoneEntity();
        Date expireTime = ResourcePeriodUtil.getExpireTime(BillingCycleEnum.MONTH, 1);
        // 查询用户信息
        final UserInfo userInfo = uicApiWrapper.queryUicUserInfoByUserId(109724L);
        DiskEntity ecsDisk = new DiskEntity();
        ecsDisk.setChargeType(InstanceChargeType.PRE_PAID);
        ecsDisk.setResourceUUID("disk-asd123123sd1");
        ecsDisk.setCreateTime(new Date());
        ecsDisk.setRegionId(regionEntity.getId());
        ecsDisk.setZoneId(zoneEntity.getId());
        ecsDisk.setDiskType(DiskType.DATA);
        ecsDisk.setDiskStatus(DiskStatus.PENDING);
        ecsDisk.setDiskSize(50);
        ecsDisk.setDiskPendingStatus(DiskPendingStatus.CREATING);
        ecsDisk.setDiskCategory(DiskCategory.LOCAL_SSD);
        ecsDisk.setAttatchStatus(DiskAttatchStatus.FALSE);
        ecsDisk.setDetatchable(true);
        ecsDisk.setExpireDate(expireTime);
        ecsDisk.setAutoRenewStatus(AutoRenewStatus.NONE);
        //自动续费的月份没有传递进来默认为1
        ecsDisk.setDetatchable(true);
        ecsDisk.setDiskName("test");
        ecsDisk.setSourceChannelType(SourceChannelType.INNER_API);
        ecsDisk.setResourceType(ResourceTypeGroup.DISK.getResourceType(regionZoneInfo.getRegionEntity().getResourceProvider()));

        TimeParam timeParam = new TimeParam();
        timeParam.setPeriod(1);
        timeParam.setBillingperiod(BillingCycleEnum.MONTH);
        timeParam.setExpireTime(expireTime);

        diskService.createDisk(ResourceType.OWN_DISK, regionEntity, zoneEntity, userInfo, ecsDisk, null, null,
                1427L, "test123s32", SourceChannelType.INNER_API, timeParam);
        try {
            Thread.sleep(150000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试激活硬盘
     */
    @Test
    public void testActiveDisk() {
        diskService.activateDisk("test-create-snapshot");
    }

    /**
     * 测试释放硬盘
     */
    @Test
    public void testReleaseDisk() {
        try {
            diskService.releaseDisk(1897L, true);
            Thread.sleep(50000);
        } catch (ReleaseDiskException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试回收硬盘
     */
    @Test
    public void testRecycleDisk() {
        final DiskEntity diskEntity = diskService.queryDiskByPlatformUUID("disk-asd123123sd1");
        diskService.addDiskToRecycle(diskEntity, null, ProdInfoChangeMessageType.RECYCLE, null);
        try {
            Thread.sleep(80000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试续费硬盘
     */
    @Test
    public void testRenewDisk() {
        final DiskEntity diskEntity = diskService.queryDiskByPlatformUUID("disk-asd123123sd1");
        TimeParam timeParam = new TimeParam();
        timeParam.setTryOut(false);
        timeParam.setPeriod(1);
        timeParam.setBillingperiod(BillingCycleEnum.MONTH);
        EcsInstanceEntity ecsInstanceEntity = ecsService.queryEcsEntityByResourceUUID("ecs-1y000025xyht");
        diskService.renewDisk(diskEntity, timeParam, AutoRenewStatus.NONE, "123", ecsInstanceEntity);
        try {
            Thread.sleep(80000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
