package com.aiya.kpy.openapi.test.imageTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.ec.api.enums.ImageQueryStatus;
import com.aiya.kpy.ec.api.enums.ImageStatus;
import com.aiya.kpy.ec.foundation.enums.ImageType;
import com.aiya.kpy.image.api.ImageAPI;
import com.aiya.kpy.image.api.request.*;
import com.aiya.kpy.image.api.response.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Set;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ImageApiTest {
	@Autowired
	private ImageAPI imageAPI;

	/**
	 * 测试镜像列表接口
	 */
	@Test
	public void testDescribeImages(){
		DescribeImagesRequest request = new DescribeImagesRequest();
//		request.setRegion("demo-2");
		request.setImageType(ImageType.SELF.getCode());
//		request.setImageStatus(ImageQueryStatus.NORMAL.getCode());
		request.setOprUserId(109724L);
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		DescribeImagesResponse response = imageAPI.describeImages(request);
		System.out.println(JSON.toJSONString(response));
	}

	@Test
	public void testCreateCustomImageByInstance() {
		CreateCusImageByInstanceRequest request = new CreateCusImageByInstanceRequest();
		request.setOprUserId(109724L);
		request.setRegion("demo-2");
		request.setInstanceUUID("ecs-7y00002blb9f");
		request.setImageName("Create Image By Instance");
		request.setDescription("接口测试");
		request.setInstanceResourceType(ResourceType.FORTNODE_ECS.getCode());
		request.setZone("demo-2-a");
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		CreateCusImageByInstanceResponse response = imageAPI.createCustomImageByInstance(request);
		try {
			Thread.sleep(90000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		System.out.println(JSON.toJSONString(response.getAsyncTaskUUID()));
	}

	@Test
	public void testCreateCustomImageBySystemDisk() {
		CreateCusImageBySystemDiskRequest request = new CreateCusImageBySystemDiskRequest();
		request.setOprUserId(109724L);
		request.setRegion("demo-2");
		request.setDiskUUID("disk-sm00002blb3s");
		request.setImageName("Create Image By SystemDisk");
		request.setDescription("接口测试");
		request.setZone("demo-2-a");
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		CreateCusImageBySystemDiskResponse response = imageAPI.createCusImageBySystemDisk(request);
		try {
			Thread.sleep(90000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		System.out.println(JSON.toJSONString(response.getAsyncTaskUUID()));
	}

	@Test
	public void testCreateCustomImageBySnapshot() {
		CreateCusImageBySnapshotRequest request = new CreateCusImageBySnapshotRequest();
		request.setOprUserId(109724L);
		request.setRegion("demo-2");
		request.setSnapshotUUID("snp-em00002bllt3");
		request.setImageName("Create Image By Snapshot");
		request.setDescription("接口测试");
		request.setZone("demo-2-a");
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		CreateCusImageBySnapshotResponse response = imageAPI.createCustomImageBySnapshot(request);
		try {
			Thread.sleep(90000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		System.out.println(JSON.toJSONString(response.getAsyncTaskUUID()));
	}

	@Test
	public void testDeleteCustomImage() {
		DeleteCustomInstanceImageRequest request = new DeleteCustomInstanceImageRequest();
		request.setOprUserId(109724L);
		request.setImageUUID("img-hs00002bb7pb");
		Set<Long> domainIds = Sets.newHashSet();
		domainIds.add(145L);
		request.setDomainIds(domainIds);
		DeleteCustomInstanceImageResponse response = imageAPI.deleteCustomImage(request);
		try {
			Thread.sleep(40000L);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		System.out.println(JSON.toJSONString(response.getAsyncTaskUUID()));
	}


}
