package com.aiya.kpy.openapi.test.imageTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSImageManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.DescribeImageException;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaasImageServiceImpl;
import com.aiya.kpy.res.sdk.model.Image;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasImageServiceTest {
    @Autowired
    private IaasImageServiceImpl iaaSImageService;
    @Autowired
    private IaaSImageManager iaaSImageManager;

    /**
     * 测试创建镜像
     */
    @Test
    public void testCreateImage() {
//		Assert.assertNotNull(iaaSImageService);
//		try {
//			iaaSImageService.createImage("img-12138",123L,"cn-fuzhou-4","cn-fuzhou-4-b","oldDriver");
//		} catch (IaaSCreateImageException e) {
//			e.printStackTrace();
//		}
    }

    /**
     * 测试描述镜像
     */
    @Test
    public void describeImage() {
        try {
            Image image = iaaSImageManager.describeImage("cc14a787-dd28-47e3-9a94-005a3fcd6731", "demo-1", "demo-1-a");
            System.out.println(JSON.toJSONString(image));
        } catch (DescribeImageException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试释放镜像
     */
    @Test
    public void releaseImage() {
//		Assert.assertNotNull(iaaSImageService);
//		try {
//			iaaSImageService.releaseImage("img-12138", 173L);
//		} catch (IaaSReleaseImageException e) {
//			e.printStackTrace();
//		}
    }

}
