package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudDiskManager;
import com.aiya.kpy.ec.foundation.ecloud.manager.param.CreateECloudDiskParam;
import com.aiya.kpy.ec.foundation.ecloud.service.impl.ECloudDiskServiceImpl;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.*;
import com.aiya.kpy.ec.foundation.iaas.model.DiskModel;
import com.aiya.kpy.res.ecloud.enums.ChargePeriodEnum;
import com.aiya.kpy.res.ecloud.enums.ProductTypeEnum;
import com.aiya.kpy.res.ecloud.sdk.request.GetDiskListRequest;
import com.aiya.kpy.res.ecloud.sdk.response.*;
import com.aiya.platform.foundation.enviroment.Enviroment;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/11
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudDiskManagerTest {
    @Resource
    private ECloudDiskManager eCloudDiskManager;
    @Resource
    private ECloudDiskServiceImpl eCloudDiskService;

    @Test
    public void getDiskConfigListTest() throws DescribeDiskException {
        List<GetDiskConfigListResponseContent> response = eCloudDiskManager.getDiskConfigList(Params.poolId);
        System.out.println(JSON.toJSONString(response));
        //[{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_cache","customBack":true,"encryption":false,"iscsi":false,"opType":"SSDEBS","priority":1,"region":"DGJD","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_cache","customBack":true,"encryption":false,"iscsi":false,"opType":"SSDEBS","priority":1,"region":"N020-GD-GZNJ01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_cache","customBack":true,"encryption":false,"iscsi":false,"opType":"SSDEBS","priority":1,"region":"N020-GD-GZFH01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_ssd","customBack":true,"encryption":false,"iscsi":false,"opType":"SSD","priority":1,"region":"DGJD","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_ssd","customBack":true,"encryption":false,"iscsi":false,"opType":"SSD","priority":1,"region":"N020-GD-GZNJ01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_ssd","customBack":true,"encryption":false,"iscsi":false,"opType":"SSD","priority":1,"region":"N020-GD-GZFH01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_data","customBack":true,"encryption":false,"iscsi":false,"opType":"CAPEBS","priority":1,"region":"DGJD","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_data","customBack":true,"encryption":false,"iscsi":false,"opType":"CAPEBS","priority":1,"region":"N020-GD-GZNJ01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_data","customBack":true,"encryption":false,"iscsi":false,"opType":"CAPEBS","priority":1,"region":"N020-GD-GZFH01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["IRONIC"],"backupType":"NONE","cinderType":"ebs_ceph_ssd_ironic","customBack":false,"encryption":false,"iscsi":true,"opType":"SSD","priority":2,"region":"DGJD","snapshotType":"NONE","vaz":"none"},{"attachServerTypes":["IRONIC"],"backupType":"NONE","cinderType":"ebs_ceph_ssd_ironic","customBack":true,"encryption":false,"iscsi":true,"opType":"SSD","priority":2,"region":"N020-GD-GZNJ01","snapshotType":"NONE","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_cache_arm","customBack":true,"encryption":false,"iscsi":false,"opType":"SSDEBSYC","priority":1,"region":"N020-GD-GZFH01","snapshotType":"SNAPSHOT","vaz":"none"},{"attachServerTypes":["VM","DECLOUD_SERVER"],"backupType":"BACKUP","cinderType":"ebs_ceph_ssd_arm","customBack":true,"encryption":false,"iscsi":false,"opType":"SSDYC","priority":1,"region":"N020-GD-GZFH01","snapshotType":"SNAPSHOT","vaz":"none"}]
    }

    @Test
    public void createDiskTest() throws CreateDiskException {
        CreateECloudDiskParam request = CreateECloudDiskParam.builder()
                .cinderType("ebs_ceph_ssd")
                .name("testopenapi")
                .size(40)
                .quantity(1)
                .share(false)
                .productType(ProductTypeEnum.NORMAL)
//                .region(Params.region)
                .periodType(ChargePeriodEnum.HOUR)
                .build();
        CreateDiskResponse response = eCloudDiskManager.createDisk(request);
        System.out.println(JSON.toJSONString(response));
        //{"orderExtId":"MOP-T-24032537943470","orderId":"MOP-O-24032580595854","resourceId":"8c3a549e-536c-40a2-9801-ea83f9706af2","resourceType":"VOLUME"}
    }

    @Test
    public void getDiskListTest() throws DescribeDiskException {
        GetDiskListRequest request = GetDiskListRequest.builder()
                .volumeId(Params.volumeId)
                .build();
        GetDiskListResponse response = eCloudDiskManager.getDiskList(Params.poolId, Params.volumeId, 1, 10);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getDiskDetailTest() throws DescribeDiskException {
        GetDiskDetailResponse response = eCloudDiskManager.getDiskDetail(Params.poolId, Params.volumeId);
        System.out.println(JSON.toJSONString(response));
        //{"attachServerTypes":["VM","EBM","DECLOUD_SERVER"],"createSource":"EMOP","createdTime":"2024-04-01 15:50:41","encryption":false,"id":"5c067f62-042d-4235-8b62-d1d84db3e5fd","isDelete":false,"isShare":false,"iscsi":false,"measure":"hour","name":"testopenapi","operationFlag":"NONE","productType":"NORMAL","recycle":false,"region":"N0531-SD-JNSC01","serverIds":[],"size":40,"status":"AVAILABLE","tags":[],"type":"SSD","userId":"CIDC-U-5c1b7c2d213f443db148d7a7e5ca1699","userName":"RFY5912415207","vaz":"none","volumeType":"ebs_ceph_ssd"}
        //{"attachServerTypes":["VM","EBM","DECLOUD_SERVER"],"createSource":"EMOP","createdTime":"2024-04-01 15:50:41","encryption":false,"id":"5c067f62-042d-4235-8b62-d1d84db3e5fd","isDelete":false,"isShare":false,"iscsi":false,"measure":"hour","name":"testopenapi","operationFlag":"NONE","productType":"NORMAL","recycle":false,"region":"N0531-SD-JNSC01","serverIds":["bf0cd0ef-bb97-4b67-8b33-4a7cd01f6d61"],"size":40,"tags":[],"type":"SSD","userId":"CIDC-U-5c1b7c2d213f443db148d7a7e5ca1699","userName":"RFY5912415207","vaz":"none","volumeType":"ebs_ceph_ssd"}
    }

    @Test
    public void mountDiskTest() throws AttachDiskException {
        MountDiskResponse response = eCloudDiskManager.mountDisk(Params.poolId, Params.volumeId, Params.serverId);
        System.out.println(JSON.toJSONString(response));
        //null
    }

    @Test
    public void unmountDiskTest() throws UnattachDiskException {
        UnmountDiskResponse response = eCloudDiskManager.unmountDisk(Params.poolId, Params.volumeId, Params.serverId);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void changeDiskTest() throws ReSizeDiskException {
        ChangeDiskResponse response = eCloudDiskManager.changeDisk(Params.poolId, Params.volumeId, 80);
        System.out.println(JSON.toJSONString(response));
        //{"orderId":"MOP-O-24040232673682","procedureCode":"POSTPAID","productList":[{"orderExtId":"MOP-T-24040243657826"}]}
    }

    @Test
    public void deleteDiskTest() throws ReleaseDiskException {
        DeleteDiskResponse response = eCloudDiskManager.deleteDisk(Params.poolId, Params.volumeId);
        System.out.println(JSON.toJSONString(response));
        //{"orderExtId":"MOP-T-24032739156570","orderId":"MOP-O-24032790563562"}
    }

    @Test
    public void describeIaasDiskInfoTest() throws Exception {
        DiskModel model = eCloudDiskService.describeIaasDiskInfo("cn-guangzhou-3", null, "e4b9e5c4-edd4-4f51-832d-5a798f9fa43c");
        System.out.println(JSON.toJSONString(model));
    }

    @Test
    public void test() {
        String code = Enviroment.instance().getEnviromentType().getCode();
        System.out.println(code);
    }
}
