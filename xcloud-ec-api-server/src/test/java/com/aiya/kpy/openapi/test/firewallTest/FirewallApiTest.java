package com.aiya.kpy.openapi.test.firewallTest;

import com.SpringBootStarter;
import com.aiya.kpy.firewall.api.FirewallAPI;
import com.aiya.kpy.firewall.api.enums.AuthorizType;
import com.aiya.kpy.firewall.api.enums.RulePosition;
import com.aiya.kpy.firewall.api.model.CreateRuleParams;
import com.aiya.kpy.firewall.api.request.*;
import com.aiya.kpy.firewall.api.response.*;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2021/10/27
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class FirewallApiTest {
    private static String regionCode = "demo-2";
    private static Long userId = 109724L;
    private static Long domainId = 145L;

    @Autowired
    private FirewallAPI firewallAPI;

    /**
     * 查询防火墙列表
     */
    @Test
    public void testDscribeFirewallList() {
        DescribeFirewallListRequest request = new DescribeFirewallListRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        request.setRegionCode(regionCode);
        DescribeFirewallListResponse response = firewallAPI.dscribeFirewallList(request);
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 查询防火墙详情
     */
    @Test
    public void testDscribeFirewallDetai() {
        DescribeFirewallDetailRequest request = new DescribeFirewallDetailRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        request.setFirewallUUID("fw-eg000027bwif");

        DescribeFirewallDetailResponse response = firewallAPI.dscribeFirewallDetail(request);
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 查询防火墙关联主机列表
     */
    @Test
    public void testDescribeFirewallEcsList() {
        DescribeFirewallEcsListRequest request = new DescribeFirewallEcsListRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallUUID("fw-88000027evta");
        request.setField("instanceId");
        request.setKeyword("00");
        request.setPageNumber(1);
        request.setPageSize(10);

        DescribeFirewallEcsListResponse response = firewallAPI.dscribeFirewallEcsList(request);
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 查询防火墙规则列表
     */
    @Test
    public void testDescribeFirewallRuleList() {
        DescribeFirewallRuleListRequest request = new DescribeFirewallRuleListRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallUUID("fw-88000027evta");
        request.setField("portRange");
        request.setKeyword("20/443");
        request.setPageNumber(1);
        request.setPageSize(50);

        DescribeFirewallRuleListResponse response = firewallAPI.dscribeFirewallRuleList(request);
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 创建防火墙
     */
    @Test
    public void testCreateFirewall() {
        CreateFirewallRequest request = new CreateFirewallRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setRegionCode(regionCode);
        request.setFirewallName("create by api");
        request.setClientToken("1111ssss");
        List<CreateRuleParams> ruleParamsList = Lists.newArrayList();
        ruleParamsList.add(setRuleParams("out", "ALL", AuthorizType.CIDR, "0.0.0.0/0", "accept", null, "default rule"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "80/80", "default rule"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "443/443", "default rule"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "22/22", "default rule"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "3389/3389", "default rule"));
        ruleParamsList.add(setRuleParams("in", "ICMP", AuthorizType.CIDR, "0.0.0.0/0", "accept", null, "default rule"));
        request.setCreateRuleParamsList(ruleParamsList);
        CreateFirewallResponse response = firewallAPI.createFirewall(request);
        try {
            Thread.sleep(60000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 删除防火墙
     */
    @Test
    public void testDeleteFirewall() {
        DeleteFirewallRequest request = new DeleteFirewallRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        request.setFirewallUUIDList(Arrays.asList("fw-eg000027bwif"));

        firewallAPI.deleteFirewall(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 绑定防火墙
     */
    @Test
    public void testAssociateFirewall() {
        AssociateFirewallRequest request = new AssociateFirewallRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallUUID("fw-88000027evta");
        request.setEcsUUIDList(Arrays.asList("ecs-vp000022oj4b"));

        firewallAPI.associateFirewall(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 解绑防火墙
     */
    @Test
    public void testUnassociateFirewall() {
        UnassociateFirewallRequest request = new UnassociateFirewallRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallUUID("fw-88000027evta");
        request.setEcsUUIDList(Arrays.asList("ecs-vp000022oj4b"));

        firewallAPI.unassociateFirewall(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改防火墙属性
     */
    @Test
    public void testModifyFirewallAttribute() {
        ModifyFirewallAttributeRequest request = new ModifyFirewallAttributeRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallUUID("fw-88000027evta");
        request.setDescription("test");
//        request.setFirewallName("test_cccccc");

        firewallAPI.modifyFirewallAttribute(request);
    }

    /**
     * 创建防火墙规则
     */
    @Test
    public void testCreateFirewallRule() {
        CreateFirewallRuleRequest request = new CreateFirewallRuleRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        List<CreateRuleParams> ruleParamsList = Lists.newArrayList();
//        ruleParamsList.add(setRuleParams(RuleDirection.OUT, "ALL", AuthorizType.CIDR, "0.0.0.0/0", "accept", null, "default rule"));
//        ruleParamsList.add(setRuleParams(RuleDirection.IN, "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "80/80", "default rule"));
//        ruleParamsList.add(setRuleParams(RuleDirection.IN, "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "443/443", "default rule"));
//        ruleParamsList.add(setRuleParams(RuleDirection.IN, "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "22/22", "default rule"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "90/150", "test"));
        ruleParamsList.add(setRuleParams("in", "TCP", AuthorizType.CIDR, "0.0.0.0/0", "accept", "1/6000", "test"));

        request.setFirewallUUID("fw-88000027evta");
        request.setCreateRuleParamsList(ruleParamsList);
        CreateFirewallRuleResponse response = firewallAPI.createFirewallRule(request);
        try {
            Thread.sleep(40000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        System.out.println(JSON.toJSONString(response));
    }

    /**
     * 删除防火墙规则
     */
    @Test
    public void testDeleteFirewallRule() {
        BatchDeleteFirewallRuleRequest request = new BatchDeleteFirewallRuleRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);
        request.setFirewallUUID("fw-eg000027bwif");
        request.setFirewallRuleUUIDList(Arrays.asList("rul-vh000027ch6p", "rul-vh000027ch6q"));

        firewallAPI.batchDeleteFirewallRule(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改规则
     */
    @Test
    public void testModifyFirewallRule() {
        ModifyFirewallRuleRequest request = new ModifyFirewallRuleRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallRuleUUID("rul-88000027evtw");
        request.setRuleDirection("in");
        request.setAuthorizObject("0.0.0.0/0");
        request.setAuthorizType(AuthorizType.CIDR);
        request.setPolicy("drop");
        request.setIpProtocol("TCP");
        request.setPortRange("80/80");
        request.setRemark("test_ModifyFirewallRule");

        firewallAPI.modifyFirewallRule(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 修改规则顺序
     */
    @Test
    public void testModifyFirewallRuleSort() {
        ModifyFirewallRuleSortRequest request = new ModifyFirewallRuleSortRequest();
        request.setOprUserId(userId);
        Set<Long> domainIds = Sets.newHashSet();
        domainIds.add(domainId);
        request.setDomainIds(domainIds);

        request.setFirewallRuleUUID("rul-88000027evtw");
        request.setDestFirewallRuleUUID("rul-88000027evty");
        request.setRulePosition(RulePosition.AFTER);
        firewallAPI.modifyFirewallRuleSort(request);
        try {
            Thread.sleep(50000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public CreateRuleParams setRuleParams(String ruleDirectionr, String ipProtocol, AuthorizType authorizType, String AuthorizObject,
                                          String policy, String portRange, String remark) {
        CreateRuleParams params = new CreateRuleParams();
        params.setRuleDirection(ruleDirectionr);
        params.setAuthorizObject(AuthorizObject);
        params.setAuthorizType(authorizType);
        params.setPolicy(policy);
        params.setIpProtocol(ipProtocol);
        params.setPortRange(portRange);
        params.setRemark(remark);
        return params;
    }
}
