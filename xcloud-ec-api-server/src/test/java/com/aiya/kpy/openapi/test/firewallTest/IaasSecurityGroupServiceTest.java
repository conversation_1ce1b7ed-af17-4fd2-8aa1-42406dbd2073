package com.aiya.kpy.openapi.test.firewallTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.iaas.manager.IaaSSecurityGroupManager;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.DescribeSecurityGroupEipsException;
import com.aiya.kpy.ec.foundation.iaas.service.impl.IaaSSecurityGroupServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @Author: chenqiang
 * @Date: 2021/10/28
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class IaasSecurityGroupServiceTest {

    @Autowired
    private IaaSSecurityGroupServiceImpl iaaSSecurityGroupService;

    @Autowired
    private IaaSSecurityGroupManager iaaSSecurityGroupManager;

    @Test
    public void TestDescribeSecurityGroupEips() {
        try {
            iaaSSecurityGroupManager.describeSecurityGroupEips("demo-2", null, "760ce368-7bf4-450a-98ca-d6dabf8a5ccf");
        } catch (DescribeSecurityGroupEipsException e) {
            e.printStackTrace();
        }
    }
}
