package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.async.foundation.model.AsyncTaskGroups;
import com.aiya.kpy.async.foundation.model.AsyncTaskName;
import com.aiya.kpy.common.enums.InstanceSeries;
import com.aiya.kpy.common.enums.ResourceProvider;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.ec.foundation.handler.sku.IaasSaleOutStrategyFactory;
import com.aiya.kpy.ec.foundation.iaas.model.EcsHostModel;
import com.aiya.kpy.ec.foundation.iaas.model.EcsHostParams;
import com.aiya.kpy.ec.foundation.iaas.service.IaaSEcsService;
import com.aiya.kpy.ec.foundation.provider.IaasServiceFactory;
import com.aiya.kpy.ecs.foundation.aspect.EcsSaleStockServiceAspect;
import com.aiya.kpy.ecs.foundation.entity.EcsInstanceEntity;
import com.aiya.kpy.ecs.foundation.model.EcsSaleStockLockParams;
import com.aiya.kpy.ecs.foundation.model.ResizeEcsInstanceTaskModel;
import com.aiya.kpy.ecs.foundation.service.EcsSaleStockService;
import com.aiya.kpy.ecs.foundation.service.impl.EcsSaleStockServiceImpl;
import com.aiya.kpy.ecs.foundation.service.impl.EcsServiceImpl;
import com.aiya.kpy.ecs.foundation.taskstate.ModifyEcsSpecTaskState;
import com.aiya.kpy.ecs.task.batch.EcsStockControlHandler;
import com.aiya.kpy.res.sdk.model.HostDetail;
import com.aiya.platform.asynctask.AsyncTaskSender;
import com.aiyounet.supervisor.ecs.service.SuperEcsService;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.aiya.kpy.ecs.foundation.util.SkuManageUtil.*;
import static com.aiya.kpy.res.kpyun.enums.SaleOutErrorEnum.NO_AVAILABLE_HOST_AVAILABLE;

/**
 * <AUTHOR>
 * @date 2024/2/19
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EcsSaleStockServiceTest {
    @Resource
    private EcsServiceImpl ecsService;
    @Resource
    private EcsSaleStockServiceImpl ecsStockLockService;
    @Resource
    private EcsSaleStockServiceAspect ecsSaleStockServiceAspect;
    @Autowired
    private AsyncTaskSender asyncTaskSender;
    @Resource
    private EcsStockControlHandler ecsStockControlHandler;

    @Test
    public void testStockLock() throws Exception {
        //ArrayList<String> list = Lists.newArrayList("ecs-rm00000ljh6r");
        //ecsStockLockService.addElasticStock(list);
        ecsStockControlHandler.execute(null);
    }

    @Test
    public void testToEcsGpuNumMap() {
        String json = "[{\"autoFailover\":false,\"availableMemory\":515072.0,\"cephmon\":\"\",\"createdAt\":\"2024-01-10 14:38:56\",\"description\":\"2080Ti\",\"disp\":\"***********:VPR4908P18102600045\",\"gpuCore\":7,\"gpuDeviceName\":\"GeForce RTX 2080 Ti\",\"gpuFree\":7,\"gpuMode\":1,\"id\":119,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"VPR4908P18102600045\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":96,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":515072.0,\"cephmon\":\"\",\"createdAt\":\"2024-01-06 01:04:48\",\"description\":\"2080Ti \",\"disp\":\"***********:VPR4908P18083000116\",\"gpuCore\":8,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":8,\"gpuMode\":1,\"id\":111,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"VPR4908P18083000116\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":96,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":515072.0,\"cephmon\":\"\",\"createdAt\":\"2024-01-06 01:04:48\",\"description\":\"2080Ti\",\"disp\":\"***********:VPR4908P18083000140\",\"gpuCore\":8,\"gpuDeviceName\":\"GeForce RTX 2080 Ti\",\"gpuFree\":8,\"gpuMode\":1,\"id\":110,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"i\n" +
                "sStandby\":false,\"sn\":\"VPR4908P18083000140\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":96,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":515072.0,\"cephmon\":\"\",\"createdAt\":\"2024-01-06 01:04:48\",\"description\":\"2080Ti CPU硬件故障\",\"disp\":\"***********:VPR4908P18083000130\",\"gpuCore\":8,\"gpuDeviceName\":\"GeForce RTX 2080 Ti\",\"gpuFree\":7,\"gpuMode\":1,\"id\":109,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"VPR4908P18083000130\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":96,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-12-04 19:22:35\",\"description\":\"2080Ti\\t\\n\",\"disp\":\"***********:A296675X9102041\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":0,\"gpuMode\":1,\"id\":97,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A296675X9102041\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-12-04 10:53:57\",\"description\":\"2080Ti\",\"disp\":\"***********:A18890140903432\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti\",\"gpuFree\":0,\"gpuMode\":1,\"id\":96,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFc\n" +
                "node\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A18890140903432\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-12-04 10:53:57\",\"description\":\"2080Ti\",\"disp\":\"***********:A263004X8B07381\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":1,\"gpuMode\":1,\"id\":95,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"***********\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A263004X8B07381\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-09-12 10:04:15\",\"description\":\"2080Ti\",\"disp\":\"***********:A16953529708826\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":0,\"gpuMode\":1,\"id\":65,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A16953529708826\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-09-12 10:04:15\",\"description\":\"2080Ti\",\"disp\":\"***********:A16953529708793\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":2,\"gpuMode\":1,\"id\":64,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"\",\"ipStorage\":\"***********\",\"isCal\n" +
                "cnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A16953529708793\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-08-09 15:55:51\",\"description\":\"2080Ti\",\"disp\":\"***********:A16953529709616\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti\",\"gpuFree\":0,\"gpuMode\":1,\"id\":46,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A16953529709616\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-08-09 15:55:45\",\"description\":\"2080Ti\",\"disp\":\"***********:A16953529610939\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":3,\"gpuMode\":1,\"id\":45,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"\",\"ipStorage\":\"***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A16953529610939\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]},{\"autoFailover\":false,\"availableMemory\":257024.0,\"cephmon\":\"\",\"createdAt\":\"2023-08-06 02:19:34\",\"description\":\"2080Ti \",\"disp\":\"***********:A16953529709595\",\"gpuCore\":4,\"gpuDeviceName\":\"GeForce RTX 2080 Ti Rev. A\",\"gpuFree\":0,\"gpuMode\":1,\"id\":44,\"ipManage\":\"***********\",\"ipNet\":\"***********\",\"ipSlave\":\"\",\"ipStorage\":\"\n" +
                "***********\",\"isCalcnode\":true,\"isFailover\":false,\"isFcnode\":false,\"isHp\":false,\"isLocal\":false,\"isNetnode\":false,\"isSriov\":false,\"isStandby\":false,\"sn\":\"A16953529709595\",\"space\":0,\"state\":1,\"systemTag\":4,\"tagDispName\":\"AnyGpu-Gn6A-r2080ti-独享GPU型\",\"tagId\":4,\"tagName\":\"aggn6ar2080ti\",\"totalCpu\":56,\"updatedAt\":\"\",\"vms\":[],\"zones\":[{\"id\":1,\"uid\":\"a\",\"zoneName\":\"a\"}]}]";
        List<HostDetail> hosts = JSON.parseArray(json, HostDetail.class);
        Map<String, List<Integer>> map = ecsStockLockService.toEcsUnitFreeMap(hosts, InstanceSeries.GPU);
        System.out.println(JSON.toJSONString(map));
    }

    @Test
    public void handleSaleOutTest() {
        Optional.ofNullable(IaasSaleOutStrategyFactory.getStrategy(NO_AVAILABLE_HOST_AVAILABLE.getValue()))
                .ifPresent(strategy -> strategy.handleSaleOut(ResourceType.LIGHTNODE_ECS, "cn-dev-1", "ecs.u16.m32"));
    }

    @Test
    public void updateSkuSaleManageTest() {
        EcsSaleStockService ecsSaleStockService = IaasServiceFactory.getIaasService(ResourceProvider.OWN, EcsSaleStockService.class);
        ecsSaleStockService.updateSkuSaleManage("cn-dev-2");
    }


    @Test
    public void lockAndUpdateSkuSaleManageTest() {
        EcsInstanceEntity ecsInstanceEntity = ecsService.queryEcsEntityByResourceUUID("ecs-rm00000lybee");
        EcsSaleStockLockParams params = new EcsSaleStockLockParams();
        params.setHostSn("818535226");
        params.setRegionCode("cn-dev-2");
        params.setResourceUUID(ecsInstanceEntity.getResourceUUID());
        params.setGpuModel(ecsInstanceEntity.getGpuModel());
        params.setGpuNum(ecsInstanceEntity.getGpuNum());
        params.setCpuModel(ecsInstanceEntity.getCpuModel());
        params.setInstanceSeries(InstanceSeries.fromCode(ecsInstanceEntity.getInstanceSeries()));
        params.setInstanceType(ecsInstanceEntity.getInstanceType());
        params.setCpu(ecsInstanceEntity.getCpu());
        params.setMemory(ecsInstanceEntity.getMemory());
        params.setSelfIsElastic(ecsInstanceEntity.getElastic());

        EcsSaleStockService ecsSaleStockService = IaasServiceFactory.getIaasService(ResourceProvider.OWN, EcsSaleStockService.class);
        ecsSaleStockService.relockAndUpdateSkuSaleManage(params);
    }

    @Test
    public void getHostsTest() {
        EcsInstanceEntity ecsInstance = ecsService.queryEcsEntityByResourceUUID("ecs-i70000006r71");
        InstanceSeries instanceSeries = InstanceSeries.fromCode(ecsInstance.getInstanceSeries());
        EcsHostParams ecsHostParams = EcsHostParams.builder()
                .regionCode("cn-dev-2")
                .model(getEcsUnitModel(ecsInstance))
                .unitSku(getEcsUnitSku(ecsInstance))
                .instanceSeries(ecsInstance.getInstanceSeries())
                .stockOccupy(getStockOccupy(ecsInstance))
                .build();
        IaaSEcsService iaasEcsService = IaasServiceFactory.getIaasService(ecsInstance.getResourceType(), IaaSEcsService.class);
        EcsHostModel hostModel = iaasEcsService.getHost(instanceSeries, ecsHostParams);
        System.out.println(JSON.toJSONString(hostModel));
    }

    @Resource
    private SuperEcsService superEcsService;

    @Test
    public void test12() {
        superEcsService.refreshSkuSaleManage();
    }

    @Test
    @Transactional
    public void sendTaskTest() {
        String json = "{\"cpu\":0,\"ecsResourceUUID\":\"ecs-rm00000ljh6r\",\"gpuParam\":{\"gpuModel\":\"RTX 2080Ti\",\"gpuNum\":0},\"iaaSEcsUUID\":\"5dbfe296-13ff-4736-8c0a-bb873ab93dc7\",\"instanceId\":164111,\"instanceSpec\":\"ecs.u8.m8\",\"memory\":0,\"oldEcsStatus\":\"STARTED\",\"operationTime\":1723457988674,\"overMaxRetryTime\":false,\"prodInfoChangeMessageType\":\"CHANGE_SPEC\",\"regionCode\":\"cn-dev-2\",\"resourceProvider\":\"OWN\",\"resourceType\":\"LIGHTNODE_ECS\",\"retryTime\":0,\"zoneCode\":\"cn-dev-2-a\"}";
        ResizeEcsInstanceTaskModel taskModel = JSON.parseObject(json, ResizeEcsInstanceTaskModel.class);
        asyncTaskSender.sendTask(AsyncTaskGroups.INSTANCE_TASK_GROUP, AsyncTaskName.MODIFY_ECS_SPEC,
                "ecs-rm00000ljh6r", taskModel, ModifyEcsSpecTaskState.INIT);
    }

    @Test
    @Transactional
    public void elasticFreeTest() {
        String json = "{\"cpu\":8,\"cpuModel\":\"Intel(R) Xeon(R) E5-2697 v3\",\"ecsUnitModel\":\"RTX 2080Ti\",\"ecsUnitSku\":\"RTX 2080Ti\",\"gpuModel\":\"RTX 2080Ti\",\"gpuNum\":1,\"hostSn\":\"818535226\",\"instanceSeries\":\"GPU\",\"instanceType\":\"ecs.u8.m8\",\"memory\":8,\"regionCode\":\"cn-dev-2\",\"resourceUUID\":\"ecs-rm00000lyz1i\"}";
        EcsSaleStockLockParams params = JSON.parseObject(json, EcsSaleStockLockParams.class);
        ecsSaleStockServiceAspect.elasticDownGrade(params);
    }

    @Test
    public void processElasticStockTest() throws Exception {
        //ecsSaleStockServiceAspect.processElasticStock("ecs-rm00000lzjcd", 1, -1);
        ecsSaleStockServiceAspect.waitForFinallySignal("ecs-rm00000lzjcd");
    }
}
