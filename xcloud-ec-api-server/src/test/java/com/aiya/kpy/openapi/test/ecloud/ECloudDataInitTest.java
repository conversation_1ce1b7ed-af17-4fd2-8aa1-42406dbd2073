package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.api.enums.NetWorkMode;
import com.aiya.kpy.ec.foundation.dao.KpyRegionDao;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudEcsManager;
import com.aiya.kpy.ec.foundation.entity.KpyRegionEntity;
import com.aiya.kpy.ec.foundation.enums.DataStatus;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.DescribeInstancesException;
import com.aiya.kpy.res.ecloud.sdk.response.GetRegionResponseContent;
import com.aiya.kpy.res.kpyun.dao.ResZoneProviderAccountDao;
import com.aiya.kpy.res.kpyun.entity.ResZoneProviderAccountEntity;
import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/20
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudDataInitTest {
    @Resource
    private KpyRegionDao kpyRegionDao;
    @Resource
    private ResZoneProviderAccountDao resZoneProviderAccountDAO;
    @Resource
    private ECloudEcsManager eCloudEcsManager;

    private static final String pools = "[{\"poolId\":\"CIDC-RP-49\",\"poolName\":\"西南-贵阳\"},{\"poolId\":\"CIDC-RP-28\",\"poolName\":\"华中-郑州\"},{\"poolId\":\"CIDC-RP-35\",\"poolName\":\"华东-杭州\"},{\"poolId\":\"CIDC-RP-26\",\"poolName\":\"华南-广州3\"},{\"poolId\":\"CIDC-RP-33\",\"poolName\":\"华东-上海1\"},{\"poolId\":\"CIDC-RP-31\",\"poolName\":\"华东-济南\"},{\"poolId\":\"CIDC-RP-29\",\"poolName\":\"华北-北京3\"},{\"poolId\":\"CIDC-RP-48\",\"poolName\":\"华北-呼和浩特\"},{\"poolId\":\"CIDC-RP-27\",\"poolName\":\"西南-成都\"},{\"poolId\":\"CIDC-RP-34\",\"poolName\":\"西南-重庆\"},{\"poolId\":\"CIDC-RP-25\",\"poolName\":\"华东-苏州\"},{\"poolId\":\"CIDC-RP-32\",\"poolName\":\"西北-西安\"},{\"poolId\":\"CIDC-RP-30\",\"poolName\":\"华中-长沙2\"},{\"poolId\":\"CIDC-RP-36\",\"poolName\":\"天津-天津\"},{\"poolId\":\"CIDC-RP-37\",\"poolName\":\"吉林-长春\"},{\"poolId\":\"CIDC-RP-38\",\"poolName\":\"湖北-襄阳\"},{\"poolId\":\"CIDC-RP-39\",\"poolName\":\"江西-南昌\"},{\"poolId\":\"CIDC-RP-40\",\"poolName\":\"甘肃-兰州\"},{\"poolId\":\"CIDC-RP-41\",\"poolName\":\"山西-太原\"},{\"poolId\":\"CIDC-RP-42\",\"poolName\":\"辽宁-沈阳\"},{\"poolId\":\"CIDC-RP-43\",\"poolName\":\"云南-昆明2\"},{\"poolId\":\"CIDC-RP-44\",\"poolName\":\"河北-石家庄\"},{\"poolId\":\"CIDC-RP-45\",\"poolName\":\"福建-厦门\"},{\"poolId\":\"CIDC-RP-46\",\"poolName\":\"广西-南宁\"},{\"poolId\":\"CIDC-RP-47\",\"poolName\":\"安徽-淮南\"},{\"poolId\":\"CIDC-RP-53\",\"poolName\":\"海南-海口\"},{\"poolId\":\"CIDC-RP-54\",\"poolName\":\"新疆-昌吉\"},{\"poolId\":\"CIDC-RP-55\",\"poolName\":\"黑龙江-哈尔滨\"},{\"poolId\":\"CIDC-RP-61\",\"poolName\":\"青海-海东\"}]";
    //生产环境RAM账号
    private static final String AK = "jKYsii9mUwaqd3oBNF44zkhbJJOX";
    private static final String SK = "gisxocY6rO1yzkfBfZAumCqA8S8zy8";
    private static final String END_POINT = "https://ecloud.10086.cn";

    /**
     * 这里仅初始化ec需要的region数据
     * 实际创建region入口在site-server
     */
    @Test
    public void testInsertKpyRegion() {
        JSON.parseArray(pools, Pool.class).forEach(pool -> {
            KpyRegionEntity entity = new KpyRegionEntity();
            entity.setRegionCode(pool.getPoolId());
            entity.setRegionName(pool.getPoolName());
            entity.setRegionDesc("移动云");
            entity.setCreateTime(new Date());
            entity.setRegionStatus(DataStatus.NORMAL);
            entity.setNetworkMode(NetWorkMode.VLAN_MODE);
            entity.setRegionSort(1);
            entity.setOwnerId(1L);
            kpyRegionDao.insert(entity);
        });
    }

    @Test
    public void testInsertResZoneProviderAccount() {
        JSON.parseArray(pools, Pool.class).forEach(pool -> {
            List<GetRegionResponseContent> regions = null;
            try {
                regions = eCloudEcsManager.getRegion(pool.getPoolId());
            } catch (DescribeInstancesException e) {
                throw new RuntimeException(e);
            }
            regions.stream()
                    .filter(r -> !r.getDeleted() && r.getVisible())
                    .forEach(r -> {
                        ResZoneProviderAccountEntity entity = new ResZoneProviderAccountEntity();
                        entity.setKpyRegionCode(pool.getPoolId());
                        entity.setZoneCode(r.getRegion());
                        entity.setZoneName(r.getName());
                        entity.setAccessKeyId(AK);
                        entity.setAccessKeySecret(SK);
                        entity.setReqUrl(END_POINT);
                        entity.setCreateTime(new Date());
                        entity.setRegionCode(pool.getPoolId());
                        entity.setLineId(null);
                        entity.setZone(r.getRegion());
                        resZoneProviderAccountDAO.insert(entity);
                    });
        });
    }


}
