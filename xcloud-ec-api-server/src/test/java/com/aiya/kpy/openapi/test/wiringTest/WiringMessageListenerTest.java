package com.aiya.kpy.openapi.test.wiringTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.message.ProdInfoChangeMessage;
import com.aiya.kpy.ec.api.model.ProdConfiguration;
import com.aiya.kpy.ec.task.mq.AccountChangeMessageListener;
import com.aiya.kpy.wiring.foundation.handler.WiringOrderHandle;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR> <PERSON>
 * @description
 * @date 2023/5/17 16:41
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class WiringMessageListenerTest {
    @Resource
    WiringOrderHandle handle;
    @Resource
    AccountChangeMessageListener accountChangeMessageListener;

    @Test
    public void createResourceTest() {
        String str = "{\n" +
                "      \"_class\": \"com.aiya.kpy.common.message.ProdInfoChangeMessage\",\n" +
                "      \"orderId\": 316363,\n" +
                "      \"orderUUID\": \"py00000014on\",\n" +
                "      \"orderPayStatus\": \"PAY_SUCCESS\",\n" +
                "      \"resourceType\": \"WIRING\",\n" +
                "      \"resourceUUID\": \"wiring-ld00000014ui\",\n" +
                "      \"messageType\": \"CREATE\",\n" +
                "      \"prodConfigJson\": \"{\\\"subnetId\\\":\\\"\\\",\\\"packageSpecCategory\\\":\\\"TK_WRING_PACKAGE\\\",\\\"hostName\\\":\\\"2023051714212875412\\\",\\\"memory\\\":1,\\\"packageSpec\\\":\\\"无人播套餐\\\",\\\"osDistroVersion\\\":\\\"CentOS\\\",\\\"bandwidthType\\\":\\\"BGP\\\",\\\"diskSize\\\":50,\\\"password\\\":\\\"\\\",\\\"zone\\\":\\\"demo-1-a\\\",\\\"vpcId\\\":\\\"\\\",\\\"autoRenew\\\":\\\"None\\\",\\\"diskType\\\":\\\"System\\\",\\\"diskCategory\\\":\\\"local\\\",\\\"salePattern\\\":\\\"package\\\",\\\"image\\\":\\\"linux\\\",\\\"instanceChargeType\\\":\\\"PostPaid\\\",\\\"imageId\\\":\\\"img-1e6bd5foq6zqkorvjpg01\\\",\\\"imageName\\\":\\\"Centos 7.3 64bit\\\",\\\"bandwidth\\\":5,\\\"netType\\\":\\\"privateNet\\\",\\\"instanceType\\\":\\\"s3\\\",\\\"cpu\\\":1,\\\"instanceSpec\\\":\\\"ecs.n1.tiny\\\",\\\"region\\\":\\\"demo-1\\\"}\",\n" +
                "      \"userId\": 30739,\n" +
                "      \"orderType\": \"BuyProduct\",\n" +
                "      \"clientToken\": \"py00000014os\",\n" +
                "      \"billingCycle\": \"HOUR\",\n" +
                "      \"domainId\": 1\n" +
                "    }";
        ProdInfoChangeMessage message = JSONObject.parseObject(str, ProdInfoChangeMessage.class);
        ProdConfiguration prodConfig = JSON.parseObject(message.getProdConfigJson(), ProdConfiguration.class);
        handle.createResource(prodConfig, message);
    }
    @Test
    public void replaceIpResourceTest() {
        String str = "{\n" +
                "      \"_class\": \"com.aiya.kpy.common.message.ProdInfoChangeMessage\",\n" +
                "      \"orderId\": 316385,\n" +
                "      \"orderUUID\": \"sa0000001dsx\",\n" +
                "      \"orderPayStatus\": \"PAY_SUCCESS\",\n" +
                "      \"resourceType\": \"WIRING\",\n" +
                "      \"resourceUUID\": \"wiring-ld00000017bf\",\n" +
                "      \"messageType\": \"REPLACE_IP\",\n" +
                "      \"prodConfigJson\": \"{\\\"bandwidthType\\\":\\\"BGP\\\",\\\"oprUserId\\\":30739,\\\"chargeMode\\\":\\\"PayByReplaceIp\\\",\\\"bandwidth\\\":50,\\\"zone\\\":\\\"demo-2-a\\\",\\\"cidrBlock\\\":\\\"***********/24\\\",\\\"changeCount\\\":1,\\\"region\\\":\\\"demo-2\\\"}\",\n" +
                "      \"userId\": 30739,\n" +
                "      \"orderType\": \"ReplaceIp\",\n" +
                "      \"clientToken\": \"sa0000001e33\",\n" +
                "      \"billingCycle\": \"DAY\",\n" +
                "      \"domainId\": 1\n" +
                "    }";
        ProdInfoChangeMessage message = JSONObject.parseObject(str, ProdInfoChangeMessage.class);
        ProdConfiguration prodConfig = JSON.parseObject(message.getProdConfigJson(), ProdConfiguration.class);
        handle.replaceIpResource(prodConfig, message);
    }

    @Test
    public void accountChangeTest() {
        String str = "{\n" +
                "      \"_class\": \"com.aiya.kpy.ec.foundation.model.AccountChangeNotifyMessage\",\n" +
                "      \"users\": [\n" +
                "        {\n" +
                "          \"_id\": 30741,\n" +
                "          \"domainId\": 1\n" +
                "        }\n" +
                "      ],\n" +
                "      \"status\": \"LOCKING\"\n" +
                "    }";

//        accountChangeMessageListener.dealWithResourceUnLock(JSONObject.parseObject(str, AccountChangeNotifyMessage.class));
//        accountChangeMessageListener.dealWithResourceLock(JSONObject.parseObject(str, AccountChangeNotifyMessage.class));
    }

}
