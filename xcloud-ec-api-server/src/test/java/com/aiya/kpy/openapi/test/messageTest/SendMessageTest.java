package com.aiya.kpy.openapi.test.messageTest;

import com.SpringBootStarter;
import com.aiya.kpy.common.enums.ResourceType;
import com.aiya.kpy.ec.foundation.constant.Constant;
import com.aiya.kpy.ec.foundation.constant.SmsTemplateCode;
import com.aiya.kpy.ec.foundation.service.SendMessageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR> by huangxy
 * @date 2021/6/3
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SendMessageTest {
    @Autowired
    private SendMessageService sendMessageService;

//    @Test
//    public void testSendEcsCreateSuccess(){
//        sendMessageService.sendEcsCreateResourceSuccess(109724L, "demo-1", "***********",
//                "root", "12345678aA", SmsTemplateCode.KPY_ECS_CREATE_SUCCESS);
//    }

    @Test
    public void testSendEcsReinstallSystemSuccess() {
        sendMessageService.sendEcsReinstallSystemSuccess(109724L, "", "***********",
                "root", "12345678aA", SmsTemplateCode.ECS_REINSTALL_SYSTEM_SUCCESS);
    }

    @Test
    public void testSendModifyPasswordSuccess() {
        String[] params = new String[]{"***********", "aA12345678"};
        sendMessageService.sendEcsModifyPassWordSuccess(params, SmsTemplateCode.KPY_ECS_MODIFY_PASSWORD_SUCCESS, "ecs-qm00001tb8ko",
                ResourceType.LIGHTNODE_ECS);
    }

    @Test
    public void testSendReplaceIpAddressSuccess() {
        sendMessageService.sendReplacePublicIpAddressSuccess("eip-qm00001tb8kr", ResourceType.OWN_EIP,
                "127.0.0.1", "***********", SmsTemplateCode.PUBLIC_IP_REPLACE_SUCCESS);
    }

    @Test
    public void testSendFlowUsedWarning() {
        sendMessageService.sendFlowUsedWarnning("eip-qm00001tb8kr", ResourceType.OWN_EIP,
                "***********", SmsTemplateCode.FLOW_USED_WARNING_SMS_CODE);

        sendMessageService.sendFlowUsedWarnning("eip-qm00001tb8kr", ResourceType.OWN_EIP,
                "***********", SmsTemplateCode.FLOW_USED_OUT);
    }

    @Test
    public void testSendEcsWillRelease() {
        sendMessageService.sendEcsWillDelete(109724L, Constant.WILL_RELEASE_24_HOUR, SmsTemplateCode.ECS_RESOURCE_WILL_RELEASE);
    }

    @Test
    public void testSendEcsReleaseSuccess() {
        sendMessageService.sendEcsDeleteSuccess(ResourceType.LIGHTNODE_ECS, "ecs-la0000088gsn", SmsTemplateCode.ECS_RELEASE_RESOURCE_SUCCESS);
    }
}
