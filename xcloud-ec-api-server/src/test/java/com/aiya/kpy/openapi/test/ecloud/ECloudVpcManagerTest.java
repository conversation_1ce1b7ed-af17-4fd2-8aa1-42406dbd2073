package com.aiya.kpy.openapi.test.ecloud;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.ecloud.manager.ECloudVpcManager;
import com.aiya.kpy.ec.foundation.ecloud.manager.param.CreateECloudNetworkParam;
import com.aiya.kpy.ec.foundation.ecloud.manager.param.CreateECloudVpcParam;
import com.aiya.kpy.ec.foundation.iaas.manager.exception.*;
import com.aiya.kpy.res.ecloud.enums.*;
import com.aiya.kpy.res.ecloud.sdk.request.CreatePortRequest;
import com.aiya.kpy.res.ecloud.sdk.request.CreateSecurityGroupRuleRequest;
import com.aiya.kpy.res.ecloud.sdk.response.*;
import com.aiya.kpy.res.ecloud.sdk.thirdrequest.VpcCreateNetworkRequestSubnets;
import com.alibaba.fastjson.JSON;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/3/7
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class ECloudVpcManagerTest {
    @Resource
    private ECloudVpcManager eCloudVpcManager;


    @Test
    public void getVpcListTest() throws DescribeVpcException {
        GetVpcListResponse response = eCloudVpcManager.getVpcList(Params.poolId, Params.region, 1, 10);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"adminStateUp":true,"createdTime":"2023-03-22 16:48:11","deleted":false,"description":"vpc_default","ecStatus":"ACTIVE","edge":false,"id":"417b231652754dbabe864ef04a42dc84","name":"vpc_default","orderType":"MOP","region":"N020-GD-GZNJ01","routerId":"5a5d0fab-af66-4c8a-bbcd-607c3bc2e74a","scale":"HIGH","userId":"CIDC-U-5c1b7c2d213f443db148d7a7e5ca1699","userName":"RFY5912415207"}],"empty":false,"total":1}
    }

    @Test
    public void getVpcDetailTest() throws Exception {
        GetVpcDetailResponse response = eCloudVpcManager.getVpcDetail(Params.poolId, Params.vpcId);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getGetVpcNetworkTest() throws DescribeVpcException {
        GetVpcNetworkResponse response = eCloudVpcManager.getVpcNetwork(Params.poolId, Params.region, 1, 100);
        System.out.println(JSON.toJSONString(response));
        //{"content":[{"createdTime":"2024-03-06 10:07:24","deleted":false,"ecStatus":"ACTIVE","edge":false,"enabled":true,"id":"38989b18-cd8c-43ae-b3df-802d1500e4a4","name":"subnet_default","networkTypeEnum":"VM","orderSource":"OP","region":"DGJD","routerId":"c90a403e-d2d7-4a4e-930f-6baae8a1c403","shared":false,"subnets":[{"cidr":"2409:8c5b:ffff:5a11::/64","createdTime":"2024-03-06 10:07:24","deleted":false,"edge":false,"gatewayIp":"2409:8c5b:ffff:5a11::1","id":"8bcbcddf-18dc-48c6-9370-321fad10dace","ipVersion":"_6","name":"subnet_default","networkId":"38989b18-cd8c-43ae-b3df-802d1500e4a4","region":"DGJD"},{"cidr":"***********/24","createdTime":"2024-03-06 10:07:24","deleted":false,"edge":false,"gatewayIp":"***********","id":"d0ba2a0d-0cee-4a52-ba42-dac4408af5d2","ipVersion":"_4","name":"subnet_default","networkId":"38989b18-cd8c-43ae-b3df-802d1500e4a4","region":"DGJD"}]}],"empty":false,"total":1}
        //{"content":[{"createdTime":"2024-01-18 17:19:55","deleted":false,"ecStatus":"ACTIVE","edge":false,"enabled":true,"id":"0b954893-33de-4bfc-9fa4-cafb265d548e","name":"yu_an","networkTypeEnum":"VM","orderSource":"OP","region":"DGJD","routerId":"5a5d0fab-af66-4c8a-bbcd-607c3bc2e74a","shared":false,"subnets":[{"cidr":"********/24","createdTime":"2024-01-18 17:19:55","deleted":false,"edge":false,"gatewayIp":"********","id":"e81138f6-2660-4fa9-a85a-579a3c232b0c","ipVersion":"_5","name":"yu_an","networkId":"0b954893-33de-4bfc-9fa4-cafb265d548e","region":"DGJD"}]}],"empty":false,"total":1}
    }

    @Test
    public void createVpcTest() throws Exception {
        CreateECloudVpcParam param = new CreateECloudVpcParam();
        param.setPlatformRegionCode(Params.poolId);
        param.setPlatformZoneCode(Params.region);
        param.setName(Params.name);
        param.setNetworkName(Params.name);
        param.setSpecs(SpecsEnum.HIGH);
        for (int i = 20; i < 21; i++) {
            param.setCidr("10.5." + i + ".0/24");
            CreateVpcResponse response = eCloudVpcManager.createVpc(param);
            System.out.println(JSON.toJSONString(response));
        }
        //{"orderExtResps":[{"orderExtId":"MOP-T-24032739030872","orderExtStatus":6,"productType":"ROUTER"}],"orderId":"MOP-O-24032789526160"}
    }

    @Test
    public void deleteVpcTest() throws ReleaseVpcException {
        DeleteVpcResponse response = eCloudVpcManager.deleteVpc(Params.poolId, Params.routerId);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getPortListTest() throws GetPortException {
        //GetPortListRequest request = GetPortListRequest.builder().build();
        //GetPortListResponse response = eCloudVpcManager.getPortList(Params.poolId, request);
        //System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void createPortTest() throws CreatePortException {
        CreatePortRequest request = CreatePortRequest.builder().build();
        CreatePortResponse response = eCloudVpcManager.createPort(Params.poolId, request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getSubnetListTest() throws DescribeVswitchException {
        //GetSubnetListRequest request = GetSubnetListRequest.builder().build();
        //GetSubnetListResponse response = eCloudVpcManager.getSubnetList(Params.poolId, request);
        //System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void createNetworkTest() throws CreateVswitchException {
//        CreateECloudNetworkParam request = CreateECloudNetworkParam.builder().build();
        CreateECloudNetworkParam param = new CreateECloudNetworkParam();
        param.setPlatformZoneCode(Params.poolId);
        param.setNetworkName(Params.name);
        param.setNetworkTypeEnum(NetworkTypeEnum.VM);
        param.setRouterId(Params.routerId);
        param.setSubnets(
                Lists.newArrayList(
                        VpcCreateNetworkRequestSubnets.builder()
                                .cidr("*********/24")
                                .ipVersion(VersionsEnum._4)
                                .build()
                )
        );
        CreateNetworkResponse response = eCloudVpcManager.createNetwork(param);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void deleteNetworkTest() throws DeleteSubnetException {
        //DeleteNetworkRequest request = DeleteNetworkRequest.builder().build();
        //DeleteNetworkResponse response = eCloudVpcManager.deleteNetwork(Params.poolId, request);
        //System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getSecurityGroupListTest() throws DescribeEipSecurityGroupsException {
        //GetSecurityGroupListRequest request = GetSecurityGroupListRequest.builder().build();
        //GetSecurityGroupListResponse response = eCloudVpcManager.getSecurityGroupList(Params.poolId, request);
        //System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void getSecurityGroupRuleListTest() throws DescribeEipSecurityGroupsException {
        GetSecurityGroupRuleListResponse response = eCloudVpcManager.getSecurityGroupRuleList(Params.poolId, Params.securityGroupId, 1, 10);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void createSecurityGroupRuleTest() throws CreateSecurityGroupRuleException {
        CreateSecurityGroupRuleRequest request = CreateSecurityGroupRuleRequest.builder()
                .direction(DirectionEnum.INGRESS)
                .securityGroupId(Params.securityGroupId)
                .protocol(ProtocolEnum.ANY)
                .remoteType(RemoteTypeEnum.CIDR)
                .remoteIpPrefix("********")
                .build();
        CreateSecurityGroupRuleResponse response = eCloudVpcManager.createSecurityGroupRule(Params.poolId, request);
        System.out.println(JSON.toJSONString(response));
        //{"content":"60017572-fa51-434a-aca5-973eec91a402"}
    }
}
