package com.aiya.kpy.openapi.test.ecsTest;

import com.SpringBootStarter;
import com.aiya.kpy.ecs.task.batch.EcsInstanceUseStatisticsHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/5/22
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class EcsInstanceUseStatisticsHandlerTest {
    @Resource
    private EcsInstanceUseStatisticsHandler ecsInstanceUseStatisticsHandler;

    @Test
    public void testStatistics() throws Exception {
        ecsInstanceUseStatisticsHandler.execute(null);
    }
}
