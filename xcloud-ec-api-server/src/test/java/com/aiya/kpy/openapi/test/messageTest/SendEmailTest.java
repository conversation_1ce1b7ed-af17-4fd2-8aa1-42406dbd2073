package com.aiya.kpy.openapi.test.messageTest;

import com.SpringBootStarter;
import com.aiya.kpy.ec.foundation.constant.EmailTemplateCode;
import com.aiya.kpy.ec.foundation.service.SendEmailService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = SpringBootStarter.class)
public class SendEmailTest {
    @Autowired
    private SendEmailService sendEmailService;

    @Test
    public void testSendAlertOrRecoveryNotice() {
        sendEmailService.sendAlertOrRecoveryNotice(109724L, "test_ecsName", "192.168.1.1",
                "16:00", "30minutes", "test_ruleName", "Cpu utilization rate >= 80% for 5 minutes; Bandwidth : public network out bandwidth >= 100Mbps, statistics for 5 minutes; Traffic : public network out bandwidth >= 100G for 5 minutes", EmailTemplateCode.YOUR_RESOURCE_IS_BACK_TO_NORMAL);
    }

    @Test
    public void testSendMergeAlertNotice() {

        //拼接邮箱通知参数
        List<String> emailParams = Lists.newArrayList();
        emailParams.add(String.valueOf(3));

        emailParams.add("主机名称");
        emailParams.add("ecs-aaaaaaaaa");
        emailParams.add("demo-1");
        emailParams.add("198.168.1.1");
        emailParams.add("1hour30minutes");
        emailParams.add("规则名称");
        emailParams.add("Cpu utilization rate >= 80% for 5 minutes;");

        emailParams.add("主机名称");
        emailParams.add("ecs-aaaaaaaaa");
        emailParams.add("demo-1");
        emailParams.add("198.168.1.1");
        emailParams.add("1hour30minutes");
        emailParams.add("规则名称");
        emailParams.add("Cpu utilization rate >= 80% for 5 minutes;");

        //参数必须为36个，否则消息服务校验不通过
        while (true) {
            if (emailParams.size() < 36) {
                emailParams.add("");
            } else {
                break;
            }
        }
        sendEmailService.sendMergeAlertNotice(109724L, emailParams.toArray(new String[emailParams.size()]), EmailTemplateCode.NOTIFICATION_MULTIPLE_RESOURCES);
    }

}
