<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:msgqueue="http://www.aiya.com/schema/msgqueue"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd 
     http://www.aiya.com/schema/msgqueue
     http://www.aiya.com/schema/msgqueue/msg-queue.xsd">

    <msgqueue:broad-cast-sender id="prodInfoChangeMessageSender"
                                redis-client="broadCastMessageRedisClient">
        <msgqueue:topic
                class-name="com.aiya.kpy.common.constants.BroadCastMessageTopicName"
                field-name="PROD_INFO_CHANGE_MESSAGE"/>
        <msgqueue:debug enable="false" client-node-id="hxy"/>
    </msgqueue:broad-cast-sender>
</beans>