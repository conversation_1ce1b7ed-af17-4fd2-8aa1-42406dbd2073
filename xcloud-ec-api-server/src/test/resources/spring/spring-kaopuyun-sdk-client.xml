<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd

     http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">


    <!--</plat-dubbo:reference>-->
    <context:component-scan base-package="com.aiya.kpy.res.kpyun.client">
    </context:component-scan>
    <context:component-scan base-package="com.aiya.kpy.res.sdk.aliyun.client">
    </context:component-scan>
    <context:component-scan base-package="com.aiya.res.antiddosip.client">
    </context:component-scan>
    <context:property-placeholder location="classpath:config/*.properties"/>
    <context:component-scan base-package="kaopuyun">
    </context:component-scan>
    <context:component-scan base-package="anti">
    </context:component-scan>
    <context:component-scan base-package="com.aiya.kpy.ec.foundation.iaas.test.**">
    </context:component-scan>
</beans>