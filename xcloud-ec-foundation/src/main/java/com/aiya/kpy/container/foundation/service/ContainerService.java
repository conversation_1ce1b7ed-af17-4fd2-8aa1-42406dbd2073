package com.aiya.kpy.container.foundation.service;

import com.aiya.kpy.res.sdk.request.*;
import com.aiya.kpy.res.sdk.response.*;
import com.aiya.kpy.common.model.PageData;

/**
 * 容器云服务接口
 * 用于容器云平台的核心业务服务，提供容器实例、网卡、VLAN、SSH密钥等管理功能。
 * 直接调用底层SDK接口，处理容器相关的所有操作。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
public interface ContainerService {

    /**
     * 创建容器实例
     * 根据指定的配置参数创建新的容器实例，包括规格、镜像、网络等配置
     *
     * @param request 创建容器实例请求参数
     * @return 创建容器实例响应结果
     */
    CreateContainerInstanceResponse createContainerInstance(CreateContainerInstanceRequest request);

    /**
     * 查询容器实例列表
     * 支持分页查询和多种条件过滤，返回容器实例的详细信息
     *
     * @param request 查询容器实例列表请求参数
     * @return 容器实例列表响应结果
     */
    DescribeContainerInstancesResponse describeContainerInstances(DescribeContainerInstancesRequest request);

    /**
     * 删除容器实例
     * 根据容器UUID删除指定的容器实例
     *
     * @param request 删除容器实例请求参数
     * @return 删除容器实例响应结果
     */
    DeleteContainerInstanceResponse deleteContainerInstance(DeleteContainerInstanceRequest request);

    /**
     * 启动容器实例
     * 启动指定的容器实例，使其进入运行状态
     *
     * @param request 启动容器实例请求参数
     * @return 启动容器实例响应结果
     */
    StartContainerInstanceResponse startContainerInstance(StartContainerInstanceRequest request);

    /**
     * 停止容器实例
     * 停止指定的容器实例，使其进入停止状态
     *
     * @param request 停止容器实例请求参数
     * @return 停止容器实例响应结果
     */
    StopContainerInstanceResponse stopContainerInstance(StopContainerInstanceRequest request);

    /**
     * 创建容器网卡
     * 为容器创建新的网络接口，配置IP地址、网关等网络参数
     *
     * @param request 创建容器网卡请求参数
     * @return 创建容器网卡响应结果
     */
    CreateContainerNicResponse createContainerNic(CreateContainerNicRequest request);

    /**
     * 创建容器VLAN
     * 创建新的虚拟局域网，用于容器网络隔离和管理
     *
     * @param request 创建容器VLAN请求参数
     * @return 创建容器VLAN响应结果
     */
    CreateContainerVlanResponse createContainerVlan(CreateContainerVlanRequest request);

    /**
     * 查询容器VLAN列表
     * 支持分页查询和条件过滤，返回VLAN配置信息
     *
     * @param request 查询容器VLAN列表请求参数
     * @return 容器VLAN列表响应结果
     */
    DescribeContainerVlansResponse describeContainerVlans(DescribeContainerVlansRequest request);

    /**
     * 创建容器SSH密钥
     * 创建新的SSH密钥对，用于容器的安全登录认证
     *
     * @param request 创建容器SSH密钥请求参数
     * @return 创建容器SSH密钥响应结果
     */
    CreateContainerSshkeyResponse createContainerSshkey(CreateContainerSshkeyRequest request);

    /**
     * 查询容器SSH密钥列表
     * 支持分页查询和条件过滤，返回SSH密钥信息
     *
     * @param request 查询容器SSH密钥列表请求参数
     * @return 容器SSH密钥列表响应结果
     */
    DescribeContainerSshkeysResponse describeContainerSshkeys(DescribeContainerSshkeysRequest request);
}
