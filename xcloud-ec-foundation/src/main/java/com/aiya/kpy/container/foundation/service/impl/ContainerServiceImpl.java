package com.aiya.kpy.container.foundation.service.impl;

import com.aiya.kpy.container.foundation.service.ContainerService;
import com.aiya.kpy.res.kpyun.client.KpyunSdkClient;
import com.aiya.kpy.res.kpyun.client.KpySdkResponse;
import com.aiya.kpy.res.kpyun.client.KpyunSdkClientCache;
import com.aiya.kpy.res.kpyun.dao.ResZoneProviderAccountDao;
import com.aiya.kpy.res.kpyun.entity.ResZoneProviderAccountEntity;
import com.aiya.kpy.res.kpyun.enums.KpyunProduct;
import com.aiya.kpy.res.sdk.request.*;
import com.aiya.kpy.res.sdk.response.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 容器云服务实现类
 * 通过KpyunSdkClient调用底层容器服务API，实现容器云平台的核心业务功能。
 * 包含详细的日志记录和异常处理机制。
 *
 * <AUTHOR>
 * @date 2025/01/24
 */
@Slf4j
@Service
public class ContainerServiceImpl implements ContainerService {

    @Autowired
    private KpyunSdkClientCache kpyunSdkClientCache;

    @Autowired
    private ResZoneProviderAccountDao resZoneProviderAccountDao;

    /**
     * 执行SDK请求的通用方法
     */
    private <T extends com.aiya.kpy.res.kpyun.response.KpyRpcAcsResponse> T executeRequest(
            com.aiya.kpy.res.kpyun.request.KpyRpcAcsRequest<T> request, String regionId, String operation) {
        try {
            // 获取账户信息
            ResZoneProviderAccountEntity account = getAccountByRegionId(regionId);

            // FIXME: 需要在KpyunProduct枚举中添加CONTAINER产品，然后使用KpyunProduct.CONTAINER
            // 临时使用ECS产品，等CONTAINER产品添加后再修改
            KpyunSdkClient client = kpyunSdkClientCache.getCacheKpyunSdkClient(account, KpyunProduct.ECS);
            KpySdkResponse<T> response = client.execute(request);

            if (response.isSuccess()) {
                return response.getAcsResponse();
            } else {
                log.error("{}失败，错误码：{}，错误信息：{}", operation, response.getCode(), response.getErrMsg());
                throw new RuntimeException(operation + "失败：" + response.getErrMsg());
            }
        } catch (Exception e) {
            log.error("{}失败，错误信息：{}", operation, e.getMessage(), e);
            throw new RuntimeException(operation + "失败：" + e.getMessage(), e);
        }
    }

    /**
     * 根据地区ID获取账户信息
     */
    private ResZoneProviderAccountEntity getAccountByRegionId(String regionId) {
        ResZoneProviderAccountEntity account = resZoneProviderAccountDao.selectFirstByKpyRegionCode(regionId);
        Assert.notNull(account, "账户信息不存在，regionId: " + regionId);
        return account;
    }

    @Override
    public CreateContainerInstanceResponse createContainerInstance(CreateContainerInstanceRequest request) {
        log.info("开始创建容器实例，请求参数：{}", request);
        CreateContainerInstanceResponse response = executeRequest(request, request.getRegionId(), "创建容器实例");
        log.info("创建容器实例成功，响应结果：{}", response);
        return response;
    }

    @Override
    public DescribeContainerInstancesResponse describeContainerInstances(DescribeContainerInstancesRequest request) {
        log.info("开始查询容器实例列表，请求参数：{}", request);
        DescribeContainerInstancesResponse response = executeRequest(request, request.getRegionId(), "查询容器实例列表");
        log.info("查询容器实例列表成功，返回{}条记录",
            response.getInfo() != null ? response.getInfo().size() : 0);
        return response;
    }

    @Override
    public DeleteContainerInstanceResponse deleteContainerInstance(DeleteContainerInstanceRequest request) {
        log.info("开始删除容器实例，容器UUID：{}", request.getUuid());
        DeleteContainerInstanceResponse response = executeRequest(request, request.getRegionId(), "删除容器实例");
        log.info("删除容器实例成功，容器UUID：{}，任务UUID：{}", request.getUuid(), response.getTaskUuid());
        return response;
    }

    @Override
    public StartContainerInstanceResponse startContainerInstance(StartContainerInstanceRequest request) {
        log.info("开始启动容器实例，容器UUID：{}", request.getUuid());
        StartContainerInstanceResponse response = executeRequest(request, request.getRegionId(), "启动容器实例");
        log.info("启动容器实例成功，容器UUID：{}，任务UUID：{}", request.getUuid(), response.getTaskUuid());
        return response;
    }

    @Override
    public StopContainerInstanceResponse stopContainerInstance(StopContainerInstanceRequest request) {
        log.info("开始停止容器实例，容器UUID：{}", request.getUuid());
        StopContainerInstanceResponse response = executeRequest(request, request.getRegionId(), "停止容器实例");
        log.info("停止容器实例成功，容器UUID：{}，任务UUID：{}", request.getUuid(), response.getTaskUuid());
        return response;
    }

    @Override
    public CreateContainerNicResponse createContainerNic(CreateContainerNicRequest request) {
        log.info("开始创建容器网卡，请求参数：{}", request);
        CreateContainerNicResponse response = executeRequest(request, request.getRegionId(), "创建容器网卡");
        log.info("创建容器网卡成功，网卡UUID：{}",
            response.getInfo() != null ? response.getInfo().getUuid() : "未知");
        return response;
    }

    @Override
    public CreateContainerVlanResponse createContainerVlan(CreateContainerVlanRequest request) {
        log.info("开始创建容器VLAN，请求参数：{}", request);
        CreateContainerVlanResponse response = executeRequest(request, request.getRegionId(), "创建容器VLAN");
        log.info("创建容器VLAN成功，VLAN UUID：{}",
            response.getInfo() != null ? response.getInfo().getUuid() : "未知");
        return response;
    }

    @Override
    public DescribeContainerVlansResponse describeContainerVlans(DescribeContainerVlansRequest request) {
        log.info("开始查询容器VLAN列表，请求参数：{}", request);
        DescribeContainerVlansResponse response = executeRequest(request, request.getRegionId(), "查询容器VLAN列表");
        log.info("查询容器VLAN列表成功，返回{}条记录",
            response.getInfo() != null ? response.getInfo().size() : 0);
        return response;
    }

    @Override
    public CreateContainerSshkeyResponse createContainerSshkey(CreateContainerSshkeyRequest request) {
        log.info("开始创建容器SSH密钥，请求参数：{}", request);
        CreateContainerSshkeyResponse response = executeRequest(request, request.getRegionId(), "创建容器SSH密钥");
        log.info("创建容器SSH密钥成功，密钥UUID：{}",
            response.getInfo() != null ? response.getInfo().getUuid() : "未知");
        return response;
    }

    @Override
    public DescribeContainerSshkeysResponse describeContainerSshkeys(DescribeContainerSshkeysRequest request) {
        log.info("开始查询容器SSH密钥列表，请求参数：{}", request);
        DescribeContainerSshkeysResponse response = executeRequest(request, request.getRegionId(), "查询容器SSH密钥列表");
        log.info("查询容器SSH密钥列表成功，返回{}条记录",
            response.getInfo() != null ? response.getInfo().size() : 0);
        return response;
    }
}
