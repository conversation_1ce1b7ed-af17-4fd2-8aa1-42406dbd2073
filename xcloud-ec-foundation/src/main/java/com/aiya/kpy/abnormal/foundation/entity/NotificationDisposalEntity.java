package com.aiya.kpy.abnormal.foundation.entity;


import com.aiya.kpy.abnormal.api.enums.DisposalType;
import com.aiya.kpy.abnormal.api.enums.NotificationStatus;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "NOTIFICATION_DISPOSAL")
@Data
public class NotificationDisposalEntity implements Serializable {


    private static final long serialVersionUID = -3437398392233477763L;
    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    private Long id;

    /**
     * 处理事件UUID
     */
    @Column(name = "DISPOSAL_UUID")
    private String disposalUUID;

    /**
     * 异常通报事件UUID
     */
    @Column(name = "NOTIFICATION_UUID")
    private String notificationUUID;

    /**
     * 异常通报事件名称
     */
    @Column(name = "NOTIFICATION_NAME")
    private String notificationName;

    /**
     * 处理方式
     */
    @Column(name = "DISPOSAL_TYPE")
    private DisposalType disposalType;

    /**
     * 创建时间
     */
    @Column(name = "CREATE_TIME")
    private Date createTime;

    /**
     * 操作人id
     */
    @Column(name = "OPERATOR_ID")
    private String OperatorId;

}
