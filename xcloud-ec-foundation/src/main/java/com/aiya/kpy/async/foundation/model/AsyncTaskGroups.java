package com.aiya.kpy.async.foundation.model;

/**
 * 封装所有异步任务的组名
 *
 * <AUTHOR>
 */
public class AsyncTaskGroups {
	/**
	 * 磁盘的异步任务
	 */
	public static final String DISK_TASK_GROUP = "diskTaskGroup";

	/**
	 * 镜像的异步任务
	 */
	public static final String IMAGE_TASK_GROUP = "imageTaskGroup";
	/**
	 * 实例组任务
	 */
	public static final String INSTANCE_TASK_GROUP = "instanceTaskGroup";

	/**
	 * 快照的异步任务
	 */
	public static final String SNAPSHOT_TASK_GROUP = "snapshotTaskGroup";

	/**
	 * VPC组任务
	 */
	public static final String VPC_TASK_GROUP = "vpcTaskGroup";

	/**
	 * VPC子网组任务
	 */
	public static final String VPC_SUBNET_TASK_GROUP = "vpcSubnetTaskGroup";
	/**
	 * 公共的异步任务
	 */
	public static final String COMMON_GROUP = "commonTaskGroup";
	/**
	 * 公共的广播消息异步任务
	 */
	public static final String BROAD_CAST_MSG_TASK_GROUP = "broadCastMsgTaskGroup";

	/**
	 * 公网IP异步任务
	 */
	public static final String PUBLIC_IP_TASK_GROUP = "publicIpTaskGroup";

	/**
	 * 流量包
	 */
	public static final String FLOW_PACKET_TASK_GROUP = "flowPacketTaskGroup";

	/**
	 * 防火墙异步任务
	 */
	public static final String FIREWALL_TASK_GROUP = "firewallTaskGroup";

	/**
	 * 防火墙规则异步任务
	 */
	public static final String FIREWALL_RULE_TASK_GROUP = "firewallRuleTaskGroup";

	/**
	 * 对象存储异步任务
	 */
	public static final String OBJECT_STORAGE_TASK_GROUP = "ObjectStorageTaskGroup";

	/**
	 * 线路异步任务
	 */
	public static final String WIRING_TASK_GROUP = "WiringTaskGroup";

	/**
	 * 物联网卡
	 */
	public static final String IOT_CARD_TASK_GROUP = "IotCardTaskGroup";

	/**
	 * 容器云异步任务
	 */
	public static final String CONTAINER_TASK_GROUP = "containerTaskGroup";
}
