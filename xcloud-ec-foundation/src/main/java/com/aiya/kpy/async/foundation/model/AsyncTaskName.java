package com.aiya.kpy.async.foundation.model;

import com.aiya.platform.foundation.enums.EnumsValue;

/**
 * 异步任务类型
 *
 * <AUTHOR>
 */
public enum AsyncTaskName implements EnumsValue {
    /**
     * 创建阿里磁盘
     */
    CREATE_DISK("CreateDisk"),

    DETACH_DISK("DetachDisk"),

    DELETE_DISK("DeleteDisk"),

    ATTACH_DISK("AttachDisk"),

    RENEW_DISK("RenewDisk"),

    REGULAR_DISK("RegularDisk"),

    ADD_DISK_TO_RECYCLE("AddDiskToRecycle"),

    /**
     * 磁盘扩容
     */
    CHANGE_DISK("ChangeDisk"),

    CREATE_IMAGE_FORM_SNAPSHOT("CreateImageFromSnapshot"),

    CREATE_IMAGE_FORM_DISK("CreateImageFromDisk"),

    DELETE_IMAGE("DeleteImage"),
    /**
     * 修改属性
     */
    MODIFY_INTANCE_PASSWORD("ModifyInstancePassowrd"),

    MODIFY_ECS_SPEC("modifyEcsSpec"),

    START_ECS_INSTANCE("startEcsInstance"),

    STOP_ECS_INSTANCE("stopEcsInstance"),

    FORCE_STOP_ECS_INSTANCE("forceStopEcsInstance"),

    REBOOT_ECS_INSTANCE("rebootEcsInstance"),

    RELEASE_ECS_FROM_RECYCLE_STATION("releaseEcsFromRecycleStation"),

    RELEASE_ECS_INSTANCE("releaseEcsInstance"),

    RELEASE_STOP_ECS_INSTANCE("releaseStopEcsInstance"),

    OUTSIDE_RELEASE_ECS_INSTANCE("OutsideReleaseEcsInstance"),

    RECYCLE_ECS_INSTANCE("recycleEcsInstance"),

    PAUSE_ECS_INSTANCE("pauseEcsInstance"),

    LOCK_ECS_INSTANCE("lockEcsInstance"),

    EXPIRE_ECS_INSTANCE("expireEcsInstance"),

    ECS_CHANGE_CHARGE_TYPE_TO_PRE("ecsChangeChargeTypeToPre"),
    
    /**
     * 外部锁定主机
     */
    OUTSIDE_LOCK_ECS_INSTANCE("outsideLockEcsInstance"),

    RESUME_ECS_INSTANCE("resumeEcsInstance"),
    /**
     * 外部恢复主机
     */
    OUTSIDE_RESUME_ECS_INSTANCE("OutsideResumeEcsInstance"),

    CREATE_ECS_TASK_NAME("createEcsTaskName"),

    /**
     * 云主机创建失败的释放
     */
    RELEASE_CREATE_FAILED_ECS("releaseCreateFailedEcs"),
    /**
     * 回滚变配失败的任务
     */
    ROLLBACK_MODIFY_SPEC_FAILED_ECS("rollbackModifySpecFailedEcs"),

    /**
     * 云主机重装系统
     */
    ECS_REINSTALL_SYSTEM("ecsReinstallSystem"),

    CREATE_SNAPSHOT_FROM_DISK("createSnapshotFromDisk"),

    CREATE_SNAPSHOT_FROM_INSTANCE("createSnapshotFromInstance"),

    CREATE_SNAPSHOT_FROM_LOCAL_DISK("createSnapshotFromLocalDisk"),

    DELETE_SNAPSHOT("deleteSnapshot"),

    DELETE_SNAPSHOT_FROM_LOCAL_DISK("deleteSnapshotFromLocalDisk"),

    ROLLBACK_SNAPSHOT("rollbackSnapshot"),
    /**
     * 创建VPC
     */
    CREATE_VPC("createVpc"),
    /**
     * 创建VPC子网
     */
    CREATE_SUBNET("createSubnet"),

    CREATE_VLAN_SUBNET("createVlanSubnet"),
    /**
     * 删除VPC子网
     */
    DELETE_SUBNET("deleteSubnet"),
    /**
     * 申请EIP
     */
    ALLOCATE_PUBLIC_IP("allocatePublicIp"),
    /**
     * 绑定EIP
     */
    ASSOCIATE_PUBLIC_IP("associatePublicIp"),
    /**
     * 解绑EIP
     */
    UNASSOCIATE_PUBLIC_IP("unassociatePublicIp"),
    /**
     * 变更弹性IP带宽
     */
    MODIFY_PUBLIC_IP_NETWORK_SPEC("modifyPublicIpNetworkSpec"),

    ADD_PUBLIC_IP_TO_RECYCLE("addPublicIpToRecycle"),

    /**
     * 弹性IP续费
     */
    RENEW_PUBLIC_IP("renewPublicIp"),
    /**
     * 释放EIP
     */
    RELEASE_PUBLIC_IP("releasePublicIp"),
    /**
     * 转正EIP
     */
    REGULAR_TRY_OUT_PUBLIC_IP("regularTryOutPublicIp"),
    /**
     * 禁用IP
     */
    DISABLE_IP("DisableIp"),
    DELETE_VPC("deleteVpc"),

    RENEW_ECS_INSTANCE("RenewEcsInstance"),
    /**
     * 云主机添加到回收站中
     */
    ADD_ECS_TO_RECYCLE("AddEcsToRecycle"),

    ECS_REGULAR_TRY_OUT("EcsRegularTryOut"),
    /**
     * mq通知产品结果的异步任务
     */
    MQ_NOTIFY_ORDCT_RESULT("MqNotifyOrdctResult"),
    /**
     * MQ通知产品资源变动的异步任务
     */
    MQ_NOTIFY_RESOURCE_CHANGE("MqNotifyResourceChange"),
    /**
     * MQ产品中心记录资源绑定关系
     */
    MQ_NOTIFY_PROD_BIND_RELATION_CHANGE("MqNotifyProdBindRelationChange"),
    /**
     * 公网IP欠费处理
     */
    PUBLIC_IP_ARREARAGE("PublicIpArrearage"),
    /**
     * 公网IP结清处理
     */
    PUBLIC_IP_SETTLE("PublicIpSettle"),
    /**
     * mq账单创建成功
     */
    MQ_BILL_CREATE_SUCCESS("MqBillCreateSuccess"),

    /**
     * 变更计费方式
     */
    MODIFY_PUBLIC_IP_CHARGE_TYPE("ModifyChargeType"),
    /**
     * 更换IP地址
     */
    REPLACE_PUBLIC_IP_ADDRESS("ReplacePublicIpAddress"),

    /**
     * 外部更换IP地址
     */
    OUTSIDE_REPLACE_PUBLIC_IP_ADDRESS("OutsideReplacePublicIpAddress"),

    /**
     * 创建流量包
     */
    CREATE_FLOW_PACKET("CreateFlowPacket"),
    /**
     * 续费流量包
     */
    RENEW_FLOW_PACKET("RenewFlowPacket"),
    /**
     * 添加IP到流量包
     */
    ADD_FLOW_PACKET_IP("AddFlowPacketIp"),
    /**
     * 流量包移除IP
     */
    REMOVE_FLOW_PACKET_IP("RemoveFlowPacketIp"),
    /**
     * 流量包加入回收站
     */
    ADD_FLOW_PACKET_TO_RECYCLE("AddFlowPacketToRecycle"),
    /**
     * 释放流量包
     */
    RELEASE_FLOW_PACKET("ReleaseFlowPacket"),
    /**
     * 销毁IP
     */
    DESTROY_PUBLIC_IP("DestroyPublicIp"),
    /**
     * 销毁硬盘
     */
    DESTROY_DISK("DestroyDisk"),
    /**
     * 超管释放主机及相关资源
     */
    SUPER_RELEASE_ECS("SuperReleaseEcs"),

    /**
     * 外部释放主机及相关资源
     */
    OUTSIDE_SUPER_RELEASE_ECS("OutsideSuperReleaseEcs"),
    /**
     * 创建防火墙
     */
    CREATE_FIREWALL("CreateFirewall"),
    /**
     * 删除防火墙
     */
    DELETE_FIREWALL("DeleteFirewall"),
    /**
     * 主机绑定防火墙
     */
    ASSOCIATE_FIREWALL("AssociateFirewall"),
    /**
     * 主机解绑防火墙
     */
    UNASSOCIATE_FIREWALL("UnassociateFirewall"),
    /**
     * 创建防火墙规则
     */
    CREATE_FIREWALL_RULE("CreateFirewallRule"),
    /**
     * 修改防火墙规则
     */
    MODIFY_FIREWALL_RULE("ModifyFirewallRule"),
    /**
     * 修改防火墙规则顺序
     */
    MODIFY_FIREWALL_RULE_SORT("ModifyFirewallRuleSort"),
    /**
     * 删除防火墙规则
     */
    DELETE_FIREWALL_RULE("DeleteFirewallRule"),
    /**
     * 上传对象
     */
    UPLOAD_OBJECT("UploadObject"),

    /**
     * 创建TK线路
     */
    CREATE_WIRING("CreateWiring"),

    /**
     * 重启TK线路
     */
    RESTART_WIRING("RestartWiring"),

    /**
     * 更换TK线路IP
     */
    REPLACE_WIRING_IP("ReplaceWiringIp"),

    /**
     * 超管释放TK线路及相关资源
     */
    SUPER_RELEASE_WIRING("SuperReleaseWiring"),

    /**
     * 释放TK线路
     */
    DESTROY_WIRING("DestroyWiring"),
    /**
     * 锁定TK线路
     */
    LOCK_WIRING("LockWiring"),

    /**
     * 恢复TK线路
     */
    RESUME_WIRING("ResumeWiring"),

    /**
     * 外部创建主机
     */
    OUTSIDE_CREATE_ECS_TASK_NAME("OutsideCreateEcsTaskName"),


    /**
     * 从回收站释放TK线路
     */
    RELEASE_WIRING_FROM_RECYCLE_STATION("ReleaseWiringFromRecycleStation"),


    /**
     * 外部释放回收站的ecs
     */
    OUTSIDE_RELEASE_ECS_FROM_RECYCLE_STATION("OutsideReleaseEcsFromRecycleStation"),

    /**
     * 解绑加速端口
     */
    UNMAPPING_ACCELERATE_PORT("UnmappingAcceleratePort"),

    /**
     * 创建物联网卡
     */
    CREATE_IOT_CARD("CreateIotCard"),

    /**
     * 创建容器实例
     */
    CREATE_CONTAINER_INSTANCE("CreateContainerInstance"),

    /**
     * 启动容器实例
     */
    START_CONTAINER_INSTANCE("StartContainerInstance"),

    /**
     * 停止容器实例
     */
    STOP_CONTAINER_INSTANCE("StopContainerInstance"),

    /**
     * 删除容器实例
     */
    DELETE_CONTAINER_INSTANCE("DeleteContainerInstance"),

    /**
     * 创建容器网卡
     */
    CREATE_CONTAINER_NIC("CreateContainerNic"),

    /**
     * 创建容器VLAN
     */
    CREATE_CONTAINER_VLAN("CreateContainerVlan"),

    /**
     * 创建容器SSH密钥
     */
    CREATE_CONTAINER_SSHKEY("CreateContainerSshkey"),
    ;


    private final String name;

    AsyncTaskName(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return name;
    }

    @Override
    public String getLabel() {
        return this.name();
    }
}
